// import 'dart:async';
//
// import 'package:bluetooth_thermal_printer/bluetooth_thermal_printer.dart';
// import 'package:esc_pos_utils/esc_pos_utils.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
//
// class PosPrintScreen extends StatefulWidget {
//   const PosPrintScreen({super.key});
//
//   @override
//   PosPrintScreenState createState() => PosPrintScreenState();
// }
//
// class PosPrintScreenState extends State<PosPrintScreen> {
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   bool connected = false;
//   List? availableBluetoothDevices = [];
//
//   Future<void> getBluetooth() async {
//     final List? bluetooths = await BluetoothThermalPrinter.getBluetooths;
//     if (kDebugMode) {
//       print("Print $bluetooths");
//     }
//     setState(() {
//       availableBluetoothDevices = bluetooths;
//     });
//   }
//
//   Future<void> setConnect(String mac) async {
//     final String? result = await BluetoothThermalPrinter.connect(mac);
//     if (kDebugMode) {
//       print("state conneected $result");
//     }
//     if (result == "true") {
//       setState(() {
//         connected = true;
//       });
//     }
//   }
//
//   Future<void> printTicket() async {
//     String? isConnected = await BluetoothThermalPrinter.connectionStatus;
//     if (isConnected == "true") {
//       List<int> bytes = await getTicket();
//       final result = await BluetoothThermalPrinter.writeBytes(bytes);
//       if (kDebugMode) {
//         print("Print $result");
//       }
//     } else {
//       //Hadnle Not Connected Senario
//     }
//   }
//
//   Future<void> printGraphics() async {
//     String? isConnected = await BluetoothThermalPrinter.connectionStatus;
//     if (isConnected == "true") {
//       List<int> bytes = await getGraphicsTicket();
//       final result = await BluetoothThermalPrinter.writeBytes(bytes);
//       if (kDebugMode) {
//         print("Print $result");
//       }
//     } else {
//       //Hadnle Not Connected Senario
//     }
//   }
//
//   Future<List<int>> getGraphicsTicket() async {
//     List<int> bytes = [];
//
//     CapabilityProfile profile = await CapabilityProfile.load();
//     final generator = Generator(PaperSize.mm80, profile);
//
//     // Print QR Code using native function
//     bytes += generator.qrcode('example.com');
//
//     bytes += generator.hr();
//
//     // Print Barcode using native function
//     final List<int> barData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 4];
//     bytes += generator.barcode(Barcode.upcA(barData));
//
//     bytes += generator.cut();
//
//     return bytes;
//   }
//
//   Future<List<int>> getTicket() async {
//     List<int> bytes = [];
//     CapabilityProfile profile = await CapabilityProfile.load();
//     final generator = Generator(PaperSize.mm80, profile);
//
//     bytes += generator.text("Demo Shop",
//         styles: const PosStyles(
//           align: PosAlign.center,
//           height: PosTextSize.size2,
//           width: PosTextSize.size2,
//         ),
//         linesAfter: 1);
//
//     bytes += generator.text(
//         "18th Main Road, 2nd Phase, J. P. Nagar, Bengaluru, Karnataka 560078",
//         styles: const PosStyles(align: PosAlign.center));
//     bytes += generator.text('Tel: +919591708470',
//         styles: const PosStyles(align: PosAlign.center));
//
//     bytes += generator.hr();
//     bytes += generator.row([
//       PosColumn(
//           text: 'No',
//           width: 1,
//           styles: const PosStyles(align: PosAlign.left, bold: true)),
//       PosColumn(
//           text: 'Item',
//           width: 5,
//           styles: const PosStyles(align: PosAlign.left, bold: true)),
//       PosColumn(
//           text: 'Price',
//           width: 2,
//           styles: const PosStyles(align: PosAlign.center, bold: true)),
//       PosColumn(
//           text: 'Qty',
//           width: 2,
//           styles: const PosStyles(align: PosAlign.center, bold: true)),
//       PosColumn(
//           text: 'Total',
//           width: 2,
//           styles: const PosStyles(align: PosAlign.right, bold: true)),
//     ]);
//
//     bytes += generator.row([
//       PosColumn(text: "1", width: 1),
//       PosColumn(
//           text: "Tea",
//           width: 5,
//           styles: const PosStyles(
//             align: PosAlign.left,
//           )),
//       PosColumn(
//           text: "10",
//           width: 2,
//           styles: const PosStyles(
//             align: PosAlign.center,
//           )),
//       PosColumn(text: "1", width: 2, styles: const PosStyles(align: PosAlign.center)),
//       PosColumn(text: "10", width: 2, styles: const PosStyles(align: PosAlign.right)),
//     ]);
//
//     bytes += generator.row([
//       PosColumn(text: "2", width: 1),
//       PosColumn(
//           text: "Sada Dosa",
//           width: 5,
//           styles: const PosStyles(
//             align: PosAlign.left,
//           )),
//       PosColumn(
//           text: "30",
//           width: 2,
//           styles: const PosStyles(
//             align: PosAlign.center,
//           )),
//       PosColumn(text: "1", width: 2, styles: const PosStyles(align: PosAlign.center)),
//       PosColumn(text: "30", width: 2, styles: const PosStyles(align: PosAlign.right)),
//     ]);
//
//     bytes += generator.row([
//       PosColumn(text: "3", width: 1),
//       PosColumn(
//           text: "Masala Dosa",
//           width: 5,
//           styles: const PosStyles(
//             align: PosAlign.left,
//           )),
//       PosColumn(
//           text: "50",
//           width: 2,
//           styles: const PosStyles(
//             align: PosAlign.center,
//           )),
//       PosColumn(text: "1", width: 2, styles: const PosStyles(align: PosAlign.center)),
//       PosColumn(text: "50", width: 2, styles: const PosStyles(align: PosAlign.right)),
//     ]);
//
//     bytes += generator.row([
//       PosColumn(text: "4", width: 1),
//       PosColumn(
//           text: "Rova Dosa",
//           width: 5,
//           styles: const PosStyles(
//             align: PosAlign.left,
//           )),
//       PosColumn(
//           text: "70",
//           width: 2,
//           styles: const PosStyles(
//             align: PosAlign.center,
//           )),
//       PosColumn(text: "1", width: 2, styles: const PosStyles(align: PosAlign.center)),
//       PosColumn(text: "70", width: 2, styles: const PosStyles(align: PosAlign.right)),
//     ]);
//
//     bytes += generator.hr();
//
//     bytes += generator.row([
//       PosColumn(
//           text: 'TOTAL',
//           width: 6,
//           styles: const PosStyles(
//             align: PosAlign.left,
//             height: PosTextSize.size4,
//             width: PosTextSize.size4,
//           )),
//       PosColumn(
//           text: "160",
//           width: 6,
//           styles: const PosStyles(
//             align: PosAlign.right,
//             height: PosTextSize.size4,
//             width: PosTextSize.size4,
//           )),
//     ]);
//
//     bytes += generator.hr(ch: '=', linesAfter: 1);
//
//     // ticket.feed(2);
//     bytes += generator.text('Thank you!',
//         styles: const PosStyles(align: PosAlign.center, bold: true));
//
//     bytes += generator.text("26-11-2020 15:22:45",
//         styles: const PosStyles(align: PosAlign.center), linesAfter: 1);
//
//     bytes += generator.text(
//         'Note: Goods once sold will not be taken back or exchanged.',
//         styles: const PosStyles(align: PosAlign.center, bold: false));
//     bytes += generator.cut();
//     return bytes;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Bluetooth Thermal Printer Demo'),
//       ),
//       body: Container(
//         padding: const EdgeInsets.all(20),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text("Search Paired Bluetooth"),
//             TextButton(
//               onPressed: () {
//                 getBluetooth();
//               },
//               child: const Text("Search"),
//             ),
//             SizedBox(
//               height: 200,
//               child: ListView.builder(
//                 itemCount: availableBluetoothDevices!.isNotEmpty
//                     ? availableBluetoothDevices!.length
//                     : 0,
//                 itemBuilder: (context, index) {
//                   return ListTile(
//                     onTap: () {
//                       String select = availableBluetoothDevices![index];
//                       List list = select.split("#");
//                       // String name = list[0];
//                       String mac = list[1];
//                       setConnect(mac);
//                     },
//                     title: Text('${availableBluetoothDevices![index]}'),
//                     subtitle: const Text("Click to connect"),
//                   );
//                 },
//               ),
//             ),
//             const SizedBox(
//               height: 30,
//             ),
//             TextButton(
//               onPressed: connected ? printGraphics : null,
//               child: const Text("Print"),
//             ),
//             TextButton(
//               onPressed: connected ? printTicket : null,
//               child: const Text("Print Ticket"),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }