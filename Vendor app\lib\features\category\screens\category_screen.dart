import 'package:sixam_mart_store/common/widgets/custom_card.dart';
import 'package:sixam_mart_store/features/category/controllers/category_controller.dart';
import 'package:sixam_mart_store/features/category/domain/models/category_model.dart';
import 'package:sixam_mart_store/features/category/screens/sub_category_product_screen.dart';
import 'package:sixam_mart_store/helper/route_helper.dart';
import 'package:sixam_mart_store/util/dimensions.dart';
import 'package:sixam_mart_store/util/styles.dart';
import 'package:sixam_mart_store/common/widgets/custom_app_bar_widget.dart';
import 'package:sixam_mart_store/common/widgets/custom_image_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CategoryScreen extends StatelessWidget {
  final CategoryModel? categoryModel;
  const CategoryScreen({super.key, required this.categoryModel});

  @override
  Widget build(BuildContext context) {
    bool isCategory = categoryModel == null;
    if(isCategory) {
      Get.find<CategoryController>().getCategoryList(null);
    }else {
      Get.find<CategoryController>().getSubCategoryList(categoryModel!.id, null);
    }

    return Scaffold(
      appBar: CustomAppBarWidget(title: isCategory ? 'categories'.tr : categoryModel!.name),
      body: GetBuilder<CategoryController>(builder: (categoryController) {
        List<CategoryModel>? categories;
        if(isCategory && categoryController.categoryList != null) {
          categories = [];
          categories.addAll(categoryController.categoryList!);
        }else if(!isCategory && categoryController.subCategoryList != null) {
          categories = [];
          categories.addAll(categoryController.subCategoryList!);
        }
        return categories != null ? categories.isNotEmpty ? RefreshIndicator(
          onRefresh: () async {
            if(isCategory) {
              await Get.find<CategoryController>().getCategoryList(null);
            }else {
              await Get.find<CategoryController>().getSubCategoryList(categoryModel!.id, null);
            }
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(Dimensions.paddingSizeDefault),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              return CustomCard(
                margin: const EdgeInsets.only(bottom: Dimensions.paddingSizeSmall),
                padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
                child: InkWell(
                  onTap: () {
                    if(isCategory) {
                      Get.toNamed(RouteHelper.getSubCategoriesRoute(categories![index]));
                    }else{
                      Get.to(() => SubCategoryProductScreen(subCategoryId: categories![index].id!, subCategoryName: categories[index].name ?? ''));
                    }
                  },
                  child: Row(children: [
                    isCategory ? ClipRRect(
                      borderRadius: BorderRadius.circular(Dimensions.radiusSmall),
                      child: CustomImageWidget(
                        image: '${categories![index].imageFullUrl}',
                        height: 65, width: 65, fit: BoxFit.cover,
                      ),
                    ) : const SizedBox(),
                    SizedBox(width: isCategory ? Dimensions.paddingSizeSmall : 0),

                    Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Text(categories?[index].name ?? '', style: robotoBold, maxLines: 1, overflow: TextOverflow.ellipsis),
                      const SizedBox(height: Dimensions.paddingSizeExtraSmall),

                      Text(
                        '${'id'.tr}: ${categories?[index].id}',
                        style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).disabledColor),
                      ),
                      /*SizedBox(height: isCategory ? Dimensions.paddingSizeExtraSmall : 0),

                      isCategory ? Text(
                        '${'sub_category'.tr}: ${categories?[index].id}',
                        style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).disabledColor),
                      ) : const SizedBox(),*/
                    ])),

                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 5),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(Dimensions.radiusSmall),
                      ),
                      child: Text('${categories?[index].productsCount} ${'items'.tr}', style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).hintColor)) ,
                    ),

                  ]),
                ),
              );
            },
          ),
        ) : Center(
          child: Text(isCategory ? 'no_category_found'.tr : 'no_subcategory_found'.tr),
        ) : const Center(child: CircularProgressIndicator());
      }),
    );
  }
}
