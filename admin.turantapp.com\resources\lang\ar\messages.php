<?php return array (
  'alert_store_out_from_campaign' => 'تريد متجرك من هذه الحملة؟',
  'alert_store_join_campaign' => 'تريد الانضمام إلى هذه الحملة؟',
  'about_us' => 'معلومات عنا',
  'about_us_image' => 'عنا صورة',
  'about_us_updated' => 'عننا تم تحديثه!',
  'about_the_campaign' => 'حول الحملة ...',
  'account_transaction' => 'معاملة الحساب',
  'account_transaction_removed' => 'معاملة الحساب تمت إزالتها!',
  'access_denied' => 'تم الرفض !',
  'access_token' => 'رمز وصول',
  'add' => 'يضيف',
  'add_new_item' => 'أضف أداة جديدة',
  'add_delivery_man' => 'أضف رجل التوصيل',
  'add_customer' => 'تسجيل عميل جديد',
  'update_discount' => 'خصم تحديث',
  'update_tax' => 'تحديث الضريبة',
  'addon' => 'اضافه',
  'add_to_cart' => 'أضف إلى السلة',
  'admin' => 'مسؤل',
  'admin_or_employee_signin' => 'تسجيل الدخول أو الموظف تسجيل الدخول',
  'admin_updated_successfully' => 'تم تحديث المسؤول بنجاح!',
  'admin_password_updated_successfully' => 'تم تحديث كلمة مرور المسؤول بنجاح!',
  'all' => 'الجميع',
  'all_stores' => 'جميع المتاجر',
  'all_categories' => 'جميع الفئات',
  'android' => 'ذكري المظهر',
  'application_placed_successfully' => 'تم وضع التطبيق بنجاح',
  'application_status_updated_successfully' => 'تم تحديث حالة التطبيق بنجاح',
  'approved' => 'موافقة',
  'approve' => 'يعتمد',
  'app_minimum_version' => 'تطبيق الحد الأدنى للتطبيق',
  'app_url' => 'APP URL',
  'app_settings' => 'إعدادات التطبيقات',
  'app_settings_updated' => 'تحديث إعدادات التطبيق',
  'app_store' => 'متجر التطبيقات',
  'as' => 'مثل',
  'at' => 'في',
  'attribute' => 'يصف',
  'attribute_choice_option_value_can_not_be_null' => 'لا يمكن أن تكون قيم خيار اختيار السمة فارغة!',
  'attributes' => 'صفات',
  'active_status_updated' => 'تم تحديث الحالة النشطة بنجاح',
  'minimum_delivery_time' => 'الحد الأدنى لوقت التسليم',
  'maximum_delivery_time' => 'أقصى وقت للتسليم',
  'approx_delivery_time' => 'تقريبا وقت التسليم',
  'availability' => 'توافر',
  'banner' => 'لافتة',
  'banner_status_updated' => 'تم تحديث حالة الشعار',
  'banner_featured_status_updated' => 'تم تحديث الحالة المميزة لافتة',
  'banner_added_successfully' => 'أضاف بانر بنجاح',
  'banner_updated_successfully' => 'تم تحديث لافتة بنجاح',
  'banner_deleted_successfully' => 'تم حذف لافتة بنجاح',
  'banners' => 'لافتات',
  'balance' => 'توازن',
  'balance_before_transaction' => 'التوازن قبل المعاملة',
  'bank' => 'بنك',
  'bank_info_updated_successfully' => 'معلومات البنك المحدث',
  'basic' => 'أساسي',
  'basic_campaign' => 'الحملة الأساسية',
  'basic_information' => 'معلومات اساسية',
  'between' => 'بين',
  'branches' => 'الفروع',
  'branch' => 'فرع',
  'business' => 'عمل',
  'bulk_import' => 'الاستيراد بالجملة',
  'bulk_export' => 'التصدير بالجملة',
  'button_links' => 'زر وروابط',
  'by_admin' => 'بواسطة المسؤول',
  'can_not_accept' => 'لا يمكن أن تقبل!',
  'cancel' => 'يلغي',
  'canceled' => 'ألغيت',
  'cash' => 'نقدي',
  'cash_in_hand' => 'نقد في اليد',
  'earning_balance' => 'كسب الرصيد',
  'card' => 'بطاقة',
  'card_holder_name' => 'إسم صاحب البطاقة',
  'card_holder_email' => 'حامل بطاقة البريد الإلكتروني',
  'card_number' => 'رقم البطاقة',
  'card_expire_month' => 'تنتهي صلاحية البطاقة',
  'card_expire_year' => 'تنتهي صلاحية البطاقة',
  'cart_empty_warning' => 'عربة التسوق فارغة',
  'category' => 'فئة',
  'categories' => 'فئات',
  'category_deleted' => 'حذف الفئة',
  'category_required' => 'اختيار القسم مطلوب!',
  'category_status_updated' => 'تحديث حالة الفئة!',
  'category_updated_successfully' => 'تحديث الفئة بنجاح!',
  'category_priority_updated successfully' => 'فئة أولوية تحديث بنجاح!',
  'category_imported_successfully' => ': العد - الفئات المستوردة بنجاح!',
  'choice_title' => 'عنوان الاختيار',
  'change_your_password' => 'غير كلمة المرور الخاصة بك',
  'click_on_the_map_select_your_defaul_location' => 'انقر على الخريطة حدد موقعك الافتراضي',
  'click_to_edit_this_item' => 'انقر لتحرير هذا العنصر',
  'collected_cash' => 'جمع النقود',
  'collect' => 'يجمع',
  'collected_cash_by_store' => 'تم جمع النقود بواسطة المتجر',
  'collect_cash_from_store' => 'جمع النقود من المتجر',
  'coordinates_out_of_zone' => 'ينسق خارج المنطقة!',
  'country' => 'دولة',
  'country_code_is_must' => 'رمز البلد لا بد منه',
  'like_for_BD_880' => 'مثل ل BD 880',
  'count' => 'عدد',
  'config' => 'تكوين',
  'config_data_updated' => 'تكوين بيانات تحديث!',
  'config_your_account' => 'تكوين حساب: طريقة الطريقة',
  'configuration_updated_successfully' => 'تم تحديث التهيئة بنجاح!',
  'confirmed' => 'مؤكد',
  'confirm_password' => 'تأكيد كلمة المرور',
  'confirm_this_order' => 'تأكد من هذا الطلب',
  'cancel_this_order' => 'إلغاء هذا الطلب',
  'coupon' => 'قسيمة',
  'coupons' => 'كوبونات',
  'coupon_added_successfully' => 'وأضاف القسيمة بنجاح!',
  'coupon_updated_successfully' => 'قسيمة تم تحديثها بنجاح!',
  'coupon_deleted_successfully' => 'تم حذف القسيمة بنجاح!',
  'coupon_status_updated' => 'تم تحديث حالة القسيمة',
  'coupon_usage_limit_over' => 'حد استخدام القسيمة قد انتهى لك!',
  'coupon_expire' => 'قسيمة انتهاء صلاحية',
  'cover' => 'غطاء',
  'currency_symbol_positon' => 'موقف رمز العملة',
  'currency_added_successfully' => 'تمت إضافة العملة بنجاح!',
  'currency_updated_successfully' => 'تم تحديث العملة بنجاح!',
  'currency_deleted_successfully' => 'تم حذف العملة بنجاح!',
  'current' => 'حاضِر',
  'customer_not_found' => 'لم يتم العثور على العميل!',
  'dashboard' => 'لوحة القيادة',
  'day_wise_report' => 'تقرير اليوم الحكيم',
  'item_wise_report' => 'البند الحكيم تقرير',
  'deleted' => 'تم الحذف',
  'delivered' => 'تم التوصيل',
  'deliverymen_earning_provide' => 'توزيع كسب DM',
  'deliveryman_preview' => 'معاينة رجل التسليم',
  'delivery_zone' => 'منطقة التسليم',
  'delivery_charge' => 'رسوم التوصيل',
  'delivery_address_updated' => 'عنوان التسليم تم تحديثه!',
  'deliveryman_earning_type' => 'كسب رجل التسليم',
  'deny' => 'ينكر',
  'designation' => 'تعيين',
  'dm_maximum_order' => 'أوامر مخصصة لأقصى مخصص لتوصيل رجل',
  'dm_maximum_order_hint' => 'عدد الطلبات القصوى التي يمكن أن يخدمها رجل التسليم في وقت واحد',
  'dm_maximum_order_exceed_warning' => 'أوامر الولادة الحد الأقصى للخدمة تتجاوز!',
  'dispatchManagement' => 'إدارة الإرسال',
  'duration' => 'مدة',
  'duplicate_data_on_column' => 'بيانات مكررة على: عمود الحقل',
  'dues' => 'مستحقات',
  'earned' => 'حصل',
  'email' => 'بريد إلكتروني',
  'email_is_ready_to_register' => 'البريد الإلكتروني جاهز للتسجيل',
  'employees' => 'موظفين',
  'employee' => 'موظف',
  'employee_added_successfully' => 'وأضاف الموظف بنجاح',
  'employee_deleted_successfully' => 'تم حذف الموظف بنجاح',
  'employee_updated_successfully' => 'تم تحديث الموظف بنجاح',
  'end' => 'نهاية',
  'enter_if_you_want_to_change' => 'أدخل إذا كنت تريد التغيير',
  'enter_new_email_address' => 'أدخل عنوان بريد إلكتروني جديد',
  'enter_new_password' => 'أدخل كلمة مرور جديدة',
  'enter_choice_values' => 'أدخل قيم الاختيار',
  'confirm_new_password' => 'قم بتأكيد كلمة المرور الجديدة الخاصة بك',
  'edit_store' => 'تحرير المتجر',
  'edited' => 'تحرير',
  'extra_discount' => 'خصم اضافي',
  'failed' => 'فشل',
  'faield_to_create_order_transaction' => 'فشل في إنشاء معاملة الطلب!',
  'failed_to_import_data' => 'فشل في استيراد البيانات!',
  'failed_to_place_order' => 'فشل في وضع الطلب! حاول مرة اخرى.',
  'faield_to_send_sms' => 'فشل في إرسال الرسائل القصيرة',
  'featured' => 'متميز',
  'filter' => 'منقي',
  'first_name' => 'الاسم الأول',
  'first_order' => 'الطلب الأول',
  'flutterwave' => 'Flutterwave',
  'item_list' => 'قائمة البند',
  'full_name' => 'الاسم الكامل',
  'product_already_added_in_cart' => 'العنصر تمت إضافته بالفعل في العربة',
  'product_has_been_added_in_cart' => 'تمت إضافة العنصر في عربة التسوق الخاصة بك!',
  'product_has_been_updated_in_cart' => 'تم تحديث عنصر العربة!',
  'product_imported_successfully' => ': العد - العناصر المستوردة بنجاح!',
  'free_delivery' => 'توصيل مجاني',
  'free_delivery_over' => 'توصيل مجاني',
  'freelancer' => 'مستقل',
  'from' => 'من',
  'form' => 'استمارة',
  'goto' => 'اذهب إلى',
  'handover' => 'سلم',
  'hash' => 'التجزئة',
  'high' => 'عالي',
  'item_wise' => 'البند الحكيم',
  'store_wise' => 'تخزين الحكمة',
  'information' => 'معلومة',
  'insufficient_balance' => 'توازن غير كاف!',
  'installments' => 'أقساط',
  'item_has_been_removed_from_cart' => 'تمت إزالة العنصر من العربة',
  'item_type' => 'نوع العنصر',
  'is' => 'يكون',
  'ios' => 'iOS',
  'join_us' => 'انضم إلينا',
  'last_name' => 'اسم العائلة',
  'landing_page' => 'الصفحة المقصودة',
  'landing_page_settings' => 'إعدادات الصفحة المقصودة',
  'landing_page_image_settings' => 'إعدادات صورة الصفحة المقصودة',
  'landing_page_image_updated' => 'تم تحديث صورة الصفحة المقصودة',
  'landing_page_text_updated' => 'تم تحديث نص الصفحة المقصودة بنجاح',
  'landing_page_links_updated' => 'تم تحديث روابط وأزرار الصفحة المقصودة بنجاح',
  'landing_page_speciality_updated' => 'تم تحديث تخصص الصفحة المقصودة بنجاح',
  'landing_page_testimonial_updated' => 'تم تحديث شهادات الصفحة المقصودة بنجاح',
  'left' => 'غادر',
  'list' => 'قائمة',
  'location' => 'موقع',
  'login' => 'تسجيل الدخول',
  'store_application' => 'تطبيق المتجر',
  'store_featured_status_updated' => 'تم تحديث الحالة المميزة للمتجر',
  'store_required_warning' => 'تأكد من أنك قد حددت متجرًا أولاً!',
  'store_temporarily_closed' => 'تخزين مغلق مؤقتا',
  'store_temporarily_closed_title' => 'تخزين مغلق مؤقتا',
  'store_opened' => 'فتح المتجر بنجاح',
  'store_settings_updated' => 'تخزين إعدادات تحديث!',
  'store_registration' => 'تسجيل المتجر',
  'deliveryman_registration' => 'التسليم التسليم',
  'deliveryman_application' => 'تطبيق التسليم',
  'category_required_warning' => 'تأكد من أنك قد حددت فئة أولاً!',
  'select_zone_for_map' => 'حدد منطقة تعتمد على موقع متجرك. بعد اختيار المنطقة ، يمكنك رؤية المنطقة على الخريطة',
  'show_hide_item_menu' => 'من خلال تعطيله ، لن يتمكن المتجر من إدارة (تحرير أو تحديث أو حذف أو تغيير الحالة) أي عنصر ولكن يمكنه عرض جميع العناصر.',
  'show_hide_reviews_menu' => 'عرض أو إخفاء قائمة المراجعات من لوحة المتجر.',
  'show_locations_on_map' => 'عرض المواقع على الخريطة',
  'system' => 'نظام',
  'store_lat_lng_warning' => 'حدد الموقع الدقيق لمتجرك من الخريطة (فقط انقر على الخريطة للحصول على موقع اختيار). يجب أن يكون الموقع داخل المنطقة المحددة.',
  'draw_your_zone_on_the_map' => '(ارسم منطقة منطقتك على الخريطة)',
  'customer_varification_toggle' => 'إذا كان هذا الحقل نشطًا ، فيجب على العميل التحقق من رقم هاتفه من خلال OTP',
  'order_varification_toggle' => 'إذا كان هذا الحقل نشطًا ، فيجب على العميل تقديم رمز OTP إلى رجل التسليم لتقديم طلب بنجاح',
  'order_confirmation_model' => 'نموذج تأكيد الطلب',
  'order_confirmation_model_hint' => 'سيعرض النموذج المختار الطلبات المعلقة أولاً ويمكنها تأكيدها',
  'store_data_updated' => 'تخزين البيانات التي تم تحديثها بنجاح!',
  'store_not_found' => 'لم يتم العثور على المتجر!',
  'interest_updated_successfully' => 'تم تحديث الاهتمام بنجاح!',
  'mail' => 'بريد',
  'maek_ready_for_delivery' => 'استعد للتسليم',
  'make_ready_for_handover' => 'استعد للتسليم',
  'maek_delivered' => 'اجعل تسليمها',
  'map_api_key' => 'MAP API مفتاح',
  'map_api_hint' => 'NB: يجب أن يكون لمفتاح العميل تمكين خريطة javaScript API ويمكنك تقييده مع HTTP Rejustere. يجب أن يكون لمفتاح الخادم مفتاح Place API ويمكنك تقييده باستخدام IP.',
  'map_api_hint_2' => 'يمكنك استخدام نفس API لكلا الحقل دون أي قيود.',
  'marketing' => 'تسويق',
  'maintenance_mode' => 'نمط الصيانة',
  'mercadopago' => 'Mercadopago',
  'me' => 'أنا',
  'merchant' => 'تاجر',
  'messages' => 'رسائل',
  'minimum' => 'الحد الأدنى',
  'medium' => 'واسطة',
  'minimum_shipping_charge' => 'الحد الأدنى رسوم التوصيل',
  'misconfiguration_or_data_missing' => 'سوء التكوين أو البيانات مفقودة!',
  'my' => 'لي',
  'my_bank_info' => 'معلوماتي المصرفية',
  'my_shop' => 'متجري',
  'new' => 'جديد',
  'new_password' => 'كلمة المرور الجديدة',
  'new_order' => 'أمر جديد!',
  'new_order_push_description' => 'طلب جديد',
  'news_letter_signup' => 'الاشتراك في النشرة الإخبارية',
  'news_letter_signup_text' => 'تلقي آخر الأخبار والتحديثات والعديد من الأخبار الأخرى كل أسبوع',
  'normal' => 'طبيعي',
  'optional' => 'خياري',
  'ongoingOrders' => 'أوامر مستمرة',
  'on_going' => 'جاري التنفيذ',
  'online' => 'متصل',
  'offline' => 'غير متصل على الانترنت',
  'weekly_off_day' => 'يوم الخوف الأسبوعي',
  'owner' => 'مالك',
  'order' => 'طلب',
  'order_already_assign_to_tis_deliveryman' => 'بالفعل تعيين إلى رجل التوصيل TIS',
  'order_push_title' => 'طلب',
  'order_ammount_wit_delivery_carge' => 'طلب ammount مع تسليم الافضل',
  'out_for_delivery' => 'خارج للتوصيل',
  'out_of_coverage' => 'خارج نطاق التغطية!',
  'our_features' => 'ميزاتنا',
  'our_mobile_applications' => 'تطبيقات الهاتف المحمول لدينا',
  'our_features_section_paragrap' => 'ملخص ميزاتنا',
  'pages' => 'الصفحات',
  'password' => 'كلمة المرور',
  'password_length_warning' => 'يجب أن يكون طول كلمة المرور: الطول.',
  'password_length_placeholder' => ': أحرف الطول المطلوبة',
  'password_not_matched' => 'كلمة المرور غير متطابقة',
  'paystack' => 'PayStack',
  'paystack_callback_warning' => 'NB: لا تنسى نسخ عنوان URL للاتصال على لوحة معلومات PayStack وتجاوزه على لوحة معلومات PayStack',
  'per_km_shipping_charge' => 'رسوم التوصيل لكل كيلومتر',
  'permission_denied' => 'تم رفض الإذن! يرجى الاتصال إلى المسؤول',
  'pending' => 'قيد الانتظار',
  'pending_take_away' => 'معلق (خذ)',
  'play_store' => 'متجر للعب',
  'please_select_store' => 'الرجاء تحديد متجر!',
  'read_more' => 'اقرأ أكثر',
  'select_category' => 'اختر الفئة',
  'select_date' => 'حدد تاريخ!',
  'select_store' => 'حدد متجر',
  'select_zone' => 'حدد المنطقة',
  'select_item' => 'حدد العنصر',
  'select_off_day' => 'اختر يوم',
  'please_choose_all_options' => 'الرجاء اختيار كل الخيارات',
  'please_fill_all_required_fields' => 'يرجى ملء جميع الحقول المطلوبة',
  'processing' => 'يعالج',
  'Proceed_for_cooking' => 'المضي قدما في المعالجة',
  'privacy_policy' => 'سياسة الخصوصية',
  'privacy_policy_updated' => 'تحديث سياسة الخصوصية!',
  'priority' => 'أولوية',
  'product' => 'منتج',
  'product_name_required' => 'اسم العنصر مطلوب!',
  'product_added_successfully' => 'تم إضافة البند بنجاح',
  'product_updated_successfully' => 'تم تحديث العنصر بنجاح',
  'product_deleted_successfully' => 'تم حذف العنصر بنجاح',
  'product_status_updated' => 'تم تحديث حالة العنصر',
  'discount_can_not_be_more_than_or_equal' => 'لا يمكن أن يكون الخصم أكثر أو يساوي السعر!',
  'provide' => 'يمد',
  'provided_dm_earnings_removed' => 'توفير أرباح التوصيل إزالتها!',
  'publicKey' => 'المفتاح العمومي',
  'push' => 'يدفع',
  'received_from' => 'مستلم من',
  'received_at' => 'وردت في',
  'received_by' => 'استلمت من قبل',
  'remember' => 'يتذكر',
  'remove_sub_categories_first' => 'إزالة الفئات الفرعية أولا!',
  'request' => 'طلب',
  'request_time' => 'طلب الوقت',
  'required' => 'مطلوب',
  'store_commission' => 'لجنة المتجر',
  'store_view' => 'عرض المتجر',
  'store_is_closed_at_order_time' => 'تم إغلاق المتجر في وقت الطلب',
  'store_owner_login_form' => 'نموذج تسجيل الدخول لمالك المتجر',
  'returned' => 'عاد',
  'reviews' => 'المراجعات',
  'review_visibility_updated' => 'مراجعة الرؤية المحدثة!',
  'right' => 'يمين',
  'role_added_successfully' => 'الدور المضافة بنجاح!',
  'role_deleted' => 'دور تم حذفه',
  'scheduled_at' => 'من المقرر في',
  'scheduled' => 'المقرر',
  'schedule_order_not_available' => 'جدول الجدول غير متوفر',
  'scheduled_date_is_store_offday' => 'آسف! تاريخ الطلب المجدول هو OFFDAY. الرجاء اختيار موعد آخر',
  'secret' => 'سر',
  'security_code' => 'رمز الحماية',
  'section' => 'قسم',
  'search_addons' => 'الإضافات البحث',
  'search_categories' => 'فئات البحث',
  'search_sub_categories' => 'البحث عن الفئات الفرعية',
  'search_here' => 'ابحث هنا',
  'searchingDM' => 'البحث عن رجل التسليم',
  'searching_for_deliverymen' => 'البحث عن رجل التسليم',
  'search_your_location_here' => 'ابحث في موقعك هنا',
  'signin' => 'تسجيل الدخول',
  'sign_in_as_employee' => 'تسجيل الدخول كموظف المتجر',
  'sign_in_as_owner' => 'تسجيل الدخول كمالك متجر',
  'speciality' => 'تخصص',
  'speciality_title' => 'عنوان التخصص',
  'speciality_img' => 'صورة التخصص',
  'social_login' => 'تسجيل الدخول الاجتماعي',
  'social' => 'الصفحات الاجتماعية',
  'Instagram' => 'Instagram',
  'Facebook' => 'فيسبوك',
  'Twitter' => 'تويتر',
  'LinkedIn' => 'LinkedIn',
  'Pinterest' => 'بينتيريست',
  'social_media_link' => 'عنوان URL لوسائل التواصل الاجتماعي',
  'social_media_inserted' => 'تم إدخال وسائل التواصل الاجتماعي',
  'social_media_exist' => 'وسائل التواصل الاجتماعي جاهزة',
  'social_media_updated' => 'تم تحديث وسائل التواصل الاجتماعي بنجاح',
  'social_media_deleted' => 'تم حذف وسائل التواصل الاجتماعي بنجاح',
  'social_media_required' => 'وسائل التواصل الاجتماعي مطلوب',
  'are_u_sure_want_to_delete' => 'هل أنت متأكد من حذف وسائل التواصل الاجتماعي هذه؟',
  'unsuspend_this_delivery_man' => 'قم بإلغاء توصيل رجل التسليم هذا',
  'suspend_this_delivery_man' => 'تعليق رجل التوصيل هذا',
  'suspended' => 'معلق',
  'testimonial' => 'شهادة',
  'feature' => 'ميزة',
  'feature_title' => 'عنوان الميزة',
  'feature_section_description' => 'وصف قسم الميزة',
  'feature_description' => 'ميزة الوصف',
  'feature_img' => 'ميزة الصورة',
  'landing_page_feature_updated' => 'ميزة الصفحة المقصودة محدثة',
  'landing_page_feature_feature_section_paragraph' => 'نص قسم الميزة',
  'testimonial_title' => 'عنوان شهادة',
  'paymob_supports_EGP_currency' => 'PayMob يدعم عملة EGP',
  'country_permission_denied_or_misconfiguration' => 'تم رفض الإذن بالبلد أو تهيئته',
  'paymob_accept' => 'Paymob قبول',
  'callback' => 'أتصل مرة أخرى',
  'transactions' => 'المعاملات',
  'accepted' => 'قبلت',
  'acceptedbyDM' => 'مقبولة من قبل رجل التسليم',
  'preparingInStores' => 'عنصر التحضير في المتاجر',
  'itemOnTheWay' => 'عنصر في الطريق',
  'refundRequest' => 'طلب ارجاع',
  'refunded' => 'رد',
  'refund_this_order' => 'استرداد هذا الترتيب',
  'senang' => 'سهل',
  'pay' => 'يدفع',
  'send' => 'يرسل',
  'settings' => 'إعدادات',
  'setup' => 'يثبت',
  'sl#' => 'يثبت',
  'sign_in' => 'تسجيل الدخول',
  'size' => 'مقاس',
  'sub_category' => 'تصنيف فرعي',
  'subscription_successful' => 'اشتراك ناجح',
  'subscription_exist' => 'البريد الإلكتروني الاشتراك موجود بالفعل',
  'subscribed_mail_list' => 'رسائل البريد الإلكتروني المشتركة',
  'table' => 'طاولة',
  'to' => 'ل',
  'total_earning' => 'مجموع الأرباح',
  'transaction' => 'عملية',
  'transaction_saved' => 'تم حفظ المعاملة بنجاح!',
  'transaction_updated' => 'تم تحديث المعاملة بنجاح!',
  'summary' => 'ملخص',
  'url' => 'عنوان URL',
  'upload' => 'رفع',
  'upload_zip_file' => 'تحميل ملف zip',
  'verification' => 'تَحَقّق',
  'veg' => 'طريق',
  'non_veg' => 'ليس الخضار',
  'walk_in_customer' => 'عميل المشي',
  'wallet' => 'محفظة',
  'want' => 'يريد',
  'wise' => 'حكيم',
  'withdraw' => 'ينسحب',
  'withdraw_able_balance' => 'توازن قابل للسحب',
  'withdraws' => 'ينسحب',
  'withdraw_request_placed_successfully' => 'تم وضع طلب السحب بنجاح!',
  'with' => 'مع',
  'your' => 'لك',
  'your_currency_is_not_supported' => 'عملتك غير مدعومة بواسطة: الطريقة.',
  'order_can_not_cancle_after_confirm' => 'لا يمكنك علاق الطلب بعد تأكيد!',
  'warning_add_bank_info' => 'الرجاء إضافة معلوماتك المصرفية أولاً!',
  'warning_missing_bank_info' => 'معلومات البنك مفقودة!',
  'tergat' => 'سقطت',
  'not_found' => 'غير معثور عليه!',
  'created_at' => 'أنشئت في',
  'role_form' => 'شكل الدور',
  'role_name' => 'اسم الدور',
  'module_permission' => 'إذن وحدة النظام',
  'roles_table' => 'جدول الدور',
  'modules' => 'الوحدات النمطية',
  'employee_form' => 'نموذج الموظف',
  'employee_image' => 'صورة الموظف',
  'Role' => 'دور',
  'employee_management' => 'إدارة شؤون الموظفين',
  'Employee' => 'موظف',
  'role_management' => 'إدارة الأدوار',
  'custom_role' => 'دور مخصص',
  'employee_handle' => 'معالج الموظف',
  'notification' => 'إشعار',
  'notification_status_updated' => 'تم تحديث حالة الإخطار!',
  'notification_deleted_successfully' => 'تم حذف الإخطار بنجاح!',
  'payment' => 'قسط',
  'payment_failed' => 'عملية الدفع فشلت',
  'payment_settings_updated' => 'تحديث إعدادات الدفع',
  'payment_reference_code_is_added' => 'تتم إضافة رمز مرجع الدفع!',
  'methods' => 'طُرق',
  'terms_and_condition' => 'البنود و الظروف',
  'terms_and_condition_updated' => 'الشروط والأحكام المحدثة!',
  'register' => 'يسجل',
  'customer' => 'عميل',
  'report_and_analytics' => 'التقرير والتحليلات',
  'deliveryman' => 'رجل التوصيل',
  'deliverymen' => 'رجال التوصيل',
  'deliveryman_type_updated' => 'نوع التسليم نوع تحديث!',
  'deliveryman_status_updated' => 'حالة التسليم المحدثة!',
  'deliveryman_added_successfully' => 'أضاف رجل التسليم بنجاح!',
  'deliveryman_updated_successfully' => 'تم تحديث رجل التسليم بنجاح!',
  'deliveryman_deleted_successfully' => 'تم حذف رجل التسليم بنجاح!',
  'earning' => 'كسب',
  'report' => 'تقرير',
  'reports' => 'التقارير',
  'web_and_app' => 'التطبيق على شبكة الإنترنت',
  'web_app_url' => 'عنوان URL تطبيق الويب (الواجهة الأمامية)',
  'mobile_app_section_heading' => 'عنوان تطبيق تطبيقات الهاتف المحمول',
  'mobile_app_section_text' => 'فقرة قسم تطبيقات الجوال',
  'welcome' => 'مرحباً',
  'welcome_message' => 'مرحبًا ، هنا يمكنك إدارة طلباتك حسب المنطقة.',
  'employee_welcome_message' => 'مرحبًا ، هنا يمكنك إدارة متاجرك.',
  'we_are_temporarily_unavailable_in_this_area' => 'نحن غير متاحين مؤقتًا في هذا المجال.',
  'none_of_your_sms_gateway_is_active_or_configured!' => 'لا يوجد أي من بوابة الرسائل القصيرة نشطة أو تم تكوينها!',
  'failed_to_send_sms' => 'فشل في إرسال الرسائل القصيرة! يرجى الاتصال بخدمة العملاء لدينا.',
  'otp_sent_successfull' => 'أرسل OTP بنجاح!',
  'products' => 'منتجات',
  'total' => 'المجموع',
  'sl' => 'يثبت',
  'orders' => 'طلبات',
  'order_id' => 'رقم التعريف الخاص بالطلب',
  'order_placed' => 'وضع النظام بنجاح',
  'order_place_notification' => 'البريد الإخطار للحصول على الطلب',
  'order_place_text' => 'لقد أرسلنا لك هذا البريد الإلكتروني ردًا على طلبك. ستتمكن من رؤية حالة طلبك بعد تسجيل الدخول إلى حسابك',
  'order_place_fail_text' => 'إذا كنت بحاجة إلى مساعدة ، أو لديك أي أسئلة أخرى ، فلا تتردد في إرسال بريد إلكتروني إلينا',
  'order_thanks' => 'شكرا على الطلب',
  'order_items' => 'أغراض',
  'order_unit_price' => 'سعر الوحدة',
  'order_qty' => 'كمية',
  'coupon_discount' => 'خصم القسيمة',
  'monthly' => 'شهريا',
  'overview' => 'ملخص',
  'in' => 'في',
  'items' => 'أغراض',
  'people' => 'الناس',
  'like' => 'يحب',
  'top' => 'قمة',
  'here' => 'هنا',
  'sign_out' => 'خروج',
  'profile' => 'حساب تعريفي',
  'profile_settings' => 'إعدادات الملف الشخصي',
  'profile_updated_successfully' => 'تم تحديث الملف الشخصي بنجاح!',
  'select' => 'يختار',
  'export' => 'يصدّر',
  'copy' => 'ينسخ',
  'copy_callback' => 'نسخ عنوان URL للاتصال',
  'print' => 'مطبعة',
  'excel' => 'Excel',
  'csv' => 'CSV',
  'pdf' => 'بي دي إف',
  'columns' => 'الأعمدة',
  'date' => 'تاريخ',
  'daily' => 'يوميًا',
  'status' => 'حالة',
  '#' => 'يثبت',
  'action' => 'فعل',
  'actions' => 'أجراءات',
  'download' => 'تحميل',
  'support' => 'يدعم',
  'download_our_apps' => 'قم بتنزيل تطبيقاتنا',
  'document_type' => 'نوع الوثيقة',
  'document_number' => 'رقم المستند',
  'option' => 'خيار',
  'options' => 'خيارات',
  'view' => 'منظر',
  'invoice' => 'فاتورة',
  'issuing_bank' => 'قضية بنك',
  'details' => 'تفاصيل',
  'paid' => 'مدفوع',
  'unpaid' => 'غير مدفوع الأجر',
  'note' => 'ملحوظة',
  'method' => 'طريقة',
  'reference' => 'مرجع',
  'code' => 'شفرة',
  'type' => 'يكتب',
  'contact' => 'اتصال',
  'info' => 'معلومة',
  'variation' => 'تفاوت',
  'addons' => 'addons',
  'addon_added_successfully' => 'أضاف Addon بنجاح!',
  'addon_updated_successfully' => 'addon تحديث بنجاح!',
  'addon_deleted_successfully' => 'تم حذف Addon بنجاح!',
  'addon_status_updated' => 'تم تحديث حالة Addon!',
  'addon_imported_successfully' => ': العد - الإضافات المستوردة بنجاح!',
  'attribute_added_successfully' => 'سمة تمت إضافتها بنجاح!',
  'attribute_updated_successfully' => 'سمة تحديث بنجاح!',
  'attribute_deleted_successfully' => 'تم حذف السمة بنجاح!',
  'attribute_status_updated' => 'تحديث حالة السمة!',
  'attribute_imported_successfully' => ': العد - السمات المستوردة بنجاح!',
  'price' => 'سعر',
  'vat' => 'ضريبة القيمة المضافة',
  'tax' => 'ضريبة',
  'vat/tax' => 'ضريبة القيمة المضافة',
  'cost' => 'يكلف',
  'subtotal' => 'مجموع',
  'discount' => 'تخفيض',
  'delivery' => 'توصيل',
  'fee' => 'مصاريف',
  'title' => 'عنوان',
  'item' => 'غرض',
  'image' => 'صورة',
  'image_uploaded_successfully' => 'تم تحميل الصور بنجاح',
  'image_deleted_successfully' => 'تم حذف الصور بنجاح',
  'choose' => 'يختار',
  'file' => 'ملف',
  'file_manager' => 'مدير الملفات',
  'submit' => '3. على',
  'close' => 'يغلق',
  'latitude' => 'خط العرض',
  'longitude' => 'خط الطول',
  'address' => 'عنوان',
  'invalid' => 'غير صالح',
  'invalid_otp' => 'OTP غير صالح!',
  'data' => 'بيانات',
  'edit' => 'يحرر',
  'name' => 'اسم',
  'save' => 'يحفظ',
  'salary_based' => 'راتب',
  'changes' => 'التغييرات',
  'change' => 'يتغير',
  'ratio' => 'نسبة',
  'any' => 'أي',
  'active' => 'نشيط',
  'disabled' => 'عاجز',
  'delete' => 'يمسح',
  'update' => 'تحديث',
  'update_option_is_disable_for_demo' => 'تم تعطيل خيار التحديث للتجريبي!',
  'main' => 'رئيسي',
  'sub' => 'الفرعية',
  'main_category' => 'الفئة الرئيسية',
  'percent' => 'نسبه مئويه',
  'amount' => 'كمية',
  'amount_to_be_paid' => 'المبلغ المستحق للدفع',
  'set_menu' => 'قائمة الضبط',
  'available' => 'متاح',
  'time' => 'وقت',
  'starts' => 'يبدأ',
  'ends' => 'ينتهي',
  'short' => 'قصير',
  'description' => 'وصف',
  'back' => 'خلف',
  'of' => 'ل',
  'variations' => 'الاختلافات',
  'reviewer' => 'المراجع',
  'review' => 'مراجعة',
  'review_submited_successfully' => 'مراجعة SUBSIDE بنجاح!',
  'coverage' => 'تغطية',
  'km' => 'كم',
  'conversation' => 'محادثة',
  'customers' => 'عملاء',
  'reply' => 'رد',
  'rating' => 'تقييم',
  'id' => 'بطاقة تعريف',
  'joined_at' => 'انضم في',
  'addresses' => 'عناوين',
  'limit' => 'حد',
  'for' => 'ل',
  'same' => 'نفس',
  'user' => 'مستخدم',
  'default' => 'تقصير',
  'first' => 'أولاً',
  'start' => 'يبدأ',
  'expire' => 'تنقضي',
  'min' => 'دقيقة',
  'max' => 'الأعلى',
  'purchase' => 'شراء',
  'search' => 'يبحث',
  'opening' => 'افتتاح',
  'closing' => 'إغلاق',
  'currency' => 'عملة',
  'on' => 'على',
  'off' => 'عن',
  'order_canceled_successfully' => 'تم إلغاء الطلب بنجاح!',
  'order_placed_successfully' => 'وضع الأمر بنجاح!',
  'order_updated_successfully' => 'أمر تم تحديثه بنجاح',
  'order_varification_code_not_matched' => 'لا يتطابق رمز التباين',
  'order_varification_code_is_required' => 'مطلوب رمز تباين الطلب',
  'order_data_not_found' => 'لا يتم العثور على بيانات الطلب',
  'order_payment_details' => 'بيانات الدفع',
  'phone' => 'هاتف',
  'phone_number_is_already_varified' => 'تم التحقق بالفعل من رقم الهاتف',
  'phone_number_varified_successfully' => 'تم التحقق من رقم الهاتف بنجاح!',
  'phone_number_and_otp_not_matched' => 'رقم الهاتف و OTP غير متطابقة!',
  'photo' => 'صورة',
  'value' => 'قيمة',
  'footer' => 'تذييل',
  'text' => 'نص',
  'text_copied' => 'نسخ النص',
  'logo' => 'شعار',
  'smtp' => 'SMTP',
  'mailer' => 'مراسل رقمي',
  'host' => 'يستضيف',
  'port' => 'ميناء',
  'pos' => 'نقاط البيع',
  'pos_system' => 'نظام نقاط البيع',
  'progress' => 'تقدم',
  'driver' => 'سائق',
  'username' => 'اسم المستخدم',
  'encryption' => 'التشفير',
  'gateway' => 'بوابة',
  'gallery' => 'صالة عرض',
  'gst' => 'ضريبة السلع والخدمات',
  'gst_can_not_be_empty' => 'لا يمكن أن يكون ضريبة السلع والخدمات فارغة!',
  'gst_status_warning' => 'إذا تم تمكين ضريبة السلع والخدمات ، فسيتم عرض رقم ضريبة السلع والخدمات في فاتورة',
  'cash_on_delivery' => 'الدفع عند الاستلام',
  'digital' => 'رقمي',
  'inactive' => 'غير نشط',
  'inactive_vendor_warning' => 'بائع غير نشط! يرجى الاتصال إلى المسؤول.',
  'configure' => 'تهيئة',
  'sslcommerz' => 'SSLCommerz',
  'razorpay' => 'أجر الشفرة',
  'store' => 'محل',
  'razorkey' => 'مفتاح الحلاقة',
  'razorsecret' => 'سر الشفرة',
  'paypal' => 'PayPal',
  'stripe' => 'شريط',
  'client' => 'عميل',
  'published' => 'نشرت',
  'key' => 'مفتاح',
  'paypalsecret' => 'سر PayPal',
  'api' => 'API',
  'firebase' => 'Firebase',
  'server' => 'الخادم',
  'service_not_available_in_this_area' => 'الخدمة غير متوفرة في هذا المجال',
  'confirmation' => 'تأكيد',
  'message' => 'رسالة',
  'messaging_service_id' => 'خدمة المراسلة سيد',
  'message_updated' => 'دفع إعدادات الإخطار تحديث!',
  'assign' => 'تعيين',
  'last' => 'آخر',
  'identity' => 'هوية',
  'identification_number' => 'رقم الهوية',
  'issuer' => 'المصدر',
  'number' => 'رقم',
  'passport' => 'جواز سفر',
  'driving' => 'القيادة',
  'license' => 'رخصة',
  'nid' => 'لا',
  'images' => 'الصور',
  'attachment' => 'مرفق',
  'show' => 'يعرض',
  'range' => 'يتراوح',
  'sold' => 'مُباع',
  'software_version' => 'إصدار البرنامج',
  'default_tax' => 'الضريبة الافتراضية',
  'default_admin_commission' => 'لجنة المشرف الافتراضية',
  'this' => 'هذا',
  'third_party_apis' => 'واجهات برمجة تطبيقات الطرف الثالث',
  'week' => 'أسبوع',
  'weekly' => 'أسبوعي',
  'column' => 'عمود',
  'vendor' => 'المتاجر',
  'zone' => 'منطقة',
  'zone_added_successfully' => 'أضافت المنطقة بنجاح!',
  'zone_status_updated' => 'حالة المنطقة محدثة!',
  'zone_updated_successfully' => 'تم تحديث المنطقة بنجاح!',
  'zone_deleted_successfully' => 'تم حذف المنطقة بنجاح!',
  'zone_id_required' => 'معرف المنطقة مطلوب!',
  'zone_wise' => 'منطقة حكيمة',
  'credentials' => 'أوراق اعتماد',
  'item_item' => 'عنصر العنصر',
  'item_name_required' => 'اسم العنصر مطلوب!',
  'item_type_is_required' => 'نوع العنصر مطلوب',
  'item_section' => 'إدارة العنصر',
  'item_status_updated' => 'تم تحديث حالة العنصر!',
  'item_not_found' => 'العنصر غير موجود',
  'charge' => 'تكلفة',
  'campaign' => 'حملة',
  'campaigns' => 'الحملات',
  'campaign_order' => 'أمر الحملة',
  'campaign_status_updated' => 'تم تحديث حالة الحملة!',
  'campaign_deleted_successfully' => 'تم حذف الحملة بنجاح!',
  'capmaign_participation_updated' => 'مشاركة الحملة تحديث!',
  'stores' => 'المتاجر',
  'store_added_to_campaign' => 'وأضاف المتجر إلى الحملة!',
  'store_imported_successfully' => ': العد - المتاجر المستوردة بنجاح!',
  'store_remove_from_campaign' => 'تم إزالة المتجر من الحملة!',
  'vendor_view' => 'عرض المتجر',
  'bank_info' => 'المعلومات المصرفية',
  'bank_name' => 'اسم البنك',
  'holder_name' => 'اسم حامل',
  'account_no' => 'حساب لا',
  'withdrawn' => 'انسحب',
  'comission' => 'comission',
  'disable' => 'إبطال',
  'enable' => 'يُمكَِن',
  'enable_earning' => 'تمكين الكسب',
  'disable_earning' => 'تعطيل الكسب',
  'panel' => 'لوحة',
  'no' => 'لا',
  'uncategorize' => 'غير مصنف',
  'unknown_tab' => 'علامة تبويب غير معروفة!',
  'removed' => 'إزالة',
  'replace' => 'يستبدل',
  'role_updated_successfully' => 'تم تحديث الدور بنجاح!',
  'role_deleted_successfully' => 'تم حذف الدور بنجاح!',
  'sine_in' => 'تسجيل الدخول',
  'take_away' => 'يبعد',
  'added_successfully' => 'اضيف بنجاح!',
  'updated_successfully' => 'تم التحديث بنجاح!',
  'successfully_removed' => 'تمت إزالته بنجاح!',
  'successfully_added' => 'أضيف بنجاح!',
  'successfully_updated' => 'تم التحديث بنجاح!',
  'successfully_updated_to_changes_restart_app' => 'تم التحديث بنجاح. لرؤية التغييرات في التطبيق إعادة تشغيل التطبيق.',
  'status_updated' => 'تم تحديث الحالة!',
  'settings_updated' => 'إعدادات تحديث!',
  'discount_cleared' => 'خصم مسح!',
  'seller_payment_approved' => 'تمت الموافقة على دفع البائع بنجاح',
  'seller_payment_denied' => 'تم رفض طلب دفع البائع بنجاح',
  'self_delivery_system' => 'نظام التسليم الذاتي',
  'not_fund' => 'غير معثور عليه!',
  'no_more_orders' => 'لا مزيد من الطلبات!',
  'please_assign_deliveryman_first' => 'الرجاء تعيين رجل التسليم أولا!',
  'add_your_paymen_ref_first' => 'أضف رمز مرجع الدفع الخاص بك أولاً!',
  'push_notification_faild' => 'فشل إشعار الدفع!',
  'you_can_not_cancel_a_completed_order' => 'لا يمكنك إلغاء طلب مكتمل أو تم شحنه بالفعل',
  'you_can_not_change_the_status_of_a_completed_order' => 'لا يمكنك تغيير حالة الطلب المكتمل',
  'you_can_not_cancel_a_order' => 'لا يمكنك إلغاء الطلب',
  'you_can_not_cancel_after_confirm' => 'لا يمكنك علبة بعد تأكيد!',
  'you_can_not_delivered_delivery_order' => 'لا يمكنك تسليم طلب!',
  'you_can_not_edit_this_zone_please_add_a_new_zone_to_edit' => 'آسف! لا يمكنك تحرير هذه المنطقة في العرض التوضيحي. الرجاء إضافة منطقة جديدة لتحريرها',
  'you_can_not_edit_this_module_please_add_a_new_module_to_edit' => 'آسف! لا يمكنك تحرير هذه الوحدة في العرض التوضيحي. الرجاء إضافة وحدة جديدة لتحريرها',
  'you_can_not_edit_this_store_please_add_a_new_store_to_edit' => 'آسف! لا يمكنك تحرير هذا المتجر في العرض التوضيحي. الرجاء إضافة متجر جديد لتحريره',
  'you_can_not_delete_this_store_please_add_a_new_store_to_delete' => 'آسف! لا يمكنك حذف هذا المتجر في العرض التوضيحي. الرجاء إضافة متجر جديد للحذف',
  'you_can_not_delete_this_zone_please_add_a_new_zone_to_delete' => 'آسف! لا يمكنك حذف هذه المنطقة في العرض التوضيحي. الرجاء إضافة منطقة جديدة للحذف',
  'veg_non_veg_disable_warning' => 'لا يمكنك تعطيل كل من الخضار وغير الخضار',
  'you_are_successfully_joined_to_the_campaign' => 'لقد انضمت بنجاح إلى الحملة',
  'you_are_successfully_removed_from_the_campaign' => 'تمت إزالتك بنجاح من الحملة',
  'you_have_uploaded_a_wrong_format_file' => 'لقد قمت بتحميل ملف تنسيق خاطئ ، يرجى تحميل الملف الصحيح.',
  'you_want_to_refund_this_order' => 'المبلغ القابل للاسترداد -: المبلغ. سيتم إضافة هذا المبلغ إلى محفظة العميل إذا كانت محفظة العميل تمكينًا وإلا ، فيرجى إدارة نقل المبلغ يدويًا.',
  'you_want_to_temporarily_close_this_store' => 'تريد أن تغلق هذا المتجر مؤقتًا؟',
  'you_want_to_open_this_store' => 'تريد تغيير الحالة النشطة لفتح هذا المتجر؟',
  'you_want_to_update_this_order_item' => 'تريد تحديث عنصر الطلب هذا؟',
  'you_want_to_remove_this_order_item' => 'تريد إزالة عنصر الطلب هذا؟',
  'you_want_to_edit_this_order' => 'تريد تحرير هذا الطلب؟',
  'you_want_to_cancel_editing' => 'تريد إلغاء تحرير هذا الطلب؟',
  'you_want_to_submit_all_changes_for_this_order' => 'تريد إرسال جميع التغييرات لهذا الطلب؟',
  'you_want_to_suspend_this_deliveryman' => 'تريد تعليق هذا التسليم؟',
  'you_want_to_unsuspend_this_deliveryman' => 'تريد إلغاء تعويض هذا التسليم؟',
  'you_want_to_approve_this_application' => 'تريد الموافقة على هذا التطبيق؟',
  'you_want_to_deny_this_application' => 'تريد إنكار هذا التطبيق؟',
  'are_you_sure_want_to_refund' => 'هل أنت متأكد من رغبتك في الاسترداد؟',
  'are_you_sure' => 'هل أنت متأكد؟',
  'you_can_not_refund_a_cod_order' => 'آسف نقدا على التسليم أو لا يمكن رد الطلب غير المدفوع!',
  'already_confirmed' => 'تم تأكيد هذا الطلب بالفعل!',
  'already_submitted' => 'أرسلت بالفعل!',
  'cannot_change_status_after_delivered' => 'آسف! لا يمكنك تغيير الحالة بعد تسليم الطلب.',
  'assign_delivery_mam_manually' => 'تعيين رجل التسليم يدويًا',
  'total_served_order' => 'إجمالي الطلبات الخدمية',
  'orders_served' => 'أوامر الخدم',
  'orders_delivered' => 'أوامر تسليمها',
  'order_confirmation_warning' => 'لا يُسمح لك بتأكيد هذا الطلب',
  'top_selling_items' => 'عناصر البيع الأعلى',
  'top_content_image' => 'أعلى صورة المحتوى',
  'popular_stores' => 'المتاجر الشعبية',
  'most_reviewed_items' => 'معظم العناصر التي تمت مراجعتها',
  'new_business' => 'عمل جديد',
  'new_campaign' => 'حملة جديدة',
  'new_category' => 'فئة جديدة',
  'new_coupon' => 'قسيمة جديدة',
  'new_item' => 'عنصر جديد',
  'new_stores' => 'متاجر جديدة',
  'new_delivery_man' => 'رجل توصيل جديد',
  'new_addon' => 'addon جديد',
  'new_banner' => 'شعار جديد',
  'new_attribute' => 'سمة جديدة',
  'new_notification' => 'إشعار جديد',
  'new_zone' => 'منطقة جديدة',
  'popular_items' => 'العناصر الشعبية',
  'total_sell' => 'إجمالي البيع',
  'total_order_amount' => 'إجمالي مبلغ الطلب',
  'total_uses' => 'يستخدم العد',
  'top_rated_items' => 'معظم العناصر تصنيف',
  'top_deliveryman' => 'أعلى رجل توصيل حسب عدد الطلبات',
  'top_customers' => 'كبار العملاء',
  'top_stores' => 'أعلى المتاجر حسب الطلب',
  'commission_earned' => 'كسب العمولة',
  'admin_commission' => 'لجنة الإدارة',
  'refund_requested' => 'استرداد طلب',
  'can_not_add_both_item_and_store_at_same_time' => 'لا يمكن إضافة كل من العنصر والتخزين في نفس الوقت!',
  'already_in_wishlist' => 'بالفعل في قائمة أمنياتك!',
  'can_not_disable_both_take_away_and_delivery' => 'لا يمكنك تعطيل كل من الأخذ والتسليم!',
  'if_sales_commission_disabled_system_default_will_be_applicable' => 'إذا تم تعطيل لجنة المبيعات هنا ، فسيتم تطبيق اللجنة الافتراضية للنظام.',
  'if_sales_tax_disabled_system_default_will_be_applicable' => 'إذا تم تعطيل ضريبة المبيعات هنا ، فسيتم تطبيق ضريبة النظام الافتراضية.',
  'dashboard_order_statistics' => 'إحصائيات طلب لوحة القيادة',
  'cooking' => 'يعالج',
  'ready_for_delivery' => 'مستعد لتوصيل',
  'item_on_the_way' => 'عنصر في الطريق',
  'yearly_statistics' => 'الإحصاءات السنوية',
  'commission_given' => 'العمولة المعطاة',
  'followup' => 'متابعة أنشطة متجرك',
  'Want_to_delete_this_role' => 'تريد حذف هذا الدور؟',
  'Want_to_delete_this_item' => 'تريد حذف هذا العنصر؟',
  'you_are_unassigned_from_a_order' => 'أنت غير معروف من أمر',
  'you_are_assigned_to_a_order' => 'تم تعيينك في طلب',
  'you_can_not_schedule_a_order_in_past' => 'لا يمكنك جدولة طلب في الوقت الماضي',
  'you_can_schedule_a_order_only_in_future_time' => 'يمكنك جدولة طلب فقط في وقت المستقبل',
  'You_can_not_change_status_after_picked_up_by_delivery_man' => 'لا يمكنك تغيير الحالة بعد التقاطها عن طريق التسليم!',
  'you_can_not_deliver_a_delivery_order' => 'لا يمكنك تقديم طلب لا يلتقط نفسه!',
  'you_need_to_order_at_least' => 'تحتاج إلى الطلب على الأقل: المبلغ.',
  'you_want_to_change_this_store_status' => 'تريد تغيير حالة حالة المتجر هذه؟',
  'you_want_to_block_this_customer' => 'تريد منع هذا العميل؟',
  'you_want_to_unblock_this_customer' => 'تريد إلغاء حظر هذا العميل؟',
  'you_want_to_hide_this_review_for_customer' => 'تريد إخفاء هذا الاستعراض للعملاء؟',
  'you_want_to_show_this_review_for_customer' => 'هل تريد مرئيًا لهذا المراجعة للعملاء؟',
  'your_account_is_blocked' => 'آسف! تم حظر حسابك. يرجى الاتصال بخدمة العملاء لدينا.',
  'your_account_has_been_blocked' => 'تم حظر حسابك! يرجى الاتصال بخدمة العملاء لدينا.',
  'your_account_has_been_suspended' => 'آسف! تم تعليق حسابك.',
  'your_first_name' => 'اسمك الأول',
  'your_last_name' => 'اسمك الاخير',
  'want_to_disable_earnings' => 'هل تريد تغيير نوع رجل التسليم إلى الراتب؟',
  'want_to_enable_earnings' => 'هل تريد تغيير نوع رجل التسليم إلى مستقل؟',
  'want_to_update_admin_password' => 'تريد تحديث كلمة مرور المسؤول؟',
  'want_to_update_password' => 'هل تريد تحديث كلمة مرور المستخدم؟',
  'proceed' => 'يتابع',
  'denied' => 'رفض',
  'sms' => 'رسالة قصيرة',
  'twilio_sms' => 'twilio',
  'sid' => 'سيد',
  'token' => 'رمز',
  'token_varified' => 'تم التحقق من الرمز المميز!',
  'token_not_found' => 'لم يتم العثور على الرمز المميز!',
  'otp_template' => 'قالب OTP',
  'nexmo_sms' => 'Nexmo',
  'api_key' => 'مفتاح API',
  'iframe_id' => 'معرف iframe',
  'integration_id' => 'معرف التكامل',
  'HMAC' => 'HMAC',
  'EGP_currency_is_required' => 'مطلوب عملة EGP',
  'api_secret' => 'سر API',
  '2factor_sms' => '2Factor',
  'msg91_sms' => 'MSG91',
  'template_id' => 'معرف القالب',
  'authkey' => 'مفتاح المصادقة',
  'module' => 'وحدة النظام',
  'management' => 'إدارة',
  'header_title_1' => 'عنوان الرأس 1',
  'header_title_2' => 'عنوان العنوان 2',
  'header_title_3' => 'عنوان الرأس 3',
  'about_title' => 'حول العنوان',
  'why_choose_us' => 'لماذا أخترتنا؟',
  'why_choose_us_title' => 'لماذا تختار العنوان لنا',
  'trusted_by_customer' => 'موثوق به من قبل العميل',
  'trusted_by_store' => 'صاحب متجر',
  'contact_us' => 'اتصل بنا',
  'footer_article' => 'مقال تذييل',
  'home' => 'بيت',
  'quick_links' => 'روابط سريعة',
  'browse_web' => 'تصفح الويب',
  'yes' => 'نعم',
  'saturday' => 'السبت',
  'sunday' => 'الأحد',
  'monday' => 'الاثنين',
  'tuesday' => 'يوم الثلاثاء',
  'wednesday' => 'الأربعاء',
  'thirsday' => 'يوم الخميس',
  'friday' => 'جمعة',
  'store_cancellation_toggle' => 'يمكن للمتجر إلغاء الطلب',
  'deliveryman_cancellation_toggle' => 'يمكن للتسليم إلغاء طلب',
  'time_zone' => 'وحدة زمنية',
  'time_format' => 'تنسيق الوقت',
  '12_hour' => '12H',
  '24_hour' => '24H',
  'language' => 'لغة',
  'show_earning_for_each_order' => 'إظهار كسب لكل طلب (تطبيق DeliveryMan)',
  'order_canceled_confirmation' => 'تريد إلغاء هذا الطلب؟',
  'first_name_is_required' => 'الإسم الأول مطلوب!',
  'select_a_zone' => 'الرجاء تحديد منطقة!',
  'select_a_module' => 'الرجاء تحديد وحدة نمطية!',
  'select_dm_type' => 'الرجاء تحديد نوع التسليم!',
  'credential_updated' => ': تحديث بيانات اعتماد الخدمة',
  'credentials_setup' => 'إعداد بيانات الاعتماد',
  'callback_uri' => 'رد الاتصال أوري',
  'copy_uri' => 'Copyiri',
  'store_client_id' => 'تخزين معرف العميل',
  'store_client_secret' => 'سر العميل سر',
  'google_api_setup_instructions' => 'جوجل API إعداد التعليمات',
  'go_to_the_credentials_page' => 'انتقل إلى صفحة بيانات الاعتماد',
  'click' => 'انقر',
  'create_credentials' => 'إنشاء بيانات الاعتماد',
  'auth_client_id' => 'معرف عميل Oauth',
  'select_the' => 'حدد',
  'web_application' => 'تطبيق الويب',
  'name_your_auth_client' => 'قم بتسمية عميل Oauth 2.0 الخاص بك',
  'add_uri' => 'أضف أوري',
  'authorized_redirect_uris' => 'uris إعادة التوجيه المعتمدة',
  'provide_the' => 'توفير',
  'from_below_and_click' => 'من الأسفل وانقر',
  'create' => 'يخلق',
  'client_id' => 'معرف العميل',
  'and' => 'و',
  'client_secret' => 'سر العميل',
  'past_in_the_input_field_below_and' => 'لصق في حقل الإدخال أدناه و',
  'facebook_api_set_instruction' => 'Facebook API إعداد التعليمات',
  'goto_the_facebook_developer_page' => 'انتقل إلى صفحة مطور Facebook',
  'click_here' => 'انقر هنا',
  'get_started' => 'البدء',
  'from_navbar' => 'من Navbar',
  'from_register_tab_press' => 'من علامة التبويب التسجيل اضغط',
  'continue' => 'يكمل',
  'if_needed' => 'إذا لزم الأمر',
  'provide_primary_email_and_press' => 'توفير البريد الإلكتروني الأساسي والضغط',
  'confirm_email' => '.تأكيد عنوان البريد الإلكتروني',
  'in_about_section_select' => 'في القسم حول SELECT',
  'other' => 'آخر',
  'and_press' => 'و اضغط',
  'complete_registration' => 'اكمل التسجيل',
  'create_app' => 'إنشاء التطبيق',
  'select_an_app_type_and_press' => 'حدد نوع التطبيق واضغط',
  'next' => 'التالي',
  'complete_the_details_form_and_press' => 'أكمل نموذج التفاصيل إضافة واضغط',
  'facebook_login' => 'تسجيل الدخول الى الفيسبوك',
  'press' => 'يضعط',
  'set_up' => 'يثبت',
  'web' => 'الويب',
  'site_url' => 'URL الموقع',
  'base_url_of_the_site' => 'عنوان URL الأساسي للموقع EX',
  'now_go_to' => 'اذهب الآن',
  'setting' => 'جلسة',
  'left_sidebar' => 'الشريط الجانبي الأيسر',
  'make_sure_to_check' => 'تأكد من التحقق',
  'client_auth_login' => 'العميل OAuth تسجيل الدخول',
  'must_on' => 'يجب على',
  'valid_auth_redirect_uris' => 'OAUTT صالح إعادة توجيه URIS',
  'save_changes' => 'حفظ التغييرات',
  'from_left_sidebar' => 'من الشريط الجانبي الأيسر',
  'fill_the_form_and_press' => 'املأ النموذج واضغط',
  'now_copy' => 'الآن ، نسخ',
  'twitter_api_set_up_instructions' => 'Twitter API إعداد التعليمات',
  'instruction_will_be_available_very_soon' => 'ستكون التعليمات متاحة قريبًا جدًا',
  'facebook' => 'فيسبوك',
  'google' => 'جوجل',
  'Customer_not_found_or_Account_has_been_suspended' => 'لم يتم العثور على العميل أو تم تعليق الحساب',
  'email_does_not_match' => 'البريد الإلكتروني لا يتطابق',
  'user_not_found' => 'لم يتم العثور على المستخدم',
  'email_already_exists' => 'البريد الالكتروني موجود بالفعل!',
  'want_to_login_your_admin_account' => 'تريد تسجيل الدخول إلى حساب المسؤول الخاص بك؟',
  'admin_login' => 'دخول المشرف',
  'your_email' => 'بريدك الالكتروني',
  'remember_me' => 'تذكرنى',
  'store_employee_login_form' => 'نموذج تسجيل الدخول إلى موظف المتجر',
  'add_new_store' => 'أضف متجر جديد',
  'edit_bank_info' => 'تحرير معلومات البنك',
  'please_only_input_png_or_jpg_type_file' => 'يرجى فقط إدخال ملف نوع PNG أو JPG',
  'file_size_too_big' => 'حجم الملف كبير جدا!',
  'credentials_does_not_match' => 'بيانات الاعتماد لا تتطابق.',
  'product_unavailable_warning' => 'عنصر واحد أو أكثر في عربة التسوق الخاصة بك غير متوفر. الرجاء مسح عربة التسوق الخاصة بك وحاول مرة أخرى.',
  'product_out_of_stock_warning' => ': عنصر خارج الأسهم!',
  'refund_request_placed_successfully' => 'طلب استرداد المبلغ بنجاح!',
  'you_can_not_request_for_refund_after_delivery' => 'لا يمكنك طلب استرداد الأموال قبل التسليم!',
  'invalid_password_warning' => 'كلمة المرور الخاصة بك غير صالحة. حاول مرة اخرى.',
  'veg_non_veg' => 'تبديل الخضار/غير الخضار',
  'store_self_registration' => 'تخزين التسجيل الذاتي',
  'dm_self_registration' => 'التسليم التسليم الذاتي',
  '404_warning_message' => 'آسف ، لا يمكن العثور على الصفحة التي تبحث عنها',
  '500_warning_message' => 'واجه الخادم خطأ داخليًا أو تكوينًا خاطئًا ولم يتمكن من إكمال طلبك.',
  'reload_page' => 'إعادة تحميل الصفحة',
  'error' => 'خطأ',
  'your_application_is_not_approved_yet' => 'لم يتم الموافقة على طلبك بعد!',
  'add_new_addon' => 'أضف ملحق جديد',
  'nav_menu' => 'قائمة NAV',
  'you_want_to_update_user_info' => 'تريد تحديث معلومات المستخدم؟',
  'schedule_order_disabled_warning' => 'تم تعطيل أمر الجدول بواسطة المسؤول',
  'vendor_pasword_updated_successfully' => 'تم تحديث كلمة مرور البائع بنجاح!',
  'languages' => 'اللغات',
  'reCaptcha' => 'recaptcha',
  'reCaptcha Setup' => 'الإعداد recaptcha',
  'Credentials SetUp' => 'إعداد بيانات الاعتماد',
  'Site Key' => 'مفتاح الموقع',
  'Secret Key' => 'المفتاح السري',
  'reCaptcha credential Set up Instructions' => 'RECAPTCHA CENSERINCES ENTRESS',
  'Go to the Credentials page' => 'انتقل إلى صفحة بيانات الاعتماد',
  'Click' => 'انقر',
  'Add a ' => 'أضف',
  'label' => 'ملصق',
  '(Ex: Test Label)' => '(على سبيل المثال: علامة الاختبار)',
  'Select reCAPTCHA v2 as ' => 'حدد recaptcha v2',
  'reCAPTCHA Type' => 'نوع recaptcha',
  'Sub type: I m not a robot Checkbox' => 'النوع الفرعي: أنا لست مربع اختيار روبوت',
  'Add' => 'يضيف',
  'domain' => 'اِختِصاص',
  '(For ex: demo.6amtech.com)' => '(لـ Ex: Demo.6amtech.com)',
  'Check in ' => 'تحقق في',
  'Accept the reCAPTCHA Terms of Service' => 'قبول شروط الخدمة recaptcha',
  'Press' => 'يضعط',
  'Submit' => 'يُقدِّم',
  'Copy' => 'ينسخ',
  'paste in the input filed below and' => 'لصق في المدخلات المودعة أدناه و',
  'Close' => 'يغلق',
  'Please check the recaptcha' => 'يرجى التحقق من recaptcha',
  'Enter recaptcha value' => 'أدخل قيمة recaptcha',
  'ReCAPTCHA Failed' => 'فشل ريكابتشا',
  'Users Overview' => 'نظرة عامة على المستخدمين',
  'This Month' => 'هذا الشهر',
  'Overall' => 'إجمالي',
  'Business Overview' => 'نظرة عامة على الأعمال',
  'Customer' => 'عميل',
  'Delivery Man' => 'رجل التوصيل',
  'Overall Statistics' => 'الإحصاءات الشاملة',
  'Today s Statistics' => 'إحصائيات اليوم',
  'Name is required!' => 'مطلوب اسم!',
  'Store is required when banner type is store wise' => 'يتطلب المتجر عندما يكون نوع اللافتة حكيماً',
  'Item is required when banner type is item wise' => 'العنصر مطلوب عندما يكون نوع اللافتة من الحكمة',
  'Role name is required!' => 'اسم الدور مطلوب!',
  'Role name already taken!' => 'اسم الدور الذي اتخذ بالفعل!',
  'Last name is required!' => 'إسم العائلة مطلوب!',
  'No Data found' => 'لاتوجد بيانات',
  'Bank Info View' => 'عرض معلومات البنك',
  'complete' => 'مكتمل',
  'Please select atleast one module' => 'يرجى تحديد وحدة نظام واحدة على الأقل',
  'Item Bulk Import' => 'عنصر استيراد الجزء الأكبر',
  'Import Item`s File' => 'استيراد ملف العنصر',
  'Download Format' => 'تنزيل تنسيق',
  'Add new category' => 'إضافة فئة جديدة',
  'Update category' => 'فئة التحديث',
  'Total Price' => 'السعر الكلي',
  'you want to sent notification to' => 'تريد إرسال إشعار إلى',
  'Variant' => 'البديل',
  'Variant Price' => 'سعر البديل',
  'quantity' => 'كمية',
  'Category Bulk Export' => 'فئة التصدير بالجملة',
  'Import Categories File' => 'ملف فئات استيراد',
  'qty' => 'كتي',
  'You have new order  Check Please.' => 'لديك طلب جديد ، تحقق من فضلك.',
  'Ok  let me check' => 'حسنا دعني افحص',
  'store deleted!' => 'تم حذف المتجر!',
  'Item deleted!' => 'تم حذف العنصر!',
  'reset' => 'إعادة ضبط',
  'Unauthorized' => 'غير مصرح.',
  'Export Stores' => 'متاجر التصدير',
  'star' => 'نجمة',
  'Order List' => 'لائحة الطلبات',
  'This Month s Statistics' => 'إحصائيات هذا الشهر',
  'Daily time schedule' => 'الجدول الزمني اليومي',
  'Create Schedule For ' => 'إنشاء جدول ل',
  'Start time' => 'وقت البدء',
  'End time' => 'وقت النهاية',
  'Store settings updated!' => 'تخزين إعدادات تحديث!',
  'Schedule added successfully' => 'الجدول الزمني المضافة بنجاح',
  'Schedule removed successfully' => 'تمت إزالته بنجاح',
  'Offday' => 'يوم خارج',
  'You want to remove this schedule' => 'تريد إزالة هذا الجدول؟',
  'End time must be after the start time' => 'يجب أن يكون وقت الانتهاء بعد وقت البدء',
  'Schedule not found' => 'جدول غير موجود أو تم حذفه بالفعل!',
  'schedule_overlapping_warning' => 'هذا الجدول يتداخل مع shcedule أخرى!',
  'Update addon' => 'تحديث addon',
  'Update campaign' => 'تحديث الحملة',
  'description_length_warning' => 'يجب ألا يكون الوصف أكبر من 1000 حرف',
  'Add new sub category' => 'أضف فئة فرعية جديدة',
  'Name and description in english is required' => 'مطلوب الاسم والوصف باللغة الإنجليزية',
  'Schedule order slot duration' => 'مدة فتحة الطلب',
  'Digit after decimal point' => 'رقم بعد نقطة عشرية',
  'minute' => 'دقيقة',
  'Update Attribute' => 'تحديث السمة',
  'add_language_warrning' => 'إذا قمت بإضافة لغة جديدة عليك إضافتها في التطبيق أيضًا',
  'paytm' => 'paytm',
  'paytm_merchant_key' => 'مفتاح تاجر Paytm',
  'paytm_merchant_mid' => 'Paytm Merchant Mid',
  'paytm_merchant_website' => 'موقع Paytm Merchant',
  'liqpay' => 'liqpay',
  'privateKey' => 'مفتاح سري',
  'base_url_by_region' => 'عنوان URL الأساسي حسب المنطقة',
  'profile_id' => 'ملف البطاقة الشخصية',
  'server_key' => 'مفتاح الخادم',
  'paytabs' => 'Paytabs',
  'bkash' => 'بكاش',
  'Cash on delivery order not available at this time' => 'نقدًا على أمر التسليم غير متوفر في هذا الوقت',
  'category_added_successfully' => 'وأضاف الفئة بنجاح!',
  'Fav Icon' => 'الرمز المفضل',
  'store_count' => 'عدد المتجر',
  'module_type' => 'نوع وحدة النظام',
  'module_added_successfully' => 'تمت إضافة وحدة النظام بنجاح!',
  'module_updated_successfully' => 'تحديث وحدة النظام بنجاح!',
  'module_deleted_successfully' => 'تم حذف وحدة النظام بنجاح!',
  'update_module' => 'تحديث وحدة النظام',
  'module_status_updated' => 'حالة وحدة النظام المحدثة!',
  'minutes' => 'دقائق',
  'hours' => 'ساعات',
  'days' => 'أيام',
  'unit' => 'وحدة',
  'units' => 'الوحدات',
  'unit_type' => 'نوع الوحدة',
  'new_unit' => 'وحدة جديدة',
  'unit_added_successfully' => 'تمت إضافة الوحدة بنجاح',
  'unit_updated_successfully' => 'تم تحديث الوحدة بنجاح',
  'unit_deleted_successfully' => 'تم حذف الوحدة بنجاح',
  'Update Unit' => 'تحديث الوحدة',
  'stock' => 'مخزون',
  'total_stock' => 'إجمالي الأسهم',
  'thumbnail' => 'ظفري',
  'remove' => 'يزيل',
  'minimum_processing_time' => 'الحد الأدنى لوقت المعالجة (دقائق)',
  'minimum_processing_time_warning' => 'ستبدأ فتحة التسليم بعد وقت الطلب بالإضافة إلى هذه المرة',
  'module_id_required' => 'معرف وحدة النظام مطلوب',
  'module_change_warning' => 'NB: لن تتمكن من تغيير وحدة النظام في المستقبل',
  'module_type_change_warning' => 'NB: لن تتمكن من تغيير نوع وحدة النظام في المستقبل',
  'parcel' => 'قطعة',
  'parcel_category' => 'فئة الطرود',
  'parcel_category_not_found' => 'لم يتم العثور على فئة الطرود',
  'update_parcel_category' => 'تحديث فئة الطرود',
  'Add_new_parcel_category' => 'أضف فئة الطرود الجديدة',
  'parcel_category_status_updated' => 'تحديث حالة فئة الطرود',
  'parcel_category_added_successfully' => 'تمت إضافة فئة الطرود بنجاح',
  'parcel_category_updated_successfully' => 'تحديث فئة الطرود بنجاح',
  'parcel_category_deleted_successfully' => 'تم حذف فئة الطرود بنجاح',
  'parcel_settings' => 'إعدادات الطرود',
  'parcel_settings_updated' => 'تحديث إعدادات الطرود',
  'receiver' => 'المتلقي',
  'current_page_only' => 'سيؤدي هذا إلى تصدير الصفحة الحالية فقط',
  'sender' => 'مرسل',
  'distance' => 'مسافة',
  'deliveryman_commission' => 'لجنة التوصيل',
  'prescription' => 'روشتة',
  'dear' => 'عزيزي',
  'thanks' => 'شكرا لك على الانضمام مع',
  'congratulations' => 'تهانينا',
  'account_created' => 'تم إنشاء الحساب',
  'buy_now' => 'اشتري الآن',
  'feedback' => 'إذا كنت بحاجة إلى أي مساعدة أو لديك ملاحظات أو اقتراحات حول موقعنا ، فيمكنك مراسلتنا عبر البريد الإلكتروني',
  'copyright' => 'كل نسخ محفوظة',
  'welcome_text' => 'نحن سعداء بوجودك معنا. استكشف المزيد من العناصر والمتاجر واستمتع',
  'no_data_found' => 'لاتوجد بيانات',
  'feature_section_image' => 'ميزة قسم قسم',
  'feature_section_title' => 'ميزة عنوان قسم',
  'mobile_app_section_image' => 'صورة قسم الهاتف المحمول',
  'change_header_bg' => 'خلفية الرأس',
  'change_footer_bg' => 'خلفية تذييل',
  'landing_page_bg' => 'خلفية الصفحة المقصودة',
  'background_updated' => 'لون الخلفية محدث',
  'background_color' => 'تغيير الخلفية',
  'reviewer_designation' => 'تعيين المراجع',
  'reviewer_name' => 'اسم المراجع',
  'thanks_for_the_order' => 'شكرا على الطلب',
  'Your_order_ID' => 'معرف طلبك',
  'Ordered_Items' => 'العناصر المطلوبة',
  'to_view_your_order_sign_into_your' => 'لعرض علامة طلبك على: company_name',
  'to_view_your_order_sign_into_your account_and_please_click_below_button' => 'لعرض علامة طلبك على حساب: company_name ، يرجى النقر أدناه على الزر',
  'Unit_price' => 'سعر الوحدة',
  'payment_details' => 'بيانات الدفع',
  'website' => 'موقع إلكتروني',
  'store_info' => 'معلومات المتجر',
  'web_app_landing_page_settings' => 'إعدادات الصفحة المقصودة تطبيق تطبيق الويب',
  'web_app' => 'التطبيق على شبكة الإنترنت',
  'web_app_settings_updated' => 'تحديث إعدادات الصفحة المقصودة تطبيق الويب',
  'icon' => 'أيقونة',
  'select_theme' => 'اختر نمطا',
  'Social Media' => 'وسائل التواصل الاجتماعي',
  'You have new order  Check Please' => 'لديك طلب جديد ، تحقق من فضلك.',
  'order_place' => 'مكان',
  'test_your_email_integration' => 'اختبر تكامل بريدك الإلكتروني',
  'inActive' => 'غير نشط',
  'smtp_mail_config' => 'تكوين البريد SMTP',
  'a_test_mail_will_be_sent_to_your_email' => 'سيتم إرسال بريد اختبار إلى بريدك الإلكتروني',
  'email_configuration_error' => 'خطأ تكوين البريد الإلكتروني',
  'email_configured_perfectly' => 'تم تكوين البريد الإلكتروني تمامًا',
  'email_status_is_not_active' => 'EmailStatus غير نشط',
  'invalid_email_address' => 'عنوان البريد الإلكتروني غير صالح',
  'send_mail' => 'ارسل بريد',
  'Active' => 'نشيط',
  'Inactive' => 'غير نشط',
  'Are you sure ' => 'هل أنت متأكد',
  'Yes' => 'نعم',
  'email_configured_perfectly!' => 'تم تكوين البريد الإلكتروني تمامًا!',
  'clean_database' => 'قاعدة بيانات نظيفة',
  'DB_clean' => 'ديسيبل نظيفة',
  'Clean database' => 'قاعدة بيانات نظيفة',
  'This_page_contains_sensitive_information.Make_sure_before_changing.' => 'تحتوي هذه الصفحة على معلومات حساسة. تأكد من التغيير.',
  'Clear' => 'واضح',
  'Sensitive_data! Make_sure_before_changing.' => 'بيانات حساسة! تأكد قبل التغيير.',
  'Cancelled' => 'ألغيت',
  'models' => 'عارضات ازياء',
  'preparingInRestaurants' => 'التحضير',
  'bukl_export' => 'تصدير Bukl',
  'environment_setup' => 'إعداد البيئة',
  'This_page_is_having_sensitive_data.Make_sure_before_changing.' => 'هذه الصفحة لديها بيانات حساسة. تأكد من التغيير.',
  'APP_NAME' => 'اسم التطبيق',
  'APP_DEBUG' => 'تفكيك التطبيق',
  'True' => 'حقيقي',
  'False' => 'خطأ شنيع',
  'APP_MODE' => 'وضع التطبيق',
  'Live' => 'يعيش',
  'Dev' => 'ديف',
  'APP_URL' => 'APP URL',
  'DB_CONNECTION' => 'اتصال DB',
  'DB_HOST' => 'مضيف DB',
  'DB_PORT' => 'منفذ DB',
  'DB_DATABASE' => 'قاعدة بيانات DB',
  'DB_USERNAME' => 'اسم المستخدم DB',
  'DB_PASSWORD' => 'كلمة مرور DB',
  'BUYER_USERNAME' => 'اسم المستخدم المشتري',
  'PURCHASE_CODE' => 'كود شراء',
  'Environment variables updated successfully!' => 'تم تحديث متغيرات البيئة بنجاح!',
  'Floor' => 'أرضية',
  'Road' => 'طريق',
  'House' => 'منزل',
  'This month' => 'هذا الشهر',
  'selected' => 'المحدد',
  'stock_report' => 'تقرير الاسهم',
  'Current stock' => 'الأسهم الحالية',
  'new_food' => 'الطعام الجديد',
  'All Zones' => 'جميع المناطق',
  'module_all_zone_hint' => 'إذا تم اختيار "All Zone" ، فسيكون المتجر متاحًا من جميع المنطقة وإلا فإن متاجر المنطقة هي فقط تنتمي إليها.',
  'Store can serve in' => 'يمكن أن يخدم المتجر في',
  'One Zone' => 'منطقة واحدة',
  'Delivery address' => 'عنوان التسليم',
  'add_new_customer' => 'أضف عميلًا جديدًا',
  'Ex_:<EMAIL>' => 'على سبيل المثال: <EMAIL>',
  'The Geolocation service failed' => 'فشلت خدمة تحديد الموقع الجغرافي',
  'Your browser doesn t support geolocation' => 'لا يدعم متصفحك تحديد الموقع الجغرافي',
  'Sorry  product out of stock' => 'آسف، والمنتجات من المخزون',
  'Sorry  you can not add multiple stores data in same cart' => 'آسف لا يمكنك إضافة بيانات متعددة في نفس العربة',
  'customer_added_successfully' => 'أضاف العميل بنجاح',
  'order ' => 'طلب',
  'contact_person_name' => 'اسم شخص الاتصال',
  'contact_person_number' => 'رقم شخص الاتصال',
  'with_country_code' => 'مع رمز البلد',
  'you_want_to_delete' => 'تريد حذف هذا الملف؟',
  'You want to remove this store' => 'تريد إزالة هذا المتجر',
  'Redirecting_to_the_payment_page' => 'إعادة توجيه صفحة الدفع',
  'items_on_the_way' => 'العناصر في الطريق',
  'role' => 'دور',
  'DESC' => 'DESC',
  'item_price' => 'سعر البند',
  'addon_cost' => 'تكلفة addon',
  'THANK YOU' => 'شكرًا لك',
  'show_hide_food_menu' => 'عرض قائمة إخفاء الطعام',
  'Create Schedule' => 'إنشاء جدول',
  'Campaign List' => 'قائمة الحملة',
  'edit_coupon' => 'تحرير القسيمة',
  'role_name_example' => 'المدير السابق',
  'Too Many Requests' => 'طلبات كثيرة جدا',
  'logout_warning_message' => 'هل ترغب بالخروج؟',
  'business_setup' => 'إعداد الأعمال',
  'all_image_delete_warning' => 'لا يمكنك حذف جميع الصور!',
  'item_image_removed_successfully' => 'تم إزالة صورة العنصر بنجاح!',
  'Page Expired' => 'انتهت صلاحية الصفحة',
  'customer_name' => 'اسم الزبون',
  'store is required when banner type is store wise' => 'يتطلب المتجر عندما يكون نوع اللافتة حكيماً',
  'Order Details' => 'تفاصيل الطلب',
  'Edit item' => 'تعديل عنصر',
  'table_unchecked_warning' => 'إلغاء تحديد ": الجدول" أولاً',
  'Paid by' => 'دفعت بواسطة',
  'password_mismatch' => 'كلمة المرور فعلت ، t تطابق!',
  'want_to_remove_store' => 'تريد إزالة هذا المتجر؟',
  'user_account_delete_warning' => 'لا يمكنك حذف حسابك دون إكمال طلباتك.',
  'user_account_wallet_delete_warning' => 'لا يمكنك حذف حسابك دون مسح مستحقاتك.',
  'Item Preview' => 'معاينة البند',
  'add_fund' => 'إضافة الصندوق',
  'you_want_to_add_fund' => 'تريد إضافة صندوق',
  'to_wallet' => 'إلى محفظة',
  'fund_added_successfully' => 'أضاف الصندوق بنجاح',
  'customer_settings' => 'إعدادات العملاء',
  'customer_wallet' => 'محفظة العميل',
  'customer_loyalty_point' => 'نقطة ولاء العملاء',
  'c_referrer_earning' => 'ج كسب المرجع',
  'refund_to_wallet' => 'استرداد المحفظة',
  'refund_to_wallet_hint' => 'عندما سيتم استرداد الطلب ، سيضيف مبلغ الطلب إلى محفظة العميل',
  'point_to_currency_exchange_rate' => '1: العملة تساوي مقدار نقاط الولاء؟',
  'item_purchase_point' => 'النسبة المئوية من نقطة الولاء على مبلغ الطلب',
  'item_purchase_point_hint' => 'في كل عملية شراء ، ستتم إضافة هذه المبلغ من المبلغ كنقطة ولاء على حسابه',
  'minimum_point_to_transfer' => 'الحد الأدنى من نقاط الولاء للانتقال إلى محفظة',
  'customer_referrer' => 'مرجع العميل',
  'referrer_to_currency' => 'أحد المرجعين يساوي كم: العملة؟',
  'stock_limit_list' => 'قائمة الحد من الأسهم',
  'dm_tips_status' => 'حالة نصائح DM',
  'customer_referrer_earning' => 'كسب مرجع العميل',
  'customer_settings_updated_successfully' => 'تحديث إعدادات العميل بنجاح',
  'loyalty_point_balance' => 'توازن نقطة الولاء',
  'debit' => 'دَين',
  'credit' => 'ائتمان',
  'transaction_type' => 'نوع المعاملة',
  'point_to_wallet' => 'تشير إلى محفظة',
  'select_customer' => 'حدد العميل',
  'order_status_updated' => 'تم تحديث حالة الطلب',
  'referrer' => 'المرجع',
  'loyalty_point' => 'نقطة الولاء',
  'add_fund_by_admin' => 'إضافة صندوق من قبل المشرف',
  'Rewared by company admin' => 'تكررها مسؤول الشركة',
  'loyalty_point_to_wallet' => 'نقطة الولاء للمحفظة',
  'earned_from_referrer' => 'كسب من المرجع',
  'add_fund_to_wallet' => 'إضافة صندوق إلى محفظة',
  'delivery_man_tips' => 'نصائح DM',
  'free_delivery_toggle_hint' => 'تمكين نظام التوصيل الذاتي إذا تم تعطيل التوصيل المجاني',
  'store_logo' => 'متجر الشعار',
  'store settings updated!' => 'تخزين إعدادات تحديث!',
  'Delivery Man Not Found' => 'لم يتم العثور على رجل التسليم',
  'export_stores' => 'متاجر التصدير',
  'dispatch' => 'إرسال',
  'food' => 'طعام',
  'link' => 'وصلة',
  'mail_config' => 'تكوين البريد',
  'Add Parcel' => 'أضف الطرود',
  'homeDelivery' => 'توصيل منزلي',
  'home Delivery' => 'توصيل منزلي',
  'ex' => 'السابق',
  'by' => 'بواسطة',
  'resturant_information' => 'معلومات مطعم',
  'store_information' => 'معلومات المتجر',
  'account' => 'حساب',
  'ex_search_module' => 'على سبيل المثال: وحدة البحث',
  'ex_:_search_module' => 'على سبيل المثال: وحدة البحث',
  'ex_:_Search_module' => 'على سبيل المثال: وحدة البحث',
  'ex_:_Search_Module' => 'على سبيل المثال: وحدة البحث',
  'STEP 1' => 'الخطوة 1',
  'Download Excel File' => 'قم بتنزيل ملف Excel',
  'STEP 2' => 'الخطوة 2',
  'Match Spread sheet data according to instruction' => 'تطابق بيانات ورقة انتشار وفقًا للتعليمات',
  'Validate data and complete import' => 'التحقق من صحة البيانات واستكمال الاستيراد',
  'STEP 3' => 'الخطوه 3',
  'Select Data Type' => 'حدد نوع البيانات',
  'Select Data Range and Export' => 'حدد نطاق البيانات والتصدير',
  'clear' => 'واضح',
  'add_new_category' => 'إضافة فئة جديدة',
  'SL' => 'يثبت',
  'ex_search_name' => 'على سبيل المثال: اسم البحث',
  'ex_:_search_name' => 'على سبيل المثال: اسم البحث',
  'ex_:_search_attribute_name' => 'على سبيل المثال: اسم السمة البحث',
  'ex_:_attribute_name' => 'على سبيل المثال: اسم السمة',
  'ex_:_categories' => 'على سبيل المثال: الفئات',
  'ex_:_addons_name' => 'على سبيل المثال: اسم الإضافات',
  'Select Data Range by Date or ID and Export' => 'حدد نطاق البيانات حسب التاريخ أو الهوية والتصدير',
  'ex_:_sub_categories' => 'على سبيل المثال: الفئات الفرعية',
  'item_details' => 'تفاصيل العنصر',
  'time_schedule' => 'الجدول الزمني',
  'ex_:_search_item_name' => 'على سبيل المثال: اسم عنصر البحث',
  'download_spreadsheet_template' => 'تنزيل قالب جدول البيانات',
  'template_with_existing_data' => 'قالب مع البيانات الموجودة',
  'template_without_data' => 'قالب بدون بيانات',
  'import_categories_file' => 'ملف فئات استيراد',
  'excellent' => 'ممتاز',
  'good' => 'جيد',
  'average' => 'متوسط',
  'below_average' => 'أقل من المتوسط',
  'poor' => 'فقير',
  'excellent_' => 'ممتاز',
  'short_description' => 'وصف قصير',
  'general_information' => 'معلومات عامة',
  'login_information' => 'معلومات تسجيل الدخول',
  'ex_:_search_delivery_man' => 'على سبيل المثال: Search Delivery Man',
  'show_data' => 'عرض البيانات',
  'download_options' => 'تنزيل الخيارات',
  'ex_:_search_by_' => 'على سبيل المثال: البحث بواسطة',
  'wallet_settings' => 'إعدادات المحفظة',
  'ex_:_search_' => 'على سبيل المثال: البحث',
  'ex_:_search_name_ _email_or_phone' => 'على سبيل المثال: اسم البحث أو البريد الإلكتروني أو الهاتف',
  'stock_report_table' => 'جدول تقرير الأسهم',
  'ex_:_search_email' => 'على سبيل المثال: Search Email',
  'ex_:_email' => 'على سبيل المثال: البريد الإلكتروني',
  'order_list' => 'لائحة الطلبات',
  'ex_:_search_ID' => 'على سبيل المثال: معرف البحث',
  'total_amount' => 'المبلغ الإجمالي',
  'genaral_information' => 'معلومات الجينرال',
  'business_information' => 'معلومات العمل',
  'business_setting' => 'إعداد الأعمال',
  'save_information' => 'حفظ المعلومات',
  'dm_tips_model_hint' => 'تلميح طراز نصائح DM',
  'social_media' => 'وسائل التواصل الاجتماعي',
  'mail_configuration_status' => 'حالة تكوين البريد',
  'change image' => 'تغيير الصورة',
  'note_:' => 'ملحوظة :',
  'ex_:_search_role_name' => 'على سبيل المثال: اسم دور البحث',
  'add_new_employee' => 'أضف موظفًا جديدًا',
  'account_information' => 'معلومات الحساب',
  'ex_:_search_sub_categories' => 'على سبيل المثال: البحث عن الفئات الفرعية',
  'ex_:_search' => 'على سبيل المثال: البحث',
  'home_delivery' => 'توصيل منزلي',
  'payment_method' => 'طريقة الدفع او السداد',
  'product_section' => 'قسم المنتج',
  'paid_amount_:' => 'المبلغ المدفوع :',
  'paid_by' => 'دفعت بواسطة',
  'billing_section' => 'قسم الفواتير',
  'delivery_options' => 'خيارات التسليم',
  'order_details' => 'تفاصيل الطلب',
  'payment_status' => 'حالة السداد',
  'order_setup' => 'ترتيب الإعداد',
  'customer_information' => 'معلومات العميل',
  'order_already_assign_to_this_deliveryman' => 'طلب بالفعل تعيين لهذا التسليم',
  'unassigned_orders' => 'الأوامر غير المعينة',
  'ongoing_orders' => 'أوامر مستمرة',
  '* This discount is applied on all the foods in your restaurant' => '* يتم تطبيق هذا الخصم على جميع الأطعمة في مطعمك',
  'purchase_conditions' => 'شروط الشراء',
  '' => '3',
  'no_discount_created_yet' => 'لم يتم إنشاء خصم بعد',
  'opening_time' => 'وقت مفتوح',
  'closing_time' => 'وقت الإغلاق',
  'order_transactions' => 'المعاملات أمر',
  'withdraw_transactions' => 'سحب المعاملات',
  'cash_transaction' => 'صفقة نقدية',
  'ex_:_search_order_id' => 'على سبيل المثال: معرف طلب البحث',
  'item_update' => 'تحديث العنصر',
  'export_items' => 'عناصر التصدير',
  'items_bulk_import' => 'عناصر الاستيراد بالجملة',
  'import' => 'يستورد',
  'category_list' => 'قائمة الفئات',
  'update_bank_info' => 'تحديث معلومات البنك',
  'add_bank_account' => 'أضف حساب مصرفي',
  'available_time_schedule' => 'الجدول الزمني المتاح',
  'account_info' => 'معلومات الحساب',
  'campaign_list' => 'قائمة الحملة',
  'delivery_man_preview' => 'معاينة رجل التسليم',
  'my_store_info' => 'معلومات متجري',
  'employee_list' => 'قائمة موظف',
  'search_role' => 'على سبيل المثال: البحث حسب اسم الدور',
  'edit_role' => 'تحرير الدور',
  'store_wallet' => 'محفظة المتجر',
  'proceed_for_processing' => 'المضي قدما في المعالجة',
  'make_delivered' => 'اجعل تسليمها',
  'order_status' => 'حالة الطلب',
  'ex_:_search_here' => 'على سبيل المثال: ابحث هنا ...',
  'all_parcel_module' => 'كل وحدة الطرود',
  'new_sub_category' => 'فئة فرعية جديدة',
  'FCM Project ID' => 'معرف مشروع FCM',
  'auth_domain' => 'المجال المصادق',
  'storage_bucket' => 'دلو التخزين',
  'messaging_sender_id' => 'معرف مرسل المراسلة',
  'app_id' => 'معرف التطبيق',
  'measurement_id' => 'معرف القياس',
  'Chat' => 'محادثة',
  'Conversations' => 'المحادثات',
  'conversations' => 'المحادثات',
  'delivery_man' => 'رجل التوصيل',
  'ex_:_new_attribute' => 'على سبيل المثال: اسم السمة الجديد ...',
  'updated_attribute' => 'سمة محدثة ...',
  'search_unit' => 'على سبيل المثال: اسم وحدة البحث',
  'unit_name' => 'على سبيل المثال: اسم الوحدة',
  'ex_:_order_id' => 'على سبيل المثال: معرف الطلب',
  'discount_amount' => 'مقدار الخصم',
  'ex_:_search_store_name' => 'على سبيل المثال: اسم متجر البحث',
  'collect_cash' => 'جمع النقد',
  'ex_:_search_sender_name' => 'على سبيل المثال: اسم مرسل البحث',
  'no_products_on_pos_search' => 'للحصول على نتيجة البحث المطلوبة أولاً ، حدد المتجر للبحث عن منتجات الحكيمة أو البحث يدويًا للعثور على منتجات ضمن هذا المتجر.',
  'payment_amount' => 'مبلغ الدفع',
  'ex_:confirm_password' => 'على سبيل المثال: تأكيد كلمة المرور',
  'ex_:_' => 'السابق :',
  'ex_:' => 'السابق :',
  'deliveryman_list' => 'قائمة التوصيل',
  'contact_info' => 'معلومات الاتصال',
  'total_orders' => 'إجمالي الطلبات',
  'active_status' => 'الحالة النشطة',
  'currently_assigned_orders' => 'أوامر تعيين حاليا',
  'delivery_fee' => 'رسوم التوصيل',
  'contact_information' => 'معلومات الاتصال',
  'active/inactive' => 'نشط نشط',
  'active/Inactive' => 'نشط نشط',
  10 => '100',
  'ex_100' => 'على سبيل المثال: 100',
  'ex_cash' => 'على سبيل المثال: نقد',
  'ex_collect_cash' => 'على سبيل المثال: جمع النقود',
  'Ex_:_Footer_Text' => 'على سبيل المثال: نص تذييل',
  'maintenance_txt' => '*عن طريق تشغيل وضع الصيانة ، سيتم تعطيل جميع تطبيقاتك وموقع العميل على الويب مؤقتًا. ستكون لوحة المسؤول ، صفحة المقصاة فقط ولوحة المتجر ، وظيفية.',
  'edit_info' => 'تحرير المعلومات',
  'total_stores' => 'مجموع المتاجر',
  'active_stores' => 'المتاجر النشطة',
  'inactive_stores' => 'المتاجر غير النشطة',
  'newly_joined_stores' => 'المتاجر المنضمة حديثا',
  'total_transactions' => 'إجمالي المعاملات',
  'total_store_withdraws' => 'يسحب مجموع المتجر',
  'ex_:_Search_Store' => 'على سبيل المثال: متجر البحث',
  'owner_information' => 'معلومات المالك',
  'discounts' => 'خصومات',
  'manage_item_setup' => 'إدارة العناصر',
  'Show_Reviews_In_Store_Panel' => 'عرض المراجعات في لوحة المتجر',
  'order_option' => 'خيار الطلب',
  'self_delivery' => 'التسليم الذاتي',
  'scheduled_order_hint' => 'إذا تم تشغيل هذه الحالة على العميل ، فسيكون العميل قادرًا على تقديم طلب مجدول لهذا المطعم.',
  'self_delivery_hint' => 'عندما يتم تمكين هذا الخيار ، تحتاج المطاعم إلى تقديم الطلبات بمفردها أو عن طريق رجل التسليم الخاص بها. ستحصل المطاعم أيضًا على خيار لإضافة رجل التسليم الخاص بهم من لوحة المطاعم.',
  'home_delivery_hint' => 'إذا كان هذا الخيار نشطًا ، فيمكن للعملاء تقديم طلبات لتسليم المنازل.',
  'take_away_hint' => 'من خلال تعطيل هذا الخيار ، يمكن للعملاء وضع أوامر الذاتي / الاستيلاء.',
  'pos_system_hint' => 'إذا تم تشغيل هذا الخيار على لوحة المطاعم ، فسيحصل على خيار Point of Sale (POS).',
  'include_pos_in_store_panel' => 'نقاط البيع في لوحة المتجر',
  'basic_campaigns' => 'الحملات الأساسية',
  'bannerd' => 'بانر',
  'scheduled_orders' => 'أوامر مجدولة',
  'accepted_orders' => 'أوامر مقبولة',
  'processing_orders' => 'أوامر المعالجة',
  'delivered_orders' => 'أوامر تسليم',
  'canceled_orders' => 'أوامر إلغاء',
  'payment_failed_orders' => 'أوامر فاشلة الدفع',
  'refunded_orders' => 'أوامر استرداد',
  'subscribed_emails' => 'رسائل البريد الإلكتروني المشتركة',
  'customer_chat' => 'دردشة العملاء',
  'sms_system_module' => 'وحدة نظام الرسائل القصيرة',
  'notification_settings' => 'إعدادات الإشعار',
  'limited_stock_item' => 'عنصر الأسهم المحدود',
  'limited_stock' => 'كمية محدودة',
  'module_management' => 'إدارة الوحدة النمطية',
  'system_module_setup' => 'إعداد وحدة النظام',
  'zone_setup' => 'إعداد المنطقة',
  'no_products_on_store_pos_search' => 'dfaklsdmf asdfalsdfskda fk asldfakld flasdfklads flasdfklsdfkladflad',
  'update_delivery_address' => 'تحديث عنوان التسليم',
  'clear_cart' => 'عربة واضحة',
  'place_order' => 'مكان الامر',
  'change_amount_:' => 'تغيير المبلغ:',
  'confirmed_orders' => 'أوامر مؤكدة',
  'cooking_orders' => 'أوامر الطهي',
  'items_list' => 'قائمة العناصر',
  'campaigns_list' => 'قائمة الحملات',
  'my_wallet' => 'محفظتى',
  'chat' => 'محادثة',
  'order_date' => 'تاريخ الطلب',
  'reviewer_table_list' => 'قائمة جدول المراجع',
  'Select Data Range by Date and Export' => 'حدد نطاق البيانات حسب التاريخ والتصدير',
  'category_id' => 'معرف الفئة',
  'category_name' => 'اسم التصنيف',
  'ex_:_search_by_category_name' => 'على سبيل المثال: البحث حسب اسم الفئة ...',
  'date_duration' => 'مدة التاريخ',
  'time_duration' => 'مدة الوقت',
  'join' => 'ينضم',
  'leave' => 'يترك',
  'edit_store_info' => 'تحرير معلومات المتجر',
  'edit_store_information' => 'تحرير معلومات المتجر',
  'Ratio (1:1)' => 'نسبة (1: 1)',
  'Employee image size max 2 MB' => 'حجم صورة الموظف بحد أقصى 2 ميغابايت',
  'Update Bank Info' => 'تحديث معلومات البنك',
  'Add Bank Info' => 'أضف معلومات البنك',
  'Identity Image' => 'صورة الهوية',
  '( Ratio 190x120 )' => '(نسبة 190x120)',
  'delivery_man_image' => 'صورة رجل التسليم',
  'deliverymen_list' => 'قائمة التوصيل',
  'active_orders' => 'أوامر نشطة',
  'delivery_man_details' => 'تفاصيل رجل التسليم',
  'reviewer_list' => 'قائمة المراجع',
  'ex_search_order_id' => 'معرف أمر البحث السابق',
  'ex_search_order_id ' => 'معرف أمر البحث السابق',
  'total_users' => 'إجمالي المستخدمين',
  'Add new zone' => 'أضف منطقة جديدة',
  'Instructions' => 'تعليمات',
  'Create zone by click on map and connect the dots together' => 'قم بإنشاء المنطقة بالنقر فوق الخريطة وتوصيل النقاط معًا',
  'Use this to drag map to find proper area' => 'استخدم هذا لسحب الخريطة للعثور على المساحة المناسبة',
  'Click this icon to start pin points in the map and connect them to draw a zone . Minimum 3  points required' => 'انقر فوق هذا الرمز لبدء نقاط الدبوس في الخريطة وتوصيلها لرسم منطقة. الحد الأدنى 3 نقاط مطلوبة',
  'Coordinates' => 'الإحداثيات',
  'Minimum delivery charge' => 'الحد الأدنى رسوم التوصيل',
  'Ex:' => 'السابق:',
  'Delivery charge per KM' => 'رسوم التوصيل لكل كيلومتر',
  'Want to change status for this zone  ' => 'تريد تغيير حالة هذه المنطقة',
  'Want to delete this zone  ' => 'تريد حذف هذه المنطقة',
  'Update Zone' => 'منطقة التحديث',
  'Add New System Module' => 'أضف وحدة نظام جديدة',
  'Module Name' => 'اسم وحدة',
  'Add new campaign' => 'أضف حملة جديدة',
  'Search Title ...' => 'عنوان البحث ...',
  'Campaign created successfully!' => 'أنشأت الحملة بنجاح!',
  'Campaign view' => 'عرض الحملة',
  'Item Info' => 'معلومات العنصر',
  'Item Image' => 'صورة البند',
  'Item Details' => 'تفاصيل العنصر',
  'Add Attribute' => 'اضف ميزة',
  'Want to delete this item  ' => 'تريد حذف هذا العنصر',
  'Want to delete this banner  ' => 'تريد حذف هذه اللافتة',
  'Update Banner' => 'تحديث لافتة',
  'Add new coupon' => 'أضف قسيمة جديدة',
  'Want to delete this coupon  ' => 'تريد حذف هذه القسيمة',
  'Select Restaurant' => 'حدد مطعم',
  'Notification list' => 'قائمة الإخطار',
  'Want to delete this notification  ' => 'تريد حذف هذا الإشعار',
  'No Image' => 'لا توجد صورة',
  'send_again' => 'أعد الإرسال',
  'all_zones' => 'جميع المناطق',
  'picked_up' => 'التقطت',
  'Previous order' => 'الامر السابق',
  'Next order' => 'الطلب التالي',
  'Change status to pending  ' => 'تغيير الحالة إلى معلق',
  'Change status to confirmed  ' => 'تغيير الحالة إلى تأكيد',
  'Change status to processing  ' => 'تغيير الحالة إلى المعالجة',
  'Change status to handover  ' => 'تغيير الحالة للتسليم',
  'Change status to out for delivery  ' => 'تغيير الحالة إلى الخارج للتسليم',
  'Change status to delivered (payment status will be paid if not) ' => 'تغيير حالة التسليم (سيتم دفع حالة الدفع إن لم يكن)',
  'Change status to canceled  ' => 'تغيير الحالة إلى إلغاء',
  'Want to delete this category' => 'تريد حذف هذه الفئة',
  'Select Main Category' => 'حدد الفئة الرئيسية',
  '1. Download the format file and fill it with proper data.' => '1. قم بتنزيل ملف التنسيق واملأه بالبيانات المناسبة.',
  '2. You can download the example file to understand how the data must be filled.' => '2. يمكنك تنزيل ملف المثال لفهم كيفية ملء البيانات.',
  '4. After uploading categories you need to edit them and set category`s images.' => '4. بعد تحميل الفئات ، تحتاج إلى تحريرها وتعيين صور الفئة.',
  '5. For parent category  position  will 0 and for sub category it will be 1.' => '5. بالنسبة لموقف الفئة الأصل ، سيكون 0 وللفئة الفرعية 1.',
  '6. By default status will be 1  please input the right ids.' => '6. سيكون الحالة الافتراضي 1 1 يرجى إدخال المعرفات الصحيحة.',
  '7. For a category parent_id will be empty  for sub category it will be the category id.' => '7. بالنسبة للفئة ، سيكون معرف الوالدين فارغًا للفئة الفرعية ، وسيكون معرف الفئة.',
  '8. For a sub category module id will it`s parents module id.' => '8. بالنسبة لمعرف وحدة الفئة الفرعية ، هل سيقوم معرف وحدة الوالدين.',
  'Download Spreadsheet Template' => 'تنزيل قالب جدول البيانات',
  'Template with Existing Data' => 'قالب مع البيانات الموجودة',
  'Template without Data' => 'قالب بدون بيانات',
  'Choose File' => 'اختر ملف',
  'Addon Bulk Export' => 'Addon Bulk Export',
  'AddOn Bulk Import' => 'addon استيراد السائبة',
  'Item List' => 'قائمة البند',
  'Review List' => 'قائمة المراجعة',
  '4. You can get store id  module id and unit id from their list  please input the right ids.' => '4. يمكنك الحصول على معرف وحدة معرف المتجر ومعرف الوحدة من قائمتهم ، يرجى إدخال المعرفات الصحيحة.',
  '5. For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59' => '5. بالنسبة للتجارة الإلكترونية ، سيكون بدء تشغيل الوقت والنهاية 00:00:00 و 23:59:59',
  '6. You can upload your product images in product folder from gallery  and copy image`s path.' => '6. يمكنك تحميل صور المنتج الخاصة بك في مجلد المنتج من المعرض ونسخ مسار Image.',
  'Food Bulk Export' => 'تصدير الغذاء بالجملة',
  'Store List' => 'قائمة المتجر',
  'Employee Add' => 'إضافة الموظف',
  'Employee Edit' => 'تحرير الموظف',
  'order_refund' => 'ترتيب الاسترداد',
  'Customer Details' => 'تفاصيل العميل',
  'Subscribed Emails' => 'رسائل البريد الإلكتروني المشتركة',
  'Customer List' => 'قائمة العملاء',
  'Search by name...' => 'البحث عن طريق الإسم...',
  'Want to remove this deliveryman  ' => 'تريد إزالة هذا التسليم',
  'Delivery Man Preview' => 'معاينة رجل التسليم',
  'ownerFirstName' => 'المالك',
  'ownerLastName' => 'مالك',
  'storeName' => 'هدف الاضطرابات',
  'delivery_time' => 'موعد التسليم',
  'zone_id' => 'معرف المنطقة',
  'module_id' => 'معرف الوحدة النمطية',
  'Withdraw request process' => 'سحب عملية الطلب',
  'reques' => 'ملحوظة',
  'Store Bulk Import' => 'تخزين الاستيراد بالجملة',
  'submit.Make sure the phone numbers and email addresses are unique' => '3. بمجرد تنزيل وملء تحميل ملف التنسيق I',
  '4. After uploading stores you need to edit them and set stores`s logo and cover.' => '4. بعد تحميل المتاجر ، تحتاج إلى تحريرها وتعيين شعار وغطاء المتاجر.',
  '5. You can get module id and  zone id from their list  please input the right ids.' => '5. يمكنك الحصول على معرف الوحدة النمطية ومعرف المنطقة من قائمتهم ، يرجى إدخال المعرفات الصحيحة.',
  '6. For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.' => '6. لوقت التسليم ، يكون التنسيق من النوع على سبيل المثال: 30-40 دقيقة. كما يمكنك استخدام أيام أو ساعات كنوع. يرجى توخي الحذر بشأن هذا التنسيق أو ترك هذا الحقل فارغًا.',
  '7. You can upload your store images in store folder from gallery  and copy image`s path.' => '7. يمكنك تحميل صور المتجر في مجلد المتجر من المعرض ونسخ مسار Image.',
  '8. Default password for store is 12345678.' => '8. كلمة المرور الافتراضية للمتجر هي 12345678.',
  'Import Stores File' => 'استيراد متاجر ملف',
  'Withdraw Request' => 'طلب سحب',
  'Withdraw information View' => 'سحب معلومات المعلومات',
  'Search By Referance' => 'ابحث عن طريق المراجع',
  'Employee List' => 'قائمة موظف',
  'Search by Order ID' => 'البحث حسب معرف الطلب',
  'Search by name or email..' => 'ابحث بالاسم أو البريد الإلكتروني ..',
  'Add new delivery-man' => 'إضافة تسليم جديد رجل',
  'Update delivery-man' => 'تحديث تسليم رجل',
  'Your browser doesn`t support geolocation' => 'متصفحك لا يدعم تحديد الموقع الجغرافي',
  'Ex :' => 'السابق :',
  'Contact Number' => 'رقم الاتصال',
  '* pin the address in the map to calculate delivery fee' => '* قم بتثبيت العنوان في الخريطة لحساب رسوم التسليم',
  'Delivery fee' => 'رسوم التوصيل',
  'Update' => 'تحديث',
  'Delivery Infomation' => 'تسليم المعلومات',
  'Home Delivery' => 'توصيل منزلي',
  'Name' => 'اسم',
  'Payment Method' => 'طريقة الدفع او السداد',
  'Cash On Delivery' => 'الدفع عند الاستلام',
  'Wallet' => 'محفظة',
  'Clear Cart' => 'عربة واضحة',
  'no_customer_selected' => 'لم يتم اختيار عميل',
  'please_select_a_valid_delivery_location_on_the_map' => 'يرجى تحديد موقع تسليم صالح على الخريطة',
  'insufficient_wallet_balance' => 'توازن محفظة غير كاف',
  'Add new customer' => 'أضف عميلًا جديدًا',
  'Billing Section' => 'قسم الفواتير',
  'Add New Customer' => 'أضف عميلًا جديدًا',
  'Total' => 'المجموع',
  'Delivery Information' => 'معلومات التوصيل',
  'Ex: +3264124565' => 'على سبيل المثال: +3264124565',
  'Ex: 4th' => 'على سبيل المثال: 4th',
  'Ex: 45/C' => 'على سبيل المثال: 45/C',
  'Ex: 1A' => 'على سبيل المثال: 1A',
  'Ex: address' => 'على سبيل المثال: العنوان',
  'Sorry  the minimum value was reached' => 'آسف تم الوصول إلى الحد الأدنى للقيمة',
  'Sorry  stock limit exceeded.' => 'آسف الحد الأقصى تجاوز.',
  'Please choose all the options' => 'الرجاء اختيار كل الخيارات',
  'Cash' => 'نقدي',
  'Card' => 'بطاقة',
  'Paid Amount' => 'المبلغ المدفوع',
  'Change Amount' => 'تغيير المبلغ',
  'All Items' => 'جميع المواد',
  'Active Items' => 'عناصر نشطة',
  'Inactive Items' => 'عناصر غير نشطة',
  'food_campaign' => 'حملة الغذاء',
  'Search by title' => 'البحث عن طريق العنوان',
  'Item Campaigns' => 'حملات البند',
  'Select Store' => 'حدد متجر',
  'Item wise report table' => 'جدول التقرير الحكيم للبند',
  'If this option is on  customers will get free delivery' => 'إذا كان هذا الخيار على العملاء سيحصل على توصيل مجاني',
  'total_withdrawal_amount' => 'إجمالي مبلغ الانسحاب',
  'confirm_order' => 'أكد الطلب',
  'Update Item' => 'تحديث العنصر',
  'item_info' => 'معلومات العنصر',
  'item_image' => 'صورة البند',
  'ex_:_search_sub_category' => 'على سبيل المثال: فئة البحث الفرعية',
  'search_by_title' => 'البحث عن طريق العنوان',
  'ex_:_Search_Store_Name' => 'على سبيل المثال: اسم متجر البحث',
  'store_type' => 'نوع المتجر',
  '* This discount is applied on all the items in your store' => '* يتم تطبيق هذا الخصم على جميع العناصر في متجرك',
  'include_POS_in_store_panel' => 'قم بتضمين POS في لوحة المتجر',
  'collect_from' => 'اجمع من',
  'Ex_:_Card' => 'على سبيل المثال: بطاقة',
  'Ex_:_1000' => 'على سبيل المثال: 1000',
  'history' => 'تاريخ',
  'Search By Referance  or Name' => 'ابحث عن طريق المراجع أو الاسم',
  'Ex : 5+ Characters' => 'على سبيل المثال: 5+ أحرف',
  'Ex_:_mail.6am.one' => 'على سبيل المثال: Mail.6am. One',
  'Ex : smtp' => 'على سبيل المثال: SMTP',
  'Ex : 587' => 'على سبيل المثال: 587',
  'message_description' => 'وصف الرسالة',
  'restaurant_bulk_export' => 'تخزين التصدير بالجملة',
  'Search Menu...' => 'قائمة البحث ...',
  'Update option is disabled for demo!' => 'تم تعطيل خيار التحديث للتجريبي!',
  'cash on delivery' => 'الدفع عند الاستلام',
  'POS Orders' => 'أوامر POS',
  'Or' => 'أو',
  'Accoutn transaction information' => 'معلومات المعاملة ACCOUTN',
  'preparing_in_restaurants' => 'التحضير في المطاعم',
  'Proceed  If thermal printer is ready.' => 'المضي قدما إذا كانت الطابعة الحرارية جاهزة.',
  'The chosen model will confirm the order first. For example  if you choose the delivery confirmation model then the delivery men will get the orders before the restaurants and confirm for delivery and after confirmation by the delivery men  the restaurants will get the order for processing.' => 'سيؤكد النموذج المختار الطلب أولاً. على سبيل المثال ، إذا اخترت نموذج تأكيد التسليم ، فسيحصل رجال التسليم على الطلبات قبل المطاعم وتأكيد التسليم وبعد تأكيد رجال التوصيل ، ستحصل المطاعم على طلب المعالجة.',
  'restaurant' => 'مطعم',
  'Delivery Man can Cancel Order' => 'يمكن أن يقوم رجل التسليم بإلغاء الطلب',
  'Order cancellation is possible by the delivery person if  Yes  is chosen .' => 'الإلغاء أمر ممكن من قبل شخص التسليم إذا تم اختيار بنعم.',
  'restaurant_can_cancel_order' => 'يمكن للمطعم إلغاء الطلب',
  'Order cancellation is possible by the restaurant if  Yes  is chosen .' => 'الإلغاء أمر ممكن من قبل المطعم إذا تم اختيار نعم.',
  'store_can_cancel_order' => 'يمكن للمتجر إلغاء الطلب',
  'Order cancellation is possible by the store if  Yes  is chosen .' => 'الإلغاء أمر ممكن من قبل المتجر إذا تم اختيار نعم.',
  'preparing_in_stores' => 'التحضير في المتاجر',
  'Campaign title...' => 'عنوان الحملة ...',
  'Coupon Title' => 'عنوان القسيمة',
  'item on the way' => 'عنصر في الطريق',
  'Clear all filters' => 'مسح جميع المرشحات',
  'ssl commerz payment' => 'SSL Commerz الدفع',
  'Want to delete this attribute  ' => 'تريد حذف هذه السمة',
  'Want to delete this unit  ' => 'تريد حذف هذه الوحدة',
  'Want to delete this addon  ' => 'تريد حذف هذا الملحق',
  'Want to remove discount ' => 'تريد إزالة الخصم',
  'Be careful before you turn on/off maintenance mode' => 'كن حذرا قبل تشغيل/إيقاف وضع الصيانة',
  'Maintenance is on.' => 'الصيانة قيد التشغيل.',
  'Maintenance is off.' => 'الصيانة متوقفة.',
  'NB : #OTP# will be replace with otp' => 'NB: سيتم استبدال # OTP # بـ OTP',
  'EX of SMS provider`s template : your OTP is XXXX here  please check.' => 'قالب Ex of SMS Provider`s: OTP الخاص بك هو xxxx هنا يرجى التحقق.',
  'NB : Keep an OTP variable in your SMS providers OTP Template.' => 'NB: احتفظ بمتغير OTP في قالب SMS SMS الخاص بك.',
  'FCM Settings' => 'إعدادات FCM',
  'Feature title' => 'عنوان الميزة',
  'Feature description' => 'ميزة الوصف',
  'Promotions' => 'الترقيات',
  'Your' => 'لك',
  'All Service' => 'كل الخدمة',
  'in one field' => 'في مجال واحد',
  'welcome_back' => 'مرحبًا بعودتك',
  'order_on_the_way' => 'اطلب على الطريق',
  'cash_receipt' => 'إيصال الدفع',
  'contact_name' => 'اسم الاتصال',
  'desc' => 'DESC',
  'Ex: Jhone' => 'على سبيل المثال: Jhone',
  'Login as Store Employee' => 'تسجيل الدخول كموظف متجر',
  'login here' => 'تسجيل الدخول هنا',
  'Login as Store Owner' => 'تسجيل الدخول كمالك متجر',
  'Do you want to logout ' => 'هل ترغب بالخروج',
  'Enter order verification code' => 'أدخل رمز التحقق من الطلب',
  'ex_:_search_by_order_ID' => 'على سبيل المثال: البحث حسب معرف الطلب',
  'take away' => 'يبعد',
  'Customer Not found!' => 'لم يتم العثور على العميل!',
  'paid_amount_is_less_than_total_amount' => 'المبلغ المدفوع أقل من المبلغ الإجمالي',
  'Both' => 'كلاهما',
  'both' => 'كلاهما',
  'digital payment' => 'الدفع الرقمي',
  'Messages' => 'رسائل',
  'After activating this field  customers are able to place scheduled orders.' => 'بعد تنشيط هذا الحقل ، يمكن للعملاء تقديم الطلبات المجدولة.',
  'If this option is enabled  the Delivery men Tip option will show on the user app & web app during order placement.' => 'إذا تم تمكين هذا الخيار ، فسيتم عرض خيار Terch Men Men على تطبيق المستخدم وتطبيق الويب أثناء وضع الطلب.',
  'If this field is enabled  the delivery man is able to see the earnings when accepting the order on the order request page.' => 'إذا تم تمكين هذا الحقل ، فسيكون رجل التسليم قادرًا على رؤية الأرباح عند قبول الطلب على صفحة طلب الطلب.',
  'Turning on this  admin will get a popup notification with sound for all orders.' => 'سيحصل تشغيل هذا المسؤول على إشعار منبثقة مع الصوت لجميع الطلبات.',
  'If this field is active  customers have to provide a 4-digit code to the delivery man to deliver an order successfully. Customers will get this code in order details.' => 'إذا كان هذا الحقل نشطًا ، فيجب على العملاء تقديم رمز مكون من 4 أرقام لرجل التسليم لتقديم طلب بنجاح. سيحصل العملاء على هذا الرمز بتفاصيل الطلب.',
  'When this field is active  the restaurants and the customers both can see the veg/non-veg tag.' => 'عندما ينشط هذا الحقل ، يمكن للمطاعم أن يرى العملاء علامة الخضار/غير الخضار.',
  'If this field is active  stores can register themself using the restaurant app  user app  or website.' => 'إذا كان هذا الحقل هو المتاجر النشطة ، فيمكنها تسجيل نفسها باستخدام تطبيق أو موقع تطبيق تطبيق المطعم.',
  'When this field is active  delivery men can register themself using the delivery man app  user app  or website.' => 'عندما يكون هذا الحقل نشطًا ، يمكن للرجال تسجيل أنفسهم باستخدام تطبيق أو موقع تطبيق تطبيق APP للتسليم.',
  'When this field is active  the stores and the customers both can see the veg/non-veg tag.' => 'عندما يكون هذا الحقل نشطًا ، يمكن للعملاء رؤية علامة الخضار/غير الخضار.',
  'If this field is active  stores can register themself using the store app  user app  or website.' => 'إذا كان هذا الحقل هو المتاجر النشطة ، فيمكنها تسجيل نفسها باستخدام تطبيق أو موقع مستخدم تطبيق المتجر.',
  'The chosen model will confirm the order first. For example  if you choose the delivery confirmation model then the delivery men will get the orders before the stores and confirm for delivery and after confirmation by the delivery men  the stores will get the order for processing.' => 'سيؤكد النموذج المختار الطلب أولاً. على سبيل المثال ، إذا اخترت نموذج تأكيد التسليم ، فسيحصل رجال التسليم على الطلبات قبل المتاجر وتأكيد التسليم وبعد تأكيد رجال التوصيل ، ستحصل المتاجر على طلب المعالجة.',
  'By disabling this field  the store can`t manage items  which means the store web panel app won`t get the access for managing items' => 'من خلال تعطيل هذا الحقل ، لا يمكن أن يدير المتجر عناصر مما يعني أن تطبيق لوحة الويب المتجر لن يحصل على إمكانية الوصول لإدارة العناصر',
  'If this field is active  the store panel & store app can see the customer`s review' => 'إذا كان هذا الحقل نشطًا ، يمكن لتطبيق لوحة المتجر والتخزين رؤية مراجعة العميل',
  'Point of Sale (POS) optio' => 'إذا تم تشغيل هذا الخيار',
  'order for this stor' => 'إذا كان هذا الوضع',
  'from the restaurant pane' => 'عندما يكون هذا الخيار ena',
  'from the store pane' => 'عندما هذا الخيار أنا',
  'customer_wallet_disable_warning_admin' => 'محفظة العميل في تعطيل ، يرجى تمكينها من إعدادات العملاء.',
  'Order Refunds' => 'استرداد الطلبات',
  'Refund Requests' => 'طلبات الاسترداد',
  'refund_settings' => 'إعدادات الاسترداد',
  'requested' => 'مطلوب',
  'Refund' => 'استرداد',
  'refund_rejected' => 'رفض المبلغ',
  'Rejected' => 'مرفوض',
  'rejected' => 'مرفوض',
  'Refund Settings' => 'إعدادات الاسترداد',
  'Refund Request' => 'طلب ارجاع',
  'Mode' => 'وضع',
  '*By Turning ON Refund Mode  Customers Can Sent Refund Requests' => '*عن طريق تشغيل وضع الاسترداد ، يمكن للعملاء إرسال طلبات استرداد الأموال',
  'Add a Refund Reason' => 'أضف سبب استرداد',
  'New Refund Reason' => 'سبب استرداد جديد',
  'Add Now' => 'إضافة الآن',
  'Refund Reason List' => 'قائمة أسباب الاسترداد',
  'Refund Reason Added Successfully' => 'رد المبلغ المضافة بنجاح',
  'Want to delete this refund reason  ' => 'تريد حذف هذا العقل الاسترداد',
  'Reason' => 'سبب',
  'Refund Reason Deleted Successfully' => 'تم حذف سبب الاسترداد بنجاح',
  'Fatoorah' => 'fatorah',
  'Refund Reason Updated Successfully' => 'رد المبلغ الذي تم تحديثه بنجاح',
  'refund requested' => 'استرداد طلب',
  'Order Rejection' => 'الرفض',
  'Note' => 'ملحوظة',
  'Confirm' => 'يتأكد',
  'requested refunds' => 'المبالغ المستردة المطلوبة',
  'Refund Reason' => 'سبب استرداد',
  'Customer Note' => 'ملاحظة العميل',
  'Admin Note' => 'ملاحظة المسؤول',
  'Refund Amount' => 'المبلغ المسترد',
  'Refund Status' => 'حالة الاسترداد',
  'Refund Method' => 'طريقة الاسترداد',
  'Refund Requested on' => 'استرداد المطلوب',
  'Refund Rejection Successfully' => 'رد الرفض بنجاح',
  'Your Refund Request has been Rejected.' => 'تم رفض طلب الاسترداد الخاص بك.',
  'refund request canceled' => 'تم إلغاء طلب استرداد المبلغ',
  'Refund Rejected' => 'رفض المبلغ',
  'pay later' => 'ادفع لاحقا',
  'requested refund' => 'طلب الاسترداد',
  'requested_on' => 'طلب على',
  'Method' => 'طريقة',
  'Status' => 'حالة',
  'refund' => 'استرداد',
  'Social Login Setup' => 'إعداد تسجيل الدخول الاجتماعي',
  'created' => 'مخلوق',
  'Copied to the clipboard' => 'نسخ إلى الحافظة',
  'default_link' => 'الرابط الافتراضي',
  'Module Setup' => 'إعداد الوحدة النمطية',
  'per_km_delivery_charge' => 'لكل رسوم توصيل كيلومتر',
  'zone_module_updated_successfully' => 'تم تحديث وحدة المنطقة بنجاح',
  'Zone Wise Module Setup' => 'إعداد الوحدة الحكيمة المنطقة',
  'Admin Comission in Delivery Charge' => 'مسؤول المسؤول في رسوم التوصيل',
  'Store Wise Report' => 'تخزين تقرير حكيم',
  'Summary Report' => 'تقرير ملخص',
  'Sales Report' => 'تقرير المبيعات',
  'Order Report' => 'تقرير الطلب',
  'Transactions Report' => 'تقرير المعاملات',
  'Store Report' => 'تقرير المتجر',
  'order_statistics' => 'إحصائيات الطلب',
  'Total Orders' => 'إجمالي الطلبات',
  'Average Order Value :' => 'متوسط ​​قيمة الطلب:',
  'Total Stores' => 'مجموع المتاجر',
  'Store' => 'محل',
  'Total Order' => 'من أجل الكاملة',
  'Total Amount' => 'المبلغ الإجمالي',
  'Completion Rate' => 'معدل الانتهاء',
  'Cancelation Rate' => 'معدل الإلغاء',
  'Action' => 'فعل',
  'Search by ID  customer or payment status' => 'ابحث عن طريق عميل الهوية أو حالة الدفع',
  'Filter Data' => 'تصفية البيانات',
  'Total Sales' => 'إجمالي المبيعات',
  'Product' => 'منتج',
  'QTY Sold' => 'تباع كتي',
  'Gross Sale' => 'بيع الإجمالي',
  'Tax' => 'ضريبة',
  'Commission' => 'عمولة',
  'Discount Given' => 'خصم',
  'Net Sale' => 'بيع صافي',
  'payment statistics' => 'إحصائيات الدفع',
  'Order ID' => 'رقم التعريف الخاص بالطلب',
  'Order Date' => 'تاريخ الطلب',
  'Customer Info' => 'معلومات العميل',
  'Discount' => 'تخفيض',
  'Delivery Charge' => 'رسوم التوصيل',
  'XID' => 'XID',
  'Created At' => 'أنشئت في',
  'Transaction Amount' => 'قيمة التحويل',
  'Reference' => 'مرجع',
  'Search by product..' => 'البحث عن طريق المنتج ..',
  'store_wise_report' => 'تخزين تقرير حكيم',
  'Search by ID..' => 'البحث عن طريق المعرف ..',
  'coupon_discount_amount' => 'مبلغ خصم القسيمة',
  'total_tax_amount' => 'إجمالي مبلغ الضريبة',
  'All Time' => 'كل الوقت',
  'This Year' => 'هذا العام',
  'Previous Year' => 'السنة الماضية',
  'This Week' => 'هذا الاسبوع',
  'Incomplete' => 'غير مكتمل',
  'Completed' => 'مكتمل',
  'Total Items' => 'إجمالي العناصر',
  'Cash Payments' => 'التسديد نقذا',
  'Digital Payments' => 'المدفوعات الرقمية',
  'delivery_charge_per_km' => 'رسوم التوصيل لكل كيلومتر',
  'Comission on delivery fee' => 'Comission على رسوم التسليم',
  'Ongoing Rate' => 'معدل مستمر',
  'order statistics' => 'إحصائيات الطلب',
  'Custom' => 'مخصص',
  'Total Discount Given' => 'إجمالي الخصم المعطى',
  'Product Discount' => 'خصم المنتج',
  'View All Orders' => 'عرض جميع الطلبات',
  'Completed payment statistics' => 'إحصائيات الدفع المكتملة',
  'Total Delivered Order' => 'ترتيب إجمالي تسليمه',
  'Not applicable' => 'غير قابل للتطبيق',
  'Average Value of completed orders.' => 'هذه القيمة هي متوسط ​​إجمالي مبلغ الطلب وإجمالي عدد الطلبات المكتملة.',
  'Average Value of all type of orders.' => 'هذه القيمة هي مجموع الترتيب والمبلغ المكتمل.',
  'ecommerce' => 'التجارة الإلكترونية',
  'venture_starts_here_!' => 'يبدأ المشروع هنا!',
  'Enjoy all services in one platform' => 'استمتع بجميع الخدمات في منصة واحدة',
  'grocery' => 'خضروات',
  'pharmacy' => 'مقابل',
  'Earn point by' => 'كسب نقطة من قبل',
  'Refer' => 'يشير إلى',
  'Friend' => 'صديق',
  'Refer a friend' => 'أوص أحد الأصدقاء',
  'Earn' => 'يكسب',
  'Money' => 'مال',
  'Earn money by using different platform' => 'كسب المال باستخدام منصة مختلفة',
  'Download Seller' => 'تنزيل البائع',
  'App' => 'برنامج',
  'Become a best' => 'تصبح أفضل',
  'Seller' => 'تاجر',
  'Download Deliveryman' => 'تنزيل التسليم',
  'Become a smart' => 'تصبح ذكية',
  'Deliveryman' => 'رجل التوصيل',
  'What s so' => 'ما هو ذلك',
  'Special' => 'خاص',
  'About' => 'عن',
  '6am' => '6am',
  'Mart ' => 'مارت',
  'Download' => 'تحميل',
  'Car provider' => 'مزود سيارة',
  'Still increasing' => 'لا يزال يتزايد',
  'Letâs' => 'Letâs',
  'Manage your business' => 'إدارة عملك',
  'Smartly or Earn.' => 'بذكاء أو كسب.',
  'Download the Seller App' => 'قم بتنزيل تطبيق البائع',
  'Download the deliveryman App' => 'قم بتنزيل تطبيق Deliveryman',
  'Suppport' => 'Suppport',
  'Contact' => 'اتصال',
  'Application' => 'طلب',
  'join as' => 'انضمام إلى',
  'download App Section' => 'تنزيل قسم التطبيق',
  'promotion Banner' => 'لافتة الترويج',
  'module Section' => 'قسم الوحدة النمطية',
  'seller_banner_bg' => 'بائع لافتة BG',
  'deliveryman_banner_bg' => 'تسليم لافتة BG',
  'landing_page_download_app_section_updated' => 'تم تحديث قسم تطبيق الصفحة المقصودة',
  'sub_title' => 'العنوان الفرعي',
  'banner_image' => 'صورة بانر',
  'two' => 'اثنين',
  'image two' => 'صورة اثنين',
  'Privacy' => 'خصوصية',
  'Policy' => 'سياسة',
  'Get' => 'يحصل',
  'in touch' => 'على اتصال',
  'Any question or remarks ' => 'أي سؤال أو ملاحظات',
  'Just write us a message!' => 'فقط اكتب لنا رسالة!',
  'Call' => 'يتصل',
  'Us' => 'نحن',
  'Email' => 'بريد إلكتروني',
  'Address' => 'عنوان',
  'Time' => 'وقت',
  'Monday' => 'الاثنين',
  'Friday' => 'جمعة',
  'Send' => 'يرسل',
  'Message' => 'رسالة',
  'Terms' => 'شروط',
  'And' => 'و',
  'Conditions' => 'شروط',
  'Refunded amount added to customer wallet' => 'المبلغ المسترد المضافة إلى محفظة العميل',
  'What' => 'ماذا',
  's so' => 'S هكذا',
  'Download Seller App' => 'تنزيل تطبيق البائع',
  'Download Deliveryman App' => 'تنزيل تسليم تطبيق',
  'module_section_title' => 'عنوان قسم الوحدة النمطية',
  'module_section_sub_title' => 'قسم الوحدة النمطية الفرعية',
  'refer_section_title' => 'إحالة عنوان القسم',
  'refer_section_sub_title' => 'إحالة العنوان الفرعي للقسمة',
  'refer_section_description' => 'إحالة وصف القسم',
  'joinus_section_title' => 'عنوان قسم Joinus',
  'joinus_section_sub_title' => 'عنوان قسم Joinus Sub Sub',
  'download_app_section_title' => 'تنزيل عنوان قسم التطبيق',
  'download_app_section_sub_title' => 'تنزيل قسم التطبيق الفرعي',
  'newsletter_title' => 'عنوان النشرة الإخبارية',
  'newsletter_sub_title' => 'العنوان الفرعي النشرة الإخبارية',
  'contact_us_title' => 'اتصل بنا العنوان',
  'contact_us_sub_title' => 'اتصل بنا العنوان الفرعي',
  'landing_page_promotion_banner_updated' => 'تم تحديث لافتة الترويج للصفحة المقصودة',
  'landing_page_module_section_updated' => 'تم تحديث قسم وحدة الصفحة المقصودة',
  'Reviewer Brand' => 'العلامة التجارية للمراجع',
  'Image' => 'صورة',
  'seller_banner_background' => 'خلفية لافتة البائع',
  'deliveryman_banner_background' => 'تسليم لافتة خلفية',
  'app_download_count_numbers' => 'أرقام عدد المستخدمين',
  'seller_count_numbers' => 'عدد العد البائع',
  'deliveryman_count_numbers' => 'تسليم العد أرقام',
  'Opening Time' => 'وقت مفتوح',
  'Closing Time' => 'وقت الإغلاق',
  'opening_day' => 'يوم الإفتتاح',
  'thrusday' => 'يوم الدعارة',
  'closing_day' => 'يوم الاغلاق',
  'application' => 'طلب',
  'Contact Messages' => 'اتصل بالرسائل',
  'all_message_lists' => 'جميع قوائم الرسائل',
  'message_lists' => 'قوائم الرسائل',
  'ex_:_message_name' => 'على سبيل المثال: اسم الرسالة',
  'subject' => 'موضوع',
  'Seen/Unseen' => 'رأيت/غير مرئي',
  'Not_Seen_Yet' => 'لم ير بعد',
  'Message View' => 'عرض الرسالة',
  'Message_View' => 'عرض الرسالة',
  'User_details' => 'بيانات المستخدم',
  'Feedback' => 'تعليق',
  'Please_send_a_Feedback' => 'الرجاء إرسال ملاحظات',
  'check' => 'يفحص',
  'Message_Log' => 'سجل الرسائل',
  'Subject' => 'موضوع',
  'No_reply' => 'لا يوجد رد',
  'Send_Mail' => 'ارسل بريد',
  'Configure_your_mail_setup_first' => 'قم بتكوين إعداد البريد الخاص بك أولاً',
  'Mail_Body' => 'جسم البريد',
  'Reply_form_6amMart' => 'الرد نموذج 6AMMART',
  'Dear' => 'عزيزي',
  'All copy right reserved' => 'كل نسخ محفوظة',
  'Seen' => 'مرئي',
  'contact_deleted_successfully' => 'تم حذف الاتصال بنجاح',
  'already_check' => 'تحقق بالفعل',
  'Cancel Refund' => 'إلغاء الاسترداد',
  'colors' => 'الألوان',
  'Primary Color 1' => 'اللون الأساسي 1',
  'Primary Color 2' => 'اللون الأساسي 2',
  'Add Parcel Category' => 'إضافة فئة الطرود',
  'You have to set category wise charge from parcel category' => 'يجب عليك تعيين فئة الحكمة من فئة الطرود',
  'Set charge from parcel category' => 'تعيين رسوم من فئة الطرود',
  'New Registered Stores' => 'المتاجر المسجلة الجديدة',
  'New Items' => 'عناصر جديدة',
  'Currently you need to manage discount with store.' => 'تحتاج حاليًا إلى إدارة الخصم مع المتجر.',
  'expense_report' => 'تقرير المصاريف',
  'Expense Report' => 'تقرير المصاريف',
  'Search by type or description' => 'ابحث حسب النوع أو الوصف',
  'expense_date' => 'تاريخ النفقات',
  'delivery_commission' => 'لجنة التوصيل',
  'Total Tax' => 'مجموع الضريبة',
  'Total Commission' => 'إجمالي العمولة',
  'Total Store Earnings' => 'إجمالي أرباح المتجر',
  'contact_messages' => 'اتصل بالرسائل',
  'Search by order_id' => 'البحث حسب معرف الطلب',
  'landing_page_counter_section_updated' => 'تم تحديث قسم عداد الصفحة المقصودة',
  'contact_us_image' => 'اتصل بنا الصورة',
  'cancelation' => 'الإلغاء',
  'shipping_policy' => 'سياسة الشحن',
  'shipping_policy_updated' => 'تحديث سياسة الشحن',
  'Want to delete this message ' => 'تريد حذف هذه الرسالة',
  'Reply_form_6amMart 123' => 'الرد نموذج 6AMMART 123',
  'Something went wrong!' => 'هناك خطأ ما!',
  'refund_page' => 'صفحة استرداد',
  'refund_updated' => 'استرداد التحديث',
  'cancelation_updated' => 'تحديث إلغاء',
  'Want to delete this message  ' => 'تريد حذف هذه الرسالة',
  'Refund Policy' => 'سياسة الاسترجاع',
  'Cancelation Policy' => 'سياسة الإلغاء',
  'Shipping Policy' => 'سياسة الشحن',
  'seller_App_url' => 'بائع APP URL',
  'deliveryman_App_url' => 'url App App Deliveryman',
  'Grocery' => 'خضروات',
  'Pharmacy' => 'مقابل',
  'Shop' => 'محل',
  'Food' => 'طعام',
  'Parcel' => 'قطعة',
  'Test A Module' => 'اختبار وحدة',
  'parcel123' => 'Parcel123',
  'Super Shop' => 'متجر سوبر',
  'parcel144' => 'Parcel144',
  'Module_test' => 'اختبار الوحدة',
  'Food43454' => 'Food43454',
  'Grocery2222' => 'البقالة 2222',
  'Module for all zone' => 'وحدة لجميع المنطقة',
  'Grocery All Zone' => 'البقالة كل المنطقة',
  'PRO Super Shop' => 'Pro Super Shop',
  'New  GroceryÂ Â System Module' => 'وحدة نظام البقالة الجديدة',
  'New  Parcel System Module' => 'وحدة نظام الطرود الجديدة',
  'cvb' => 'CVB',
  'test mo' => 'اختبار الاثنين',
  'Tech Mart' => 'تقنية مارت',
  'Tech parcel' => 'قطعة تقنية',
  'Mart GM test' => 'اختبار مارت جنرال موتورز',
  'Mart  New Parcel For' => 'مارت لادة جديدة ل',
  'Agora x' => 'الآن x',
  'Mart FM test' => 'اختبار Mart FM',
  'ValAgen Gro' => 'فالاجين جرو',
  'ValAgen Food' => 'طعام فالاجين',
  'ValAgen Phar' => 'فالاجين الفار',
  'ValAgen Ecom' => 'فالاجين ECOM',
  'ValAgen Parcel' => 'طرود فالاجين',
  'Tech Mart Mart' => 'Tech Mart Mart',
  'test' => 'امتحان',
  'ghmnh' => 'GHMNH',
  'zsxscds' => 'ZSXSCDS',
  'Test Parcel' => 'اختبار الطرود',
  '6parcel' => '6Parcel',
  'What is so special about' => 'ما هو مميز جدا',
  'Header Title' => 'عنوان الرأس',
  'Header Sub Title' => 'عنوان العنوان الفرعي',
  'asdf' => 'Asdf',
  'sadf' => 'SADF',
  'Store_front' => 'المتجر الجبهة',
  'free_over_delivery_message' => 'إذا تجاوز مبلغ الطلب هذا المبلغ ، فستكون رسوم التسليم مجانية وسيتم استنتاج رسوم التسليم من لجنة Admins.',
  'footer_text' => 'نص تذييل',
  'minimum_order' => 'الحد الأدنى للطلب',
  'schedule_order' => 'أمر الجدول',
  'vendor_id' => 'رقم المورد',
  'cover_photo' => 'صورة الغلاف',
  'reviews_section' => 'قسم المراجعات',
  'off_day' => 'خارج اليوم',
  'order_count' => 'عدد الطلبات',
  'total_order' => 'من أجل الكاملة',
  'order_place_to_schedule_interval' => 'مكان الطلب لجدولة الفاصل الزمني',
  'category_ids' => 'معرفات الفئة',
  'add_ons' => 'إضافة ONS',
  'choice_options' => 'خيارات الاختيار',
  'tax_type' => 'نوع الضريبة',
  'discount_type' => 'نوع الخصم',
  'available_time_starts' => 'يبدأ الوقت المتاح',
  'available_time_ends' => 'نهايات الوقت المتاحة',
  'store_id' => 'معرف المتجر',
  'avg_rating' => 'AVG تصنيف',
  'rating_count' => 'عدد التقييم',
  'unit_id' => 'معرف الوحدة',
  'parent_id' => 'معرف الوالدين',
  'position' => 'موضع',
  'total_delivered_orders' => 'إجمالي أوامر تسليمها',
  'ssl_commerz_payment' => 'SSL Commerz الدفع',
  'Something went wrong' => 'هناك خطأ ما',
  'Currently you need to manage discount with the Restaurant.' => 'تحتاج حاليًا إلى إدارة الخصم مع المطعم.',
  'refund_request_canceled' => 'تم إلغاء طلب استرداد المبلغ',
  'Refund Image' => 'استرداد الصورة',
  'New  GroceryÃÂ ÃÂ System Module' => 'وحدة نظام البقالة الجديدة',
  'insufficient_point' => 'نقطة غير كافية',
  'machine' => 'آلة',
  'store_name' => 'اسم المتجر',
  'store_phone' => 'تخزين الهاتف',
  'owner_name' => 'اسم المالك',
  'owner_phone' => 'هاتف المالك',
  'invalid_latitude_or_longtitude' => 'خط عرض غير صالح أو طويل',
  'amount_received_by' => 'المبلغ المستلم من قبل',
  '9. Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error' => '9. يجب أن يكون Latitude رقمًا بين -90 إلى 90 ، ويجب أن يكون خط الطول رقمًا يتراوح بين -180 إلى 180. وإلا فإنه سيؤدي إلى خطأ خادم.',
  'food_variations' => 'الاختلافات الغذائية',
  'add_new_variation' => 'أضف تباينًا جديدًا',
  'add_new' => 'اضف جديد',
  'selcetion_type' => 'نوع selcetion',
  'Multiple' => 'عديد',
  'Single' => 'أعزب',
  'Min' => 'دقيقة',
  'Max' => 'الأعلى',
  'Required' => 'مطلوب',
  'Delete' => 'يمسح',
  'Option_name' => 'اسم الخيار',
  'Additional_price' => 'سعر إضافي',
  'Add_New_Option' => 'أضف خيار جديد',
  'please_add_more_options_or_change_the_max_value_for' => 'الرجاء إضافة المزيد من الخيارات أو تغيير القيمة القصوى ل',
  'add new variation' => 'أضف تباينًا جديدًا',
  'You_need_to_select_minimum_ ' => 'تحتاج إلى تحديد الحد الأدنى',
  'to_maximum_ ' => 'إلى أقصى حد',
  'Please select items from' => 'الرجاء تحديد العناصر من',
  'Variation' => 'تفاوت',
  'multiple_select' => 'مجموعة مختارة متعددة',
  'Min_select' => 'MIN SELECT',
  'Max_select' => 'ماكس حدد',
  'delivery_tips' => 'نصائح التسليم',
  'Search Data' => 'بيانات البحث',
  'All Category' => 'كل الفئة',
  'Today' => 'اليوم',
  'Custom Date Range' => 'نطاق تاريخ مخصص',
  'Filter' => 'منقي',
  'All Categories' => 'جميع الفئات',
  'Start Date' => 'تاريخ البدء',
  'End Date' => 'تاريخ الانتهاء',
  'single_select' => 'اختيار واحد',
  'Please select minimum ' => 'الرجاء تحديد الحد الأدنى',
  ' For ' => 'ل',
  'average_ratings' => 'متوسط ​​التقييمات',
  'total_amount_sold' => 'إجمالي المبلغ المباع',
  'total_discount_given' => 'إجمالي الخصم المعطى',
  'average_sale_value' => 'متوسط ​​قيمة البيع',
  'Admin Earning' => 'كسب المسؤول',
  'Store Earning' => 'كسب المتجر',
  'Deliveryman Earning' => 'كسب التوصيل',
  'Completed Transaction' => 'معاملة مكتملة',
  'On-Hold Transactions' => 'المعاملات على الحمل',
  'Canceled Transactions' => 'المعاملات الملغاة',
  'order_report' => 'تقرير الطلب',
  'in_progress_orders' => 'في أوامر التقدم',
  'failed_orders' => 'أوامر فاشلة',
  'on_the_way' => 'علي الطريق',
  'Total Item Amount' => 'إجمالي مبلغ البند',
  'Item Discount' => 'خصم البند',
  'Coupon Discount' => 'خصم القسيمة',
  'Discounted Amount' => 'مبلغ مخفض',
  'Order Amount' => 'كمية الطلب',
  'Customer Name' => 'اسم الزبون',
  'Order Status' => 'حالة الطلب',
  'order_amount' => 'كمية الطلب',
  'custom' => 'مخصص',
  'lists' => 'قوائم',
  'expense' => 'مصروف',
  'requests' => 'الطلبات',
  'Pending Requests' => 'الطلبات المعلقة',
  'Store Pending Requests' => 'المتجر في انتظار الطلبات',
  'pending_requests' => 'الطلبات المعلقة',
  'deny_requests' => 'رفض الطلبات',
  'Store Deny Requests' => 'المتجر رفض الطلبات',
  'deny_delivery_man' => 'إنكار رجل التسليم',
  'denied_stores' => 'تم رفض المتاجر',
  'total_item_amount' => 'إجمالي مبلغ البند',
  'item_discount' => 'خصم البند',
  'discounted_amount' => 'مبلغ مخفض',
  'digital_payment' => 'الدفع الرقمي',
  'ssl_commerz' => 'SSL Commerz',
  'vendor_type_required' => 'نوع البائع المطلوب',
  'admin_discount' => 'خصم المسؤول',
  'store_discount' => 'خصم المتجر',
  'current_language_key_required' => 'اللغة الحالية للمستخدم مطلوب!',
  'transection_report' => 'تقرير الانتقال',
  'item_report' => 'تقرير العنصر',
  'Item report table' => 'جدول تقرير العنصر',
  'transaction_report' => 'تقرير المعاملات',
  'parcel_order' => 'ترتيب الطرود',
  'Accepted by Delivery Man' => 'مقبولة من قبل رجل التسليم',
  'Packaging' => 'التغليف',
  'Out for Delivery' => 'خارج للتوصيل',
  'User Statistics' => 'إحصائيات المستخدم',
  'view_all' => 'عرض الكل',
  'Orders' => 'طلبات',
  'Delivery man' => 'رجل التوصيل',
  'Settings' => 'إعدادات',
  'Monitor your business general settings from here' => 'مراقبة الإعدادات العامة لعملك من هنا',
  'Zone Setup' => 'إعداد المنطقة',
  'System Module Setup' => 'إعداد وحدة النظام',
  'Business Settings' => 'إعدادات الأعمال',
  '3rd Party' => 'تخصص صورة',
  'Social Media and Page Setup' => 'وسائل التواصل الاجتماعي وإعداد الصفحة',
  'View All' => 'عرض الكل',
  'Modules Section' => 'قسم الوحدات',
  'Select Module & Monitor your business monitor wise' => 'حدد الوحدة النمطية ومراقبة مراقبة عملك الحكيمة',
  'e-Pharma' => 'e-pharma',
  'e-Medi Care' => 'رعاية ميدي',
  'Vaccine Center' => 'مركز اللقاح',
  'GroFresh' => 'جروفريش',
  'e-Cab' => 'الكابرة الإلكترونية',
  'e-Shop' => 'متجر إلكتروني',
  'top selling stores' => 'متاجر البيع الأعلى',
  'order : ' => 'طلب :',
  'most rated stores' => 'معظم المتاجر تصنيف',
  'top selling products' => 'أفضل منتجات البيع',
  'top rated products' => 'أعلى المنتجات تصنيف',
  'Failed Orders' => 'أوامر فاشلة',
  'category_setup' => 'إعداد الفئة',
  'delivery_fee_setup' => 'إعداد رسوم التسليم',
  'tags' => 'العلامات',
  'foods' => 'الأطعمة',
  'restaurants' => 'المطاعم',
  'U' => 'في',
  'Users' => 'المستخدمون',
  'Transactions & Reports' => 'المعاملات والتقارير',
  'total_customer' => 'إجمالي العميل',
  'active_customer' => 'عميل نشط',
  'blocked_customers' => 'العملاء المحظورون',
  'blocked_customer' => 'عميل محظور',
  'newly_joined' => 'انضم حديثا',
  'total_delivery_man' => 'إجمالي التوصيل رجل',
  'active_delivery_man' => 'رجل التوصيل النشط',
  'newly_joined_delivery_man' => 'انضم حديثا رجل التسليم',
  'inactive_deliveryman' => 'تسليم غير نشط',
  'assing_order_count' => 'عدد أوامر التأكيد',
  'total_employee' => 'إجمالي الموظف',
  'customer_satisfaction' => 'رضا العملاء',
  'review_received' => 'استلمت المراجعة',
  'customer_growth' => 'نمو العملاء',
  'this_year' => 'هذا العام',
  'man' => 'رجل',
  'delivery_man_settings' => 'إعدادات رجل التسليم',
  'Date & Time' => 'التاريخ والوقت',
  'Expense Type' => 'نوع المصاريف',
  'expense amount' => 'مبلغ المصاريف',
  'Delivery Man Request' => 'طلب رجل التسليم',
  'store_report' => 'تقرير المتجر',
  'store request' => 'طلب المتجر',
  'Including accepted and processing Orders' => 'بما في ذلك أوامر المقبولة والمعالجة',
  'Including accepted and processing orders' => 'بما في ذلك أوامر المقبولة والمعالجة',
  'cache_clear' => 'ذاكرة التخزين المؤقت واضح',
  'cache_clear_successfully' => 'ذاكرة التخزين المؤقت مسح بنجاح',
  'delivery_man_request' => 'طلب رجل التسليم',
  'prescription_order' => 'ترتيب وصفة طبية',
  'update_order_amount' => 'تحديث مبلغ الطلب',
  'update_discount_amount' => 'تحديث مبلغ الخصم',
  'store_request' => 'طلب المتجر',
  'Refunded' => 'رد',
  'Refunded Transactions' => 'المعاملات المستردة',
  'Refunded Transaction' => 'معاملة استرداد',
  'commision_on_delivery_charge' => 'تعهد رسوم التسليم',
  'admin_net_income' => 'صافي الدخل الإداري',
  'store_net_income' => 'تخزين صافي الدخل',
  'order_transaction_statement' => 'أمر معاملة الطلب',
  'statement' => 'إفادة',
  'If_you_require_any_assistance_or_have_feedback_or_suggestions_about_our_site _you' => 'إذا كنت بحاجة إلى أي مساعدة أو لديك ملاحظات أو اقتراحات حول موقعنا أنت',
  'can_email_us_at' => 'يمكن مراسلتنا عبر البريد الإلكتروني على',
  'All_copy_right_reserved_Â©_2023_' => 'كل نسخة محفوظة â © 2023',
  'total_ordered_product_price' => 'إجمالي سعر المنتج المطلوب',
  'total_product_discount' => 'إجمالي خصم المنتج',
  'total_coupon_discount' => 'إجمالي خصم القسيمة',
  'total_discounted_amount' => 'إجمالي المبلغ المخفض',
  'total_vat/_tax' => 'إجمالي ضريبة القيمة المضافة/ الضريبة',
  'total_delivery_charge' => 'إجمالي رسوم التوصيل',
  'additional_information' => 'معلومات إضافية',
  'totals' => 'المجاميع',
  'the_start_day_and_end_day_is_same' => 'يوم البدء ونهاية هو نفسه',
  'completed' => 'مكتمل',
  'delivered_by' => 'سلمت بواسطة',
  'freelance' => 'حسابهم الخاص',
  'email_not_found' => 'البريد الإلكتروني غير موجود',
  'prescription_order_hint' => 'من خلال تمكين هذا الخيار ، سيتمكن المستخدمون من تحميل الوصفات الطبية وتقديم الطلبات بدلاً من إضافة الدواء إلى العربة',
  'not_received_yet' => 'لم يتم استلامها بعد',
  'Registered Stores' => 'المتاجر المسجلة',
  'search_by_name' => 'البحث عن طريق الإسم',
  'new_joining_requests' => 'طلبات انضمام جديدة',
  'pending_stores' => 'متاجر معلقة',
  'maximum_cod_order_amount' => 'الحد الأقصى لمبلغ طلب سمك القد',
  'set_maximum_cod_order_amount' => 'مبلغ الحد الأقصى للطلب مقابل التسليم',
  'search_tags' => 'علامات البحث',
  'discount_given' => 'خصم',
  'pending_delivery_man' => 'رجل التسليم المعلق',
  'denied_deliveryman' => 'نفى التسليم',
  'ex_: search_delivery_man' => 'على سبيل المثال: Search Delivery Man',
  'denied_delivery_man' => 'نفى تسليم رجل',
  'Want to change digital payment for this zone  ' => 'تريد تغيير الدفع الرقمي لهذه المنطقة',
  'Want to change cash on delivery for this zone  ' => 'تريد تغيير النقد عند التسليم لهذه المنطقة',
  'zone_cash_on_delivery_status_updated' => 'نقود المنطقة عند تحديث حالة التسليم',
  'zone_digital_payment_status_updated' => 'تم تحديث حالة الدفع الرقمي للمنطقة',
  'Employees' => 'موظفين',
  'prescription_order_status' => 'حالة طلب الوصفة الطبية',
  'When this field is active  store will get prescription order option.' => 'عندما يكون هذا الحقل ، سيحصل المتجر النشط على خيار طلب وصفة طبية.',
  'Handover' => 'سلم',
  'gross_sale' => 'بيع الإجمالي',
  'Freelance' => 'حسابهم الخاص',
  'When the order is successfully delivered full order amount goes to this section.' => 'عندما يتم تسليم الطلب بنجاح ، يذهب مبلغ الطلب الكامل إلى هذا القسم.',
  'If the order is successfully refunded  the full order amount goes to this section without the delivery fee and delivery tips.' => 'إذا تم استرداد الطلب بنجاح ، فإن مبلغ الطلب الكامل يذهب إلى هذا القسم دون رسوم التسليم ونصائح التسليم.',
  'Deducting the admin discount from the admin net income amount goes to this section.' => 'إن خصم المسؤول من مبلغ الدخل الصافي للمشرف يذهب إلى هذا القسم.',
  'If self-delivery is off  deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.' => 'إذا كان التسليم الذاتي خارجًا ، فسيذهب مبلغ أمر العمولة ومسؤول العمولة لتخزين الأرباح وإلا فإن لجنة المشرف جميع مبلغ الطلب يذهب إلى هذا القسم.',
  'earning section' => 'خصم',
  'Include_TAX_Amount' => 'تشمل المبلغ الضريبي',
  'When this field is active  Tax amount will not be added with the total product price' => 'عندما يكون هذا الحقل ، لن تتم إضافة مبلغ ضريبي نشط مع إجمالي سعر المنتج',
  'TAX_Included' => 'شامل الضريبة',
  'Site Direction' => 'اتجاه الموقع',
  'Left_to_Right' => 'من اليسار إلى اليمين',
  'Right_to_Left' => 'من اليمين الى اليسار',
  'Fully Booked Delivery Man' => 'رجل توصيل محجوز بالكامل',
  'Dispatch Management' => 'إدارة الإرسال',
  'Languages' => 'اللغات',
  'System_Setup' => 'إعداد النظام',
  'changing_some_settings_will_take_time_to_show_effect_please_clear_session_or_wait_for_60_minutes_else_browse_from_incognito_mode' => 'سيستغرق تغيير بعض الإعدادات بعض الوقت لإظهار التأثير ، يرجى مسح الجلسة أو الانتظار لمدة 60 دقيقة أخرى للتصفح من وضع التخفي',
  'language_table' => 'جدول اللغة',
  'add_new_language' => 'أضف لغة جديدة',
  'Id' => 'بطاقة تعريف',
  'Code' => 'شفرة',
  'new_language' => 'لغة جديدة',
  'country_code' => 'الرقم الدولي',
  'direction' => 'اتجاه',
  'status_updated_successfully' => 'تم تحديث الحالة بنجاح',
  'Are you sure to delete this' => 'هل أنت متأكد من حذف هذا',
  'You will not be able to revert this' => 'لن تكون قادرًا على إعادة هذا',
  'delete it' => 'احذفه',
  'default language can not be deleted! to delete change the default language first!' => 'لا يمكن حذف اللغة الافتراضية! لحذف تغيير اللغة الافتراضية أولا!',
  'Translate' => 'يترجم',
  'Dashboard' => 'لوحة القيادة',
  'Language' => 'لغة',
  'language_content_table' => 'جدول محتوى اللغة',
  'SL#' => 'يثبت#',
  'auto_translate' => 'ترجمة السيارات',
  'text_updated_successfully' => 'تم تحديث النص بنجاح',
  'Key removed successfully' => 'إزالتها بنجاح',
  'Key translated successfully' => 'مفتاح ترجمة بنجاح',
  'searching for deliverymen' => 'البحث عن رجال التوصيل',
  'This week' => 'هذا الاسبوع',
  'This year' => 'هذا العام',
  'Cooking' => 'طبخ',
  'most rated' => 'الأكثر تقييما',
  'top rated' => 'أعلى التقييمات',
  'top selling' => 'الأكثر مبيعا',
  'Grocery Stores' => 'محلات البقالة',
  'newly added' => 'مضاف حديثا',
  'available_delivery_man' => 'رجل التوصيل المتاح',
  'Available to assign more order' => 'متاح لتعيين المزيد من الطلبات',
  'sale' => 'أُوكَازيُون',
  'new_restaurants' => 'مطاعم جديدة',
  'add new restaurant' => 'أضف مطعم جديد',
  'Search Delivery Man ...' => 'تسليم البحث رجل ...',
  'Withdraw Requests' => 'سحب الطلبات',
  'Delivery Man Payments' => 'مدفوعات رجل التسليم',
  'Currently Active Delivery Men' => 'رجال التوصيل النشط حاليا',
  'View All Delivery Men' => 'عرض جميع رجال التوصيل',
  'User Overview' => 'نظرة عامة على المستخدم',
  'Hello  here you can manage your users by zone.' => 'مرحبًا هنا ، يمكنك إدارة المستخدمين حسب المنطقة.',
  'Dispatch Overview' => 'نظرة عامة على إرسال',
  'Hello  here you can manage your dispatch management.' => 'مرحبًا هنا ، يمكنك إدارة إدارة الإرسال الخاصة بك.',
  'Hello  here you can manage your dispatch orders.' => 'مرحبًا هنا ، يمكنك إدارة أوامر الإرسال الخاصة بك.',
  'Hello  here you can manage your' => 'مرحبا هنا يمكنك إدارة',
  'orders by zone.' => 'أوامر حسب المنطقة.',
  'Hello  Here You Can Manage Your' => 'مرحبا هنا يمكنك إدارة',
  'orders by Zone.' => 'أوامر حسب المنطقة.',
  'Select Module & Monitor your business module wise' => 'حدد الوحدة النمطية ومراقبة وحدة عملك الحكيمة',
  'Please  Enable or Create Module First' => 'يرجى تمكين أو إنشاء الوحدة النمطية أولاً',
  'Customer Statistics' => 'إحصائيات العملاء',
  'Positive' => 'إيجابي',
  'Good' => 'جيد',
  'Neutral' => 'حيادي',
  'Negetive' => 'سلبي',
  'Deliveryman Statistics' => 'إحصائيات التوصيل',
  'pos section' => 'قسم POS',
  'New Sale' => 'بيع جديد',
  'Promotion Management' => 'إدارة الترويج',
  'Product Setup' => 'إعداد المنتج',
  'Company Information' => 'معلومات الشركة',
  'Currency Symbol' => 'رمز العملة',
  'Currency Position' => 'موقف العملة',
  'Default Commission on Order' => 'لجنة الطلب الافتراضية',
  'Commission on Delivery Charge' => 'لجنة تهمة التسليم',
  'Company Location' => 'موقع الشركة',
  'Logo & Icon' => 'شعار وأيقونة',
  'Office Opening & Closing' => 'فتح المكتب وإغلاقه',
  'add_module' => 'إضافة الوحدة النمطية',
  'add_new_module' => 'أضف وحدة جديدة',
  'default language can not be updated! to update change the default language first!' => 'لا يمكن تحديث اللغة الافتراضية! لتحديث تغيير اللغة الافتراضية أولاً!',
  'Vehicle_List' => 'قائمة المركبات',
  'vehicles_category_list' => 'قائمة فئة المركبات',
  'add_vehicle_category' => 'إضافة فئة السيارة',
  'Ex: Search by type...' => 'على سبيل المثال: البحث حسب النوع ...',
  'Type' => 'يكتب',
  'Starting_coverage_area' => 'بدء منطقة التغطية',
  'Maximum_coverage_area' => 'منطقة التغطية القصوى',
  'Extra_charges' => 'رسوم إضافية',
  'vehicles_category' => 'فئة المركبات',
  'Add new Vehicle' => 'أضف مركبة جديدة',
  'Vehicle' => 'عربة',
  'extra_charges' => 'رسوم إضافية',
  'This amount will be added with delivery charge' => 'سيتم إضافة هذا المبلغ مع رسوم التوصيل',
  'starting_coverage_area' => 'بدء منطقة التغطية',
  'minimum_coverage_area_hint' => 'يلمح الحد الأدنى من التغطية',
  'maximum_coverage_area' => 'منطقة التغطية القصوى',
  'maximum_coverage_area_hint' => 'تلميح منطقة التغطية القصوى',
  'Vehicle_created' => 'تم إنشاؤها مركبة',
  'vehicle' => 'عربة',
  'vehicle view' => 'عرض السيارة',
  'vehicle_type' => 'نوع السيارة',
  'Vehicle Information' => 'معلومات السيارة',
  'No_vehicle_data_found' => 'لم يتم العثور على بيانات مركبة',
  'select_a_vehicle' => 'حدد مركبة',
  'Vehicle_Type' => 'نوع السيارة',
  'Vehicle_Extra_Charges' => 'مركبة رسوم إضافية',
  'Vehicle_minimum_coverage_area' => 'مساحة التغطية الحد الأدنى للمركبة',
  'Vehicle_maximum_coverage_area' => 'مساحة التغطية القصوى للمركبة',
  'Maximum delivery charge' => 'أقصى رسوم توصيل',
  'maximum delivery charge' => 'أقصى رسوم توصيل',
  'Max_Number_of_Otp_Hits_in_a_Row' => 'الحد الأقصى لعدد ضربات OTP على التوالي',
  'otp_resend_interval_time' => 'OTP إعادة الالتحاق بوقت الفاصل الزمني',
  'seconds' => 'ثوان',
  'you_want_to_confirm_this_store' => 'تريد تأكيد هذا المتجر',
  'Approve' => 'يعتمد',
  'you_want_to_reject_this_store' => 'تريد رفض هذا المتجر',
  'Deny' => 'ينكر',
  'not_approved' => 'غير مقبول',
  'New message arrived' => 'وصلت رسالة جديدة',
  '--' => '--',
  'Food Setup' => 'إعداد الطعام',
  'Recommended' => 'مُستَحسَن',
  'Recommend_to_customers' => 'التوصية للعملاء',
  'Item recommendation updated!' => 'توصية عنصر تحديث!',
  'order_cancellation_reasons' => 'أسباب إلغاء الطلب',
  'add_an_order_cancellation_reason' => 'أضف سبب إلغاء الطلب',
  'select_user_type' => 'حدد نوع المستخدم',
  'add_reason' => 'أضف سببًا',
  'order_cancellation_reason_list' => 'قائمة أسباب إلغاء الطلب',
  'order_cancellation_reason' => 'سبب الإلغاء',
  'Save_changes' => 'حفظ التغييرات',
  'Order Type' => 'نوع الطلب',
  'select_cancellation_reason' => 'حدد سبب الإلغاء',
  'order_cancellation_reason_added_successfully' => 'تم إضافة سبب إلغاء الطلب بنجاح',
  'want_to_delete_this_order_cancellation_reason' => 'تريد حذف سبب إلغاء الطلب هذا',
  'Cancelled_By' => 'ألغيت بواسطة',
  'This report shows the delivery fee for all orders whose delivery fee is free for using  free delivery over  or  free delivery coupon .' => 'يوضح هذا التقرير رسوم التسليم لجميع الطلبات التي تكون رسوم التوصيل مجانية لاستخدام "توصيل مجاني" أو "قسيمة توصيل مجانية".',
  'Report' => 'تقرير',
  'vendor_expense_report_discription' => 'تقرير تقرير مصاريف البائع',
  'Search by title or code' => 'ابحث حسب العنوان أو الرمز',
  'No data to show' => 'لا توجد بيانات لإظهارها',
  'Taxi' => 'سيارة اجره',
  'Taxi Rent' => 'تأجير التاكسي',
  'Coverage_area_overlapped' => 'تداخل منطقة التغطية',
  'update_vehicle_category' => 'تحديث فئة السيارة',
  'Vehicle_updated' => 'مركبة تحديث',
  'add_fund_by_customer' => 'إضافة صندوق من قبل العميل',
  'refund_order' => 'ترتيب استرداد',
  'admin_bonus' => 'مكافأة المسؤول',
  'maximum_shipping_charge' => 'شحن الشحن القصوى',
  'app_min_version_hint' => 'سيتم مقارنة هذا الإصدار مع متغير App_version المتاح في ملف App_Constants.dart (مسار الملف: lib/util/app_constants.dart)',
  'Download_Url' => 'تنزيل عنوان URL',
  'deliveryman_app_section' => 'قسم تطبيق التسليم',
  'for_deliveryman' => 'للتسليم',
  'store_app_section' => 'قسم التطبيق',
  'for_store' => 'للمتجر',
  'discount_on_product' => 'خصم على المنتج',
  'Vehicle_category_updated' => 'فئة مركبة تحديث',
  'Vehicle_category_created' => 'فئة المركبات التي تم إنشاؤها',
  'minimum_coverage_area' => 'الحد الأدنى لمنطقة التغطية',
  'campaign_is_expired' => 'انتهت صلاحية الحملة',
  'It will add a limite on total delivery charge.' => 'سيضيف حدودًا على إجمالي رسوم التوصيل.',
  'Maximum delivery charge must be greater than minimum delivery charge.' => 'يجب أن تكون شحنة التسليم القصوى أكبر من الحد الأدنى لرسوم التسليم.',
  'maximum_coverage' => 'الحد الأقصى للتغطية',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & food discounts(partial according to order commission).' => 'سيظهر هذا التقرير جميع الطلبات التي تم فيها استخدام خصم المسؤول. خصم المسؤول هو: التسليم المجاني عبر خصم القسيمة خصم على المتجر وخصومات الطعام (جزئي وفقًا لجنة الطلب).',
  'Update Coupon' => 'تحديث القسيمة',
  'This report will show all the orders in which the store discount has been used. The restaurant discounts are: Free delivery  Coupon discount & item discounts(partial according to order commission).' => 'سيظهر هذا التقرير جميع الطلبات التي تم فيها استخدام خصم المتجر. خصومات المطعم هي: خصومات قسيمة التوصيل المجانية وخصومات العناصر (جزئيًا وفقًا لجنة الطلب).',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & item discounts(partial according to order commission).' => 'سيظهر هذا التقرير جميع الطلبات التي تم فيها استخدام خصم المسؤول. خصم المسؤول هو: تسليم مجاني عبر خصم القسيمة خصم على المتجر وخصومات العناصر (جزئي وفقًا لجنة الطلب).',
  'low_stock_report' => 'تقرير الأسهم منخفضة',
  'react_landing_page' => 'رد فعل الصفحة المقصودة',
  'button_text' => 'زر كتابة',
  'Ex: Button text' => 'على سبيل المثال: نص زر',
  'Ex: Link' => 'على سبيل المثال: الرابط',
  'React Landing Page' => 'رد فعل الصفحة المقصودة',
  'React Landing Page Features' => 'رد فعل ميزات الصفحة المقصودة',
  'Header Section Banner' => 'لافتة قسم الرأس',
  'Change Image' => 'تغيير الصورة',
  'App Section Image' => 'صورة قسم التطبيق',
  'Footer Logo' => 'شعار تذييل',
  'Hero Section' => 'قسم البطل',
  'heading' => 'عنوان',
  'Ex: Heading' => 'على سبيل المثال: العنوان',
  'slogan' => 'شعار',
  'Ex: Slogan' => 'على سبيل المثال: شعار',
  'Short Description' => 'وصف قصير',
  'Ex: Short Description' => 'على سبيل المثال: وصف قصير',
  'Delivery Service Section' => 'قسم خدمة التوصيل',
  'Title' => 'عنوان',
  'Ex: Service title' => 'على سبيل المثال: عنوان الخدمة',
  'Description' => 'وصف',
  'Ex: Description' => 'على سبيل المثال: الوصف',
  '1352 X 250 px' => '1352 × 250 بكسل',
  'React Landing Page Main Banner Image' => 'رد فعل الصفحة المقصودة صورة لافتة رئيسية',
  'Main Banner' => 'لافتة رئيسية',
  'Banner Title' => 'لافتة لافتة',
  'Ex: Banner title' => 'على سبيل المثال: لافتة لافتة',
  'Banner Sub Title' => 'اللافتة الفرعية لقب',
  'Ex: Banner sub title' => 'على سبيل المثال: اللافتة الفرعية',
  'Full Banner Image' => 'صورة لافتة كاملة',
  'React Landing Page Discount Banner Image' => 'رد فعل لافتة خصم الصفحة المقصودة',
  'Discount Banner' => 'لافتة خصم',
  'Discount Banner Title' => 'عنوان لافتة الخصم',
  'Ex: Discount banner title' => 'على سبيل المثال: عنوان لافتة الخصم',
  'Discount Banner Sub Title' => 'عنوان لافتة الخصم الفرعي',
  'Ex: Discount banner sub title' => 'على سبيل المثال: عنوان Banner Sub Sub',
  'Discount Banner Image' => 'خصم صورة لافتة',
  'Half Banner Images' => 'صور نصف لافتة',
  'Banner' => 'لافتة',
  'Banner Image' => 'صورة بانر',
  '668 X 250 px' => '668 × 250 بكسل',
  'Ex: Feature title' => 'على سبيل المثال: عنوان الميزة',
  'Ex: Feature description' => 'على سبيل المثال: وصف الميزة',
  '140 X 140 px' => '140 × 140 بكسل',
  'Max_Number_of_Otp_Verification_in_a_Row' => 'الحد الأقصى لعدد التحقق من OTP على التوالي',
  'otp_verification_interval_time' => 'وقت التحقق من OTP',
  'Gst No' => 'ضريبة السلع والخدمات لا',
  'apple' => 'تفاحة',
  'team_id' => 'معرف الفريق',
  'key_id' => 'المعرف الرئيسي',
  'apple_api_set_instruction' => 'تعليمات تعيين API Apple',
  'goto_the_apple_developer_page' => 'goto صفحة مطور Apple',
  'Service Id file' => 'ملف معرف الخدمة',
  'Check Service Id file' => 'تحقق من ملف معرف الخدمة',
  'File Exists' => 'الملف موجود',
  'File not found' => 'لم يتم العثور على الملف',
  'redirect_url' => 'إعادة توجيه URL',
  'service_file' => 'ملف الخدمة',
  '(Already Exists)' => '(موجود أصلا)',
  'Go to Apple Developer page' => 'انتقل إلى صفحة مطور Apple',
  'Here in top left corner you can see the' => 'هنا في أعلى الزاوية اليسرى يمكنك رؤية',
  'Team ID' => 'معرف الفريق',
  '[Apple_Deveveloper_Account_Name - Team_ID]' => '[اسم حساب Apple Developer - معرف الفريق]',
  'Click Plus icon -  select App IDs -  click on Continue' => 'انقر فوق أيقونة - حدد معرفات التطبيق - انقر فوق متابعة',
  'Put a description and also identifier (identifier that used for app) and this is the' => 'ضع وصفًا ومعرفًا أيضًا (المعرف المستخدم للتطبيق) وهذا هو',
  'Client ID' => 'معرف العميل',
  'Click Continue and Download the file in device named AuthKey_ID.p8 (Store it safely and it is used for push notification)' => 'انقر فوق متابعة وتنزيل الملف في الجهاز المسماة Authkey ID.P8 (قم بتخزينه بأمان ويتم استخدامه لإخطار الدفع)',
  'Again click Plus icon -  select Service IDs -  click on Continue' => 'مرة أخرى ، انقر فوق أيقونة - حدد معرفات الخدمة - انقر فوق متابعة',
  'Push a description and also identifier and Continue' => 'ادفع وصفًا وأيضًا معرفًا ومتابعة',
  'Download the file in device named' => 'قم بتنزيل الملف في الجهاز المسمى',
  'AuthKey_KeyID.p8' => 'Authkey keyid.p8',
  '[This is the Service Key ID file and also after AuthKey_ that is the Key ID]' => '[هذا هو ملف معرف مفتاح الخدمة وأيضًا بعد Authkey هذا هو معرف المفتاح]',
  'This value is the minimum distance for a vehicle in this category to serve an order.' => 'هذه القيمة هي الحد الأدنى للمسافة للسيارة في هذه الفئة لتقديم طلب.',
  'Identity Documents' => 'وثائق الهوية',
  'Identity_Image' => 'صورة الهوية',
  'Registration request successfull' => 'طلب التسجيل ناجح',
  'Thank you for the joinning request on' => 'شكرا لك على طلب الانضمام على',
  'Your registration request is now pending' => 'طلب التسجيل الخاص بك معلق الآن',
  'Please wait untill admin aprroave your request' => 'الرجاء الانتظار حتى المسؤول aprove طلبك',
  'If you require any assistance or have feedback or suggestions about our site  you can email us at' => 'إذا كنت بحاجة إلى أي مساعدة أو لديك ملاحظات أو اقتراحات حول موقعنا ، يمكنك مراسلتي عبر البريد الإلكتروني',
  'can_not_add_both_food_and_restaurant_at_same_time' => 'لا يمكن إضافة كل من الطعام والمطعم في نفس الوقت',
  'login_to_your_panel' => 'تسجيل الدخول إلى اللوحة الخاصة بك',
  'select_your_role_&_login' => 'حدد دورك وتسجيل الدخول',
  'Verify' => 'يؤكد',
  'Change Password' => 'تغيير كلمة المرور',
  'New Password' => 'كلمة المرور الجديدة',
  'Confirm Password' => 'تأكيد كلمة المرور',
  'password_changed_successfully' => 'تم تغيير الرقم السري بنجاح',
  'select_role' => 'حدد الدور',
  'admin_employee' => 'موظف المشرف',
  'store_employee' => 'موظف المتجر',
  'Please_enter_a_valid_email_address.' => 'يرجى إدخال عنوان بريد إلكتروني صالح.',
  'Forget Password' => 'ننسى كلمة المرور',
  'Send_Mail_to_Your_Email' => 'أرسل بريدًا إلى بريدك الإلكتروني',
  'A mail will be send to your registered email with a  link to change passowrd' => 'سيتم إرسال بريد إلى بريدك الإلكتروني المسجل مع رابط لتغيير passowrd',
  'Send Mail' => 'ارسل بريد',
  'A mail has been sent to your registered email' => 'تم إرسال بريد إلى بريدك الإلكتروني المسجل',
  'Click the link in the mail description to change password' => 'انقر على الرابط في وصف البريد لتغيير كلمة المرور',
  'dm_cancel_order_hint' => 'تلميح أمر إلغاء DM',
  'You_must_select_your_role_before_login' => 'يجب عليك تحديد دورك قبل تسجيل الدخول',
  'Password_must_be_at_least_8_character_long' => 'يجب أن تكون كلمة المرور لمدة 8 أحرف على الأقل',
  'You_password_must_contain_at_least_one_uppercase_and_one_lowercase_letter' => 'يجب أن تحتوي كلمة المرور على أحرف كبيرة واحدة على الأقل وحرف صغيرة واحدة',
  'Password_must_contain_at_least_one_letter' => 'كلمة السر يجب أن تحتوي على حرف واحد على الأقل',
  'Password_must_contain_at_least_one_number' => 'يجب ان تحتوي كلمة المرور على الاقل رقما واحدا',
  'Password_must_contain_at_least_one_symbols' => 'يجب أن تحتوي كلمة المرور على رموز واحدة على الأقل',
  'Your_password_has_appeared_in_a_data_leak._Please_choose_a_different_password' => 'ظهرت كلمة المرور الخاصة بك في تسرب البيانات. الرجاء اختيار كلمة مرور مختلفة',
  'The password must contain at least one uppercase and one lowercase letter.' => 'يجب أن تحتوي كلمة المرور على أحرف كبيرة واحدة على الأقل وحرف صغيرة واحدة.',
  'The password must contain at least one letter.' => 'يجب أن تحتوي كلمة المرور على حرف واحد على الأقل.',
  'The password must contain at least one symbol.' => 'يجب أن تحتوي كلمة المرور على رمز واحد على الأقل.',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter _and_at_least_8_or_more_characters' => 'يجب أن يحتوي على رقم واحد على الأقل وحرفًا كبيرًا وصقلًا و 8 حرفًا على الأقل أو أكثر',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter_and_symbol _and_at_least_8_or_more_characters' => 'يجب أن تحتوي على رقم واحد على الأقل وحرفًا كبيرًا وصقلًا ورمزًا و 8 حرفًا على الأقل أو أكثر',
  'Ex: 8+ Character' => 'على سبيل المثال: 8+ حرف',
  'The password and confirm password must match.' => 'يجب أن تتطابق كلمة المرور وتأكيد كلمة المرور.',
  'The given password has appeared in a data leak. Please choose a different password.' => 'ظهرت كلمة المرور المحددة في تسرب البيانات. الرجاء اختيار كلمة مرور مختلفة.',
  'The confirm password field is required.' => 'حقل تأكيد كلمة المرور مطلوب.',
  'The phone must be at least 10 characters.' => 'يجب أن يكون الهاتف 10 أحرف على الأقل.',
  'Save' => 'يحفظ',
  'link_expired' => 'انتهت صلاحية الرابط',
  'maximum_delivery_charge' => 'أقصى رسوم توصيل',
  'Credentials does not match.' => 'بيانات الاعتماد لا تتطابق.',
  'language_list' => 'قائمة اللغة',
  'english_value' => 'القيمة الإنجليزية',
  'translated_value' => 'القيمة المترجمة',
  'minimum_delivery_time_should_be_more_than_10_min' => 'يجب أن يكون الحد الأدنى لوقت التسليم أكثر من 10 دقائق',
  'Password_Reset' => 'إعادة تعيين كلمة المرور',
  'Change_password_request' => 'تغيير طلب كلمة المرور',
  'hi' => 'أهلاً',
  'Please_click' => 'من فضلك اضغط',
  'or_click_the_link_below_to_change_your_password' => 'أو انقر فوق الرابط أدناه لتغيير كلمة المرور الخاصة بك',
  'Please_contact_us_for_any_queries_ _weâre_always_happy_to_help.' => 'يرجى الاتصال بنا للحصول على أي استفسارات يسعدنا دائمًا المساعدة.',
  'Thanks_&_Regards_ ' => 'مع الشكر و التقدير',
  '* When this discount is available  is applied on all the items in this stores.' => '* عندما يتوفر هذا الخصم على جميع العناصر في هذه المتاجر.',
  'duplicate_email_or_phone_exists_at_the_database' => 'يوجد بريد إلكتروني أو هاتف مكرر في قاعدة البيانات',
  'food_variations_generator' => 'مولد الاختلافات الغذائية',
  'generate' => 'يولد',
  'Import' => 'يستورد',
  'You_want_to_' => 'اتريد',
  'Please click save information button below to save all the changes' => 'يرجى النقر فوق زر حفظ المعلومات أدناه لحفظ جميع التغييرات',
  'Customer Can Earn & Buy From Wallet' => 'يمكن للعميل كسب وشراء من محفظة',
  'lorem_ipsum_hint' => 'لوريم تلميح جدا',
  'Referral Earning' => 'كسب الإحالة',
  'Customer Can Earn & Buy From Referral' => 'يمكن للعميل كسب وشراء من الإحالة',
  'Earning Per Referral ($)' => 'كسب لكل إحالة ($)',
  'Customer Verification' => 'التحقق من العملاء',
  'Loyalty Point' => 'نقطة الولاء',
  'Customer Can Earn Loyalty Point' => 'يمكن للعميل كسب نقطة الولاء',
  'Loyalty Point Earn Per Order (%)' => 'نقطة الولاء كسب لكل طلب (٪)',
  'Minimum Loyalty Point Required to Transfer' => 'الحد الأدنى لنقطة الولاء المطلوبة للنقل',
  'Save Information' => 'حفظ المعلومات',
  'By Turning OFF ' => 'عن طريق إيقاف تشغيل',
  'Refund to Wallet' => 'استرداد المحفظة',
  'Option' => 'خيار',
  'Customer will no see wallet option from his profile settings' => 'لن يرى العميل خيار محفظة من إعدادات ملفه الشخصي',
  'By Turning ON ' => 'عن طريق التشغيل',
  'Customer will see wallet option in his profile settings & can earn & send money from his wallet money' => 'سيرى العميل خيار محفظة في إعدادات ملفه الشخصي ويمكنه كسب وإرسال أموال من أموال محفظته',
  'Ok' => 'نعم',
  'Cancel' => 'يلغي',
  'Wallet Earning' => 'كسب محفظة',
  'Customer will not get any option to share code and earn' => 'لن يحصل العميل على أي خيار لتبادل الرمز والكسب',
  'Customer can share his refer code to other & when other people will login while using this code & place their first order delivered. customer will earn wallet money.' => 'يمكن للعميل مشاركة رمز المراجع إلى الآخرين ومتى سيقوم الآخرون بتسجيل الدخول أثناء استخدام هذا الرمز ووضع طلبه الأول. سوف يكسب العميل أموال محفظة.',
  'Customer will no see loyalty point option from his profile settings' => 'لن يرى العميل خيار نقطة الولاء من إعدادات ملفه الشخصي',
  'Customer will see loyalty point option in his profile settings & can earn & convert this point to wallet money' => 'سيرى العميل خيار نقطة الولاء في إعدادات ملفه الشخصي ويمكنه كسب وتحويل هذه النقطة إلى محفظة أموال',
  'Customers do not need to verify their number through OTP while sign up. They can directly create account.' => 'لا يحتاج العملاء إلى التحقق من عددهم من خلال OTP أثناء التسجيل. يمكنهم إنشاء حساب مباشرة.',
  'Customers have verify their number through OTP while sign up' => 'يتحقق العملاء من عددهم من خلال OTP أثناء التسجيل',
  'Show Earnings in App' => 'إظهار الأرباح في التطبيق',
  'Maximum Assigned Order Limit' => 'الحد الأقصى المخصص للطلب',
  'Delivery Man Tips' => 'نصائح رجل التسليم',
  'Customer will not be able give tips from checkout page to the delivery man while placing order' => 'لن يتمكن العميل من تقديم نصائح من صفحة الخروج إلى رجل التسليم أثناء تقديم الطلب',
  'Customer will be able give tips from checkout page to the delivery man while placing order' => 'سيتمكن العميل من تقديم نصائح من صفحة الخروج إلى رجل التسليم أثناء تقديم الطلب',
  'Show Earnings in Apps' => 'إظهار الأرباح في التطبيقات',
  'If this feature is off  delivery man canât see his earning from each order in order details from his app' => 'إذا كانت هذه الميزة خارج التسليم ، فلا يمكنك رؤية ربحه من كل طلب من خلال تفاصيل الطلب من تطبيقه',
  'If this feature is on  delivery man can see his earning from each order in order details from his app' => 'إذا كانت هذه الميزة على التسليم ، يمكن أن يرى رجل ربحه من كل طلب من أجل تفاصيل تطبيقه من تطبيقه',
  'Delivery Man Self Registration' => 'التسليم رجل التسليم الذاتي',
  'User canât register  him self as a delivery man from user website  or Delivery Man App. He has to contact admin & request create an account for him' => 'لا يمكن للمستخدم تسجيله بنفسه كرجل توصيل من موقع المستخدم أو تطبيق MANAR. يجب عليه الاتصال بالمشرف وطلب إنشاء حساب له',
  'User can register  him self as a delivery man from user website  or Delivery Man App' => 'يمكن للمستخدم تسجيله بنفسه كرجل توصيل من موقع المستخدم أو تطبيق التسليم',
  'Store Self Registration' => 'تخزين التسجيل الذاتي',
  'User canât register to open a store all by him self from user website or Store App . He has to contact admin & request to open a store' => 'لا يستطيع المستخدم التسجيل لفتح متجر من قبله من موقع المستخدم أو تطبيق المتجر. عليه الاتصال بالمسؤول ويطلب فتح متجر',
  'User can register to open a store all by him self from user website or Store App ' => 'يمكن للمستخدم التسجيل لفتح متجر من قبله من موقع المستخدم أو تطبيق المتجر',
  'lorem ipsum' => 'لوريم Ipsum',
  'Takeaway' => 'يبعد',
  'Schedule Order Time Interval' => 'جدولة فاصل زمني أمر',
  'Order Delivery Verification' => 'طلب تسليم',
  'Customer will not get any code and delivery man does not need the code to complete the order process' => 'لن يحصل العميل على أي رمز ولا يحتاج رجل التسليم إلى الرمز لإكمال عملية الطلب',
  'While delivery man handover the order to customer  a OTP will be send to customer website / app. The delivery man have to use the code to complete delivery process.' => 'أثناء توصيل رجل تسليم الطلب إلى العميل ، سيتم إرسال OTP إلى موقع / تطبيق العميل. يتعين على رجل التسليم استخدام الرمز لإكمال عملية التسليم.',
  'Order Using Prescription' => 'اطلب باستخدام الوصفة الطبية',
  'place order by uploading prescription will be off in customer app / website' => 'سيتم إيقاف الطلب عن طريق تحميل الوصفات الطبية في تطبيق / موقع العميل',
  'While order from pharmacy module   customer can just upload prescription and place order' => 'بينما يمكن للعميل من وحدة الصيدلة أن يحمل الوصفة وطلب وضعه فقط',
  'Customer will not able to use home delivery option' => 'لن يتمكن العميل من استخدام خيار توصيل المنازل',
  'Customer will able to use home delivery option' => 'سيتمكن العميل من استخدام خيار توصيل المنازل',
  'Customer will not able to use takeaway option.' => 'لن يتمكن العميل من استخدام خيار الوجبات الجاهزة.',
  'Customer will able to use takeaway option.' => 'سيتمكن العميل من استخدام خيار الوجبات الجاهزة.',
  'Schedule Order' => 'أمر الجدول',
  'After turning off this field customers wonât able to use schedule order.' => 'بعد إيقاف تشغيل هذا الحقل ، لن يتمكن العملاء من استخدام طلب الجدول.',
  'After turning on this field customers are able to use schedule order.' => 'بعد تشغيل هذا الحقل ، يمكن للعملاء استخدام طلب الجدول.',
  'Order Cancelation Messages' => 'طلب رسائل إلغاء',
  'Order Cancellation Reason' => 'سبب الإلغاء',
  'User Type' => 'نوع المستخدم',
  '3:1' => '3:1',
  'Favicon' => 'فافيكون',
  '1:1' => '1:1',
  'General Settings' => 'الاعدادات العامة',
  'Left' => 'غادر',
  'Right' => 'يمين',
  'Copyright Text' => 'نص حقوق الطبع والنشر',
  'business settings' => 'إعدادات الأعمال',
  'Veg/Non Veg' => 'الخضار/غير الخضار',
  'Store & Customer both will not see veg / non-veg tag with food and canât search according to the tags from website & apps' => 'لن يرى كل من المتجر والعميل علامة الخضار / غير الخضار مع الطعام ولا يمكن البحث وفقًا للعلامات من موقع الويب والتطبيقات',
  'Store & Customer both can see veg / non-veg tag with food and can search according to the tags from website & apps' => 'يمكن للمتجر والعميل على حد سواء رؤية علامة الخضار / غير الخضار مع الطعام ويمكنه البحث وفقًا للعلامات من موقع الويب والتطبيقات',
  'Include Tax Amount' => 'تشمل المبلغ الضريبي',
  'Tax will be shown separately from product / item prices' => 'سيتم عرض الضريبة بشكل منفصل عن أسعار المنتج / العناصر',
  'Product / item price will be show as the summation of product price & tax' => 'سيتم عرض سعر المنتج / العنصر على أنه تجميع أسعار المنتج والضرائب',
  'Free Delivery Over' => 'توصيل مجاني',
  'Free delivery over a certain order amount feature will not work' => 'لن تعمل ميزة التوصيل المجاني عبر مبلغ معين',
  'After a certain order amount decided by admin   the delivery charge will be consider free for customer. & The delivery fee will be deduct from admin wallet' => 'بعد مبلغ طلب معين من قبل المسؤول ، سيتم النظر في رسوم التوصيل مجانًا للعميل. وسيتم خصم رسوم التسليم من محفظة المشرف',
  'Confirmation' => 'تأكيد',
  'Are you sure you want to turn on self-registration for stores ' => 'هل أنت متأكد أنك تريد تشغيل التسجيل الذاتي للمتاجر',
  'store_setup' => 'تخزين الإعداد',
  'order_cancellation_reason_updated_successfully' => 'تم تحديث سبب إلغاء الطلب بنجاح',
  'Payment Methods' => 'طرق الدفع',
  'SMS Module' => 'وحدة SMS',
  'Mail Config' => 'تكوين البريد',
  'Map APIs' => 'خريطة واجهات برمجة التطبيقات',
  'Social Logins' => 'تسجيلات تسجيلات اجتماعية',
  'Recaptcha' => 'recaptcha',
  'Without configuring this section map functionality will not work properly. Thus the whole system will not work as it planned' => 'دون تكوين وظيفة خريطة القسم لن تعمل بشكل صحيح. وبالتالي لن يعمل النظام بأكمله كما خطط',
  'By Turning OFF Cash On Delivery Option' => 'عن طريق إيقاف النقد على خيار التسليم',
  'Customers will not be able to select COD as a payment method during checkout. Please review your settings and enable COD if you wish to offer this payment option to customers.' => 'لن يتمكن العملاء من تحديد COD كطريقة دفع أثناء الخروج. يرجى مراجعة الإعدادات الخاصة بك وتمكين COD إذا كنت ترغب في تقديم خيار الدفع هذا للعملاء.',
  'Send Test Mail' => 'إرسال بريد الاختبار',
  'How it Works' => 'كيف تعمل',
  'Turn OFF' => 'أطفأ',
  '*By Turning OFF mail configuration  all your mailing services will be off.' => '*عن طريق إيقاف تشغيل تكوين البريد ، سيتم إيقاف تشغيل جميع خدماتك البريدية.',
  'Important!' => 'مهم!',
  'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.' => 'سيسمح تمكين خدمات تكوين البريد للنظام بإرسال رسائل البريد الإلكتروني. يرجى التأكد من أنك قمت بتكوين إعدادات SMTP بشكل صحيح لتجنب المشكلات المحتملة مع تسليم البريد الإلكتروني.',
  'Warning!' => 'تحذير!',
  'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.' => 'سيمنع تعطيل خدمات تكوين البريد النظام من إرسال رسائل البريد الإلكتروني. يرجى إيقاف تشغيل هذه الخدمة فقط إذا كنت تنوي تعليق إرسال البريد الإلكتروني مؤقتًا. لاحظ أن هذا قد يؤثر على وظائف النظام التي تعتمد على اتصال البريد الإلكتروني.',
  'Congratulations! Your SMTP mail has been setup successfully!' => 'تهانينا! تم إعداد بريد SMTP الخاص بك بنجاح!',
  'Go to test mail to check that its work perfectly or not!' => 'انتقل إلى اختبار البريد للتحقق من أن عمله تمامًا أم لا!',
  'Send a Test Mail to Your Email   ' => 'إرسال بريد اختبار إلى بريدك الإلكتروني',
  'A test mail will be send to your email to confirm it works perfectly.' => 'سيتم إرسال بريد الاختبار إلى بريدك الإلكتروني لتأكيد أنه يعمل بشكل مثالي.',
  'Find SMTP Server Details' => 'ابحث عن تفاصيل خادم SMTP',
  'Contact your email service provider or IT administrator to obtain the SMTP server details  such as hostname  port  username  and password.' => 'اتصل بمزود خدمة البريد الإلكتروني الخاص بك أو مسؤول تكنولوجيا المعلومات للحصول على تفاصيل خادم SMTP مثل اسم المستخدم وكلمة مرور اسم Port.',
  'Note: If you re not sure where to find these details  check the email provider s documentation or support resources for guidance.' => 'ملاحظة: إذا لم تكن متأكدًا من مكان العثور على هذه التفاصيل ، تحقق من وثائق موفر البريد الإلكتروني أو موارد الدعم للتوجيه.',
  'Configure SMTP Settings' => 'تكوين إعدادات SMTP',
  'Go to the SMTP mail setup page in the admin panel.' => 'انتقل إلى صفحة إعداد بريد SMTP في لوحة المسؤول.',
  'Enter the obtained SMTP server details  including the hostname  port  username  and password.' => 'أدخل تفاصيل خادم SMTP التي تم الحصول عليها بما في ذلك اسم المستخدم وكلمة المرور من منفذ اسم المضيف.',
  'Choose the appropriate encryption method (e.g.  SSL  TLS) if required. Save the settings.' => 'اختر طريقة التشفير المناسبة (مثل SSL TLS) إذا لزم الأمر. احفظ الإعدادات.',
  'Test SMTP Connection' => 'اختبار اتصال SMTP',
  'Click on the  Send Test Mail  button to verify the SMTP connection.' => 'انقر فوق زر إرسال بريد الإرسال للتحقق من اتصال SMTP.',
  'If successful  you will see a confirmation message indicating that the connection is working fine.' => 'إذا نجحت ، فسترى رسالة تأكيد تشير إلى أن الاتصال يعمل بشكل جيد.',
  'If not  double-check your SMTP settings and try again.' => 'إذا لم يكن التحقق من إعدادات SMTP الخاصة بك وحاول مرة أخرى.',
  'Note: If you re unsure about the SMTP settings  contact your email service provider or IT administrator for assistance.' => 'ملاحظة: إذا لم تكن متأكدًا من إعدادات SMTP ، فاتصل بمزود خدمة البريد الإلكتروني أو مسؤول تكنولوجيا المعلومات للحصول على المساعدة.',
  'Enable Mail Configuration' => 'تمكين تكوين البريد',
  'If the SMTP connection test is successful  you can now enable the mail configuration services by toggling the switch to  ON. ' => 'إذا نجح اختبار اتصال SMTP ، فيمكنك الآن تمكين خدمات تكوين البريد عن طريق تبديل التبديل إلى ON.',
  'This will allow the system to send emails using the configured SMTP settings.' => 'سيسمح ذلك للنظام بإرسال رسائل البريد الإلكتروني باستخدام إعدادات SMTP التي تم تكوينها.',
  'Got It' => 'فهمتها',
  'Google Map API Setup' => 'إعداد API MAP Google',
  'system will not work as it planne' => 'دون تكوين هذا القسم',
  'Please go to settings and select module for this zone' => 'يرجى الانتقال إلى الإعدادات وتحديد الوحدة النمطية لهذه المنطقة',
  'Otherwise this zone won t function properly & will work show anything against this zone' => 'وإلا فإن هذه المنطقة لن تعمل بشكل صحيح وستعمل على عرض أي شيء ضد هذه المنطقة',
  'Facebook Login Turned Off' => 'تم إيقاف تسجيل الدخول إلى Facebook',
  'Facebook is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'تم تعطيل Facebook الآن. لن يتمكن العملاء من التسجيل أو تسجيل الدخول باستخدام حسابات وسائل التواصل الاجتماعي الخاصة بهم. يرجى ملاحظة أن هذا قد يؤثر على تجربة المستخدم وعملية التسجيل/تسجيل الدخول.',
  'reCAPTCHA is now enabled for added security. Users may be prompted to complete a reCAPTCHA challenge to verify their human identity and protect against spam and malicious activity.' => 'تم الآن تمكين Recaptcha لمزيد من الأمان. قد يُطلب من المستخدمين إكمال تحد recaptcha للتحقق من هويتهم الإنسانية والحماية من البريد العشوائي والنشاط الخبيث.',
  'Credential Setup' => 'إعداد الاعتماد',
  'Reset' => 'إعادة ضبط',
  'Disabling reCAPTCHA may leave your website vulnerable to spam and malicious activity and suspects that a user may be a bot. It is highly recommended to keep reCAPTCHA enabled to ensure the security and integrity of your website.' => 'قد يترك تعطيل Recaptcha موقع الويب الخاص بك عرضة للبريد العشوائي والنشاط الخبيث ويشتبه في أن المستخدم قد يكون روبوتًا. يوصى بشدة بالحفاظ على تمكين RecaptCha لضمان أمان وسلاسة موقع الويب الخاص بك.',
  'Push Notification' => 'إشعار دفع',
  'Firebase Configuration' => 'تكوين Firebase',
  'Read Documentation' => 'قراءة الوثائق',
  'Where to get this information' => 'من أين تحصل على هذه المعلومات',
  '*Select Module Here' => '*حدد الوحدة هنا',
  'Write your message' => 'اكتب رسالتك',
  'Ex: AAAAaBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789' => 'على سبيل المثال: AAAAABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
  'Ex: abcd1234efgh5678ijklmnop90qrstuvwxYZ' => 'EX: ABCD1234EFGH5678IJKLMNOP90QRSTUVWXYZ',
  'Ex: my-awesome-app-12345' => 'على سبيل المثال: My-Awesome-App-12345',
  'Ex: my-awesome-app.firebaseapp.com' => 'على سبيل المثال: My-Awesome-app.firebaseapp.com',
  'Ex: my-awesome-app.appspot.com' => 'على سبيل المثال: My-Awesome-app.appspot.com',
  'Ex: 1234567890' => 'على سبيل المثال: 1234567890',
  'Ex: 9876543210' => 'على سبيل المثال: 9876543210',
  'Ex: F-12345678' => 'على سبيل المثال: F-12345678',
  'Go to Firebase Console' => 'اذهب إلى وحدة التحكم في Firebase',
  'Open your web browser and go to the Firebase Console' => 'افتح متصفح الويب الخاص بك وانتقل إلى وحدة التحكم في Firebase',
  '(https://console.firebase.google.com/)' => '(https://console.firebase.google.com/)',
  'Select the project for which you want to configure FCM from the Firebase Console dashboard.' => 'حدد المشروع الذي تريد تكوين FCM من Firebase Console Dashboard.',
  'Navigate to Project Settings' => 'انتقل إلى إعدادات المشروع',
  'In the left-hand menu  click on the  Settings  gear icon  and then select  Project settings  from the dropdown.' => 'في القائمة اليسرى ، انقر فوق أيقونة إعدادات الإعدادات ، ثم حدد إعدادات المشروع من القائمة المنسدلة.',
  'In the Project settings page  click on the  Cloud Messaging  tab from the top menu.' => 'في صفحة إعدادات المشروع ، انقر فوق علامة تبويب المراسلة السحابية من القائمة العليا.',
  'Obtain All The Information Asked!' => 'الحصول على جميع المعلومات المطلوبة!',
  'In the Firebase Project settings page  click on the  General  tab from the top menu.' => 'في صفحة إعدادات مشروع Firebase ، انقر فوق علامة التبويب العامة من القائمة العليا.',
  'Under the  Your apps  section  click on the  Web  app for which you want to configure FCM.' => 'ضمن قسم التطبيقات الخاصة بك ، انقر على تطبيق الويب الذي تريد تكوين FCM.',
  'Then Obtain API Key  FCM Project ID  Auth Domain  Storage Bucket  Messaging Sender ID.' => 'ثم الحصول على مفتاح API FCM معرف مشروع AUTH DOLIAN DOLAIN DEVORCET SESSAGED ID.',
  'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation  terms of service  and any applicable laws and regulations.' => 'ملاحظة: يرجى التأكد من استخدام المعلومات التي تم الحصول عليها بشكل آمن ووفقًا لشروط خدمة Firebase و FCM وأي قوانين ولوائح قابلة للتطبيق.',
  'Please Visit the Docs to Set FCM on Mobile Apps' => 'يرجى زيارة المستندات لتعيين FCM على تطبيقات الهاتف المحمول',
  'Please check the documentation below for detailed instructions on setting up your mobile app to receive Firebase Cloud Messaging (FCM) notifications.' => 'يرجى التحقق من الوثائق أدناه للحصول على إرشادات مفصلة عند إعداد تطبيق الهاتف المحمول الخاص بك لتلقي الإخطارات السحابية Firebase (FCM).',
  'Click Here' => 'انقر هنا',
  'Facebook is Disabled!' => 'تم تعطيل Facebook!',
  'Customers will be able to select COD as a payment method during checkout. Please review your settings to ensure proper configuration and availability of COD as a payment option.' => 'سيتمكن العملاء من اختيار COD كطريقة دفع أثناء الخروج. يرجى مراجعة الإعدادات الخاصة بك لضمان التكوين المناسب وتوافر COD كخيار للدفع.',
  'Facebook is Enabled' => 'تم تمكين Facebook',
  'Turned ON ' => 'تشغيل',
  'Turned OFF ' => 'أطفئ',
  'is now enabled. Customers will be able to sign up or log in using their social media accounts.' => 'تم تمكينه الآن. سيتمكن العملاء من التسجيل أو تسجيل الدخول باستخدام حسابات وسائل التواصل الاجتماعي الخاصة بهم.',
  'is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'تم تعطيله الآن. لن يتمكن العملاء من التسجيل أو تسجيل الدخول باستخدام حسابات وسائل التواصل الاجتماعي الخاصة بهم. يرجى ملاحظة أن هذا قد يؤثر على تجربة المستخدم وعملية التسجيل/تسجيل الدخول.',
  'Login Turned ON ' => 'تم تشغيل تسجيل الدخول',
  'Login Turned OFF ' => 'إيقاف تشغيل تسجيل الدخول',
  'Login is now enabled. Customers will be able to sign up or log in using their social media accounts.' => 'تم الآن تمكين تسجيل الدخول. سيتمكن العملاء من التسجيل أو تسجيل الدخول باستخدام حسابات وسائل التواصل الاجتماعي الخاصة بهم.',
  'Login is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'تم تعطيل تسجيل الدخول الآن. لن يتمكن العملاء من التسجيل أو تسجيل الدخول باستخدام حسابات وسائل التواصل الاجتماعي الخاصة بهم. يرجى ملاحظة أن هذا قد يؤثر على تجربة المستخدم وعملية التسجيل/تسجيل الدخول.',
  'Customer will not see wallet option from his profile settings' => 'لن يرى العميل خيار محفظة من إعدادات ملفه الشخصي',
  'How the Setting Works' => 'كيف يعمل الإعداد',
  'Check how the settings works' => 'تحقق من كيفية عمل الإعدادات',
  'View Image' => 'عرض الصورة',
  'Copy Link' => 'نسخ الوصلة',
  'Copy Path' => 'مسار نسخ',
  'User App Version Control' => 'التحكم في إصدار تطبيق المستخدم',
  'For android' => 'لأجهزة الأندرويد',
  'For iOS' => 'لنظام التشغيل iOS',
  'What is App Version  ' => 'ما هو إصدار التطبيق',
  'App Download Link' => 'رابط تنزيل التطبيق',
  'By Turning OFF Order ' => 'عن طريق إيقاف الطلب',
  'Pending Message' => 'رسالة معلقة',
  'User can t get a clear message to know that order is pending or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب معلق أم لا',
  'By Turning ON Order ' => 'عن طريق التشغيل',
  'User will get a clear message to know that order is pending' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب معلق',
  'User can`t get a clear message to know that order is pending or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب معلق أم لا',
  'User can not get a clear message to know that order is pending or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب معلق أم لا',
  'pending Message' => 'رسالة معلقة',
  'confirmation Message' => 'رسالة تأكيد',
  'User will get a clear message to know that order is confirmed' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم تأكيده',
  'User can not get a clear message to know that order is confirmed or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الأمر تم تأكيده أو لا',
  'processing Message' => 'رسالة معالجة',
  'User will get a clear message to know that order is processing' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الترتيب يعالج',
  'User can not get a clear message to know that order is processing or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب هو معالجة أم لا',
  'Order Handover Message' => 'طلب رسالة تسليم',
  'User will get a clear message to know that order is handovered' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم تسليمه',
  'User can not get a clear message to know that order is handovered or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب يتم تسليمه أم لا',
  'Out For Delivery Message' => 'خارج رسالة التسليم',
  'User will get a clear message to know that order is out for delivery' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب قد تم تسليمه',
  'User can not get a clear message to know that order is out for delivery or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب خارج للتسليم أو لا',
  'delivered Message' => 'تسليم رسالة',
  'User will get a clear message to know that order is delivered' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم تسليمه',
  'User can not get a clear message to know that order is delivered or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب يتم تسليمه أم لا',
  'Delivery Man Assigned Message' => 'رسالة مخصصة رجل التسليم',
  'User will get a clear message to know that order is delivery man assigned' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب هو مخصص رجل التسليم',
  'User can not get a clear message to know that order is delivery man assigned or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب هو مخصص رجل أو لا',
  'User will get a clear message to know that order is assigned to delivery man' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم تعيينه لرجل التسليم',
  'User can not get a clear message to know that order is assigned to delivery man or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب يتم تعيينه لرجل التسليم أم لا',
  'Delivery Man Delivered Message' => 'تسليم رجل التسليم رسالة',
  'User will get a clear message to know that order is delivered by delivery man' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم تسليمه بواسطة رجل التسليم',
  'User can not get a clear message to know that order is delivered by delivery man or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب يتم تسليمه عن طريق التسليم أو لا',
  'canceled Message' => 'الرسالة الملغاة',
  'User will get a clear message to know that order is canceled' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب قد تم إلغاؤه',
  'User can not get a clear message to know that order is canceled or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب قد تم إلغاؤه أو لا',
  'Order Refund Message' => 'طلب رسالة استرداد',
  'User will get a clear message to know that order is refunded' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن الطلب يتم استرداده',
  'User can not get a clear message to know that order is refunded or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن الطلب يتم استرداده أم لا',
  'Refund Request Cancel Message' => 'طلب استرداد رسالة إلغاء',
  'User will get a clear message to know that orders refund request canceled' => 'سيحصل المستخدم على رسالة واضحة لمعرفة أن طلبات استرداد الطلبات قد تم إلغاؤها',
  'User can not get a clear message to know that orders refund request canceled or not' => 'لا يمكن للمستخدم الحصول على رسالة واضحة لمعرفة أن طلبات استرداد الطلبات قد تم إلغاؤها أم لا',
  'Customers will be able to select COD as a payment method during checkout.' => 'سيتمكن العملاء من اختيار COD كطريقة دفع أثناء الخروج.',
  'By Turning ON Cash On Delivery Option' => 'عن طريق تشغيل نقود على خيار التسليم',
  'By Turning ON Digital Payment Option' => 'عن طريق تشغيل خيار الدفع الرقمي',
  'By Turning OFF Digital Payment Option' => 'عن طريق إيقاف خيار الدفع الرقمي',
  'Customers will not be able to select digital payment as a payment method during checkout. Please review your settings and enable digital payment if you wish to offer this payment option to customers.' => 'لن يتمكن العملاء من اختيار الدفع الرقمي كوسيلة دفع أثناء الخروج. يرجى مراجعة الإعدادات الخاصة بك وتمكين الدفع الرقمي إذا كنت ترغب في تقديم خيار الدفع هذا للعملاء.',
  'Customers will be able to select digital payment as a payment method during checkout.' => 'سيتمكن العملاء من اختيار الدفع الرقمي كوسيلة دفع أثناء الخروج.',
  'Don t show this anymore' => 'لا تظهر هذا بعد الآن',
  'I will do it later' => 'انا سوف اقوم بها لاحقا',
  'Go to the Settings' => 'انتقل إلى الإعدادات',
  'By switching the status to âONâ   this zone and under all the functionality of this zone will be turned on' => 'عن طريق تغيير الحالة إلى Â على هذه المنطقة وتحت كل وظائف هذه المنطقة سيتم تشغيلها',
  'In the user app & website all stores & products  already assigned under this zone will show to the customers' => 'في تطبيق المستخدم وموقع الويب ، سيتم عرض جميع المتاجر والمنتجات المعينة بالفعل تحت هذه المنطقة للعملاء',
  'By switching the status off  this zone and under all the functionality of this zone will be turned off' => 'عن طريق إيقاف تشغيل الحالة خارج هذه المنطقة وتحت كل وظائف هذه المنطقة سيتم إيقاف تشغيلها',
  'In the user app & website all stores & products  already assigned under this zone will show to the customers.' => 'في تطبيق المستخدم وموقع الويب ، سيتم عرض جميع المتاجر والمنتجات المعينة بالفعل تحت هذه المنطقة للعملاء.',
  'In the user app & website no stores & products  already assigned under this zon this zone will not show to the customers.' => 'في تطبيق المستخدم وموقع الويب ، لن يتم عرض أي متاجر ومنتجات تم تعيينها بالفعل بموجب هذه المنطقة على العملاء.',
  'Add New Module' => 'أضف وحدة جديدة',
  'How does it works  ' => 'كيف يعمل',
  'Module Status' => 'حالة الوحدة',
  'is now enabled. You can use its features and functionality.' => 'تم تمكينه الآن. يمكنك استخدام ميزاتها ووظائفها.',
  'currently disabled. You can enable it in the settings to access its features and functionality.' => 'تعطيل حاليا. يمكنك تمكينه في الإعدادات للوصول إلى ميزاتها ووظائفها.',
  'module is now enabled. You can use its features and functionality.' => 'تم تمكين الوحدة النمطية الآن. يمكنك استخدام ميزاتها ووظائفها.',
  'module currently disabled. You can enable it in the settings to access its features and functionality.' => 'وحدة تعطيل حاليا. يمكنك تمكينه في الإعدادات للوصول إلى ميزاتها ووظائفها.',
  'admin_landing_page' => 'صفحة الإدارة المقصودة',
  'admin_landing_pages' => 'صفحات هبوط المشرف',
  'fixed_data' => 'بيانات ثابتة',
  'promotional_section' => 'القسم الترويجي',
  'feature_list' => 'قائمة الميزات',
  'earn_money' => 'يكتسب نقود',
  'download_apps' => 'تنزيل التطبيقات',
  'testimonials' => 'الشهادات - التوصيات',
  'contact_us_page' => 'اتصل بنا',
  'header_section' => 'قسم الرأس',
  'title_here...' => 'العنوان هنا ...',
  'Sub Title' => 'العنوان الفرعي',
  'sub_title_here...' => 'العنوان الفرعي هنا ...',
  'module_list_section' => 'قسم قائمة الوحدة النمطية',
  'NB: Client key should have enable map javascript api and you can restrict it with http refere. Server key should have enable place api key and you can restrict it with ip. You can use same api for both field without any restrictions.' => 'NB: يجب أن يكون لمفتاح العميل تمكين خريطة javaScript API ويمكنك تقييده مع HTTP Rejustere. يجب أن يكون لمفتاح الخادم مفتاح Place API ويمكنك تقييده باستخدام IP. يمكنك استخدام نفس API لكلا الحقل دون أي قيود.',
  'Referral & Earning' => 'الإحالة والكسب',
  'To Change the illustrations & primary colour please change primary colour according to the ' => 'لتغيير الرسوم التوضيحية واللون الأساسي ، يرجى تغيير اللون الأساسي وفقًا لـ',
  '(size: 3:1)' => '(الحجم: 3: 1)',
  'Promotional Banners' => 'لافتات ترويجية',
  'Search by ID or name' => 'ابحث عن طريق الهوية أو الاسم',
  'By Turning OFF Promotional Banner Section' => 'عن طريق إيقاف تشغيل قسم الراية الترويجية',
  'Promotional banner will be disabled. You will be unable to see promotional activity' => 'سيتم تعطيل لافتة ترويجية. لن تتمكن من رؤية النشاط الترويجي',
  'By Turning ON Promotional Banner Section' => 'عن طريق تشغيل قسم الراية الترويجية',
  'Promotional banner will be enabled. You will be able to see promotional activity' => 'سيتم تمكين لافتة ترويجية. ستتمكن من رؤية النشاط الترويجي',
  'Feature Title & Short Description' => 'عنوان الميزة ووصف قصير',
  'Section View' => 'عرض القسم',
  'short description' => 'وصف قصير',
  'Feature List' => 'قائمة الميزات',
  'Feature List Section' => 'قسم قائمة الميزات',
  'Feature list will be disabled. You can enable it in the settings to access its features and functionality' => 'سيتم تعطيل قائمة الميزات. يمكنك تمكينه في الإعدادات للوصول إلى ميزاتها ووظائفها',
  'Feature list is enabled. You can now access its features and functionality' => 'يتم تمكين قائمة الميزات. يمكنك الآن الوصول إلى ميزاتها ووظائفها',
  'Download User App Section Content ' => 'قم بتنزيل محتوى قسم تطبيق المستخدم',
  'Download Seller App Section' => 'تنزيل قسم تطبيق البائع',
  'Playstore Button' => 'زر بلايستور',
  'Download Link' => 'رابط التحميل',
  'Ex: https://play.google.com/store/apps' => 'على سبيل المثال: https://play.google.com/store/apps',
  'App Store Button' => 'زر متجر التطبيق',
  'Ex: https://www.apple.com/app-store/' => 'على سبيل المثال: https://www.apple.com/app-store/',
  'Download Delivery Man App Section' => 'قسم تطبيق تنزيل التسليم',
  'Admin Earn Money' => 'المسؤول كسب المال',
  'Download Delivery Man App Section ' => 'قسم تطبيق تنزيل التسليم',
  'App Store Button Disabled for Seller' => 'زر متجر التطبيقات معطل للبائع',
  'App Store button is enabled now everyone can use or see the button' => 'تم تمكين زر متجر التطبيقات الآن يمكن للجميع استخدامه أو رؤية الزر',
  'App Store Button Enabled for Seller' => 'ممكّن زر متجر التطبيقات للبائع',
  'App Store button is disabled now no one can use or see the button' => 'تم تعطيل زر متجر التطبيقات الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'Playstore Button Disabled for Seller' => 'تعطيل زر Playstore للبائع',
  'Playstore button is disabled now no one can use or see the button' => 'تم تعطيل زر PlayStore الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'Playstore Button Enabled for Seller' => 'تمكين زر Playstore للبائع',
  'Playstore button is enabled now everyone can use or see the button' => 'تم تمكين زر PlayStore الآن يمكن للجميع استخدامه أو رؤية الزر',
  'App Store Button Disabled for Deliveryman' => 'زر متجر التطبيق معطل للتسليم',
  'App Store Button Enabled for Deliveryman' => 'ممكّن زر متجر التطبيقات للتسليم',
  'Ex: Title of the section' => 'على سبيل المثال: عنوان القسم',
  'Special Criteria List Section ' => 'قسم قائمة المعايير الخاصة',
  'Special Criteria Title' => 'عنوان المعايير الخاصة',
  'Ex:  Multi Store System' => 'على سبيل المثال: نظام المتاجر المتعددة',
  'Criteria Icon/ Image' => 'أيقونة/ صورة معايير',
  ' Special Criteria' => 'معايير خاصة',
  'This Criteria' => 'هذه المعايير',
  'This section  will be disabled. You can enable it in the settings' => 'سيتم تعطيل هذا القسم. يمكنك تمكينه في الإعدادات',
  'This section will be enabled. You can see this section on your landing page.' => 'سيتم تمكين هذا القسم. يمكنك رؤية هذا القسم على صفحتك المقصودة.',
  'Counter Section' => 'القسم المضاد',
  'Total App Download' => 'إجمالي تنزيل التطبيق',
  'Ex: 500' => 'على سبيل المثال: 500',
  'Total Seller' => 'إجمالي البائع',
  'Total Delivery Man' => 'إجمالي التوصيل رجل',
  'Total Customer' => 'إجمالي العميل',
  'Download User App Section Content' => 'قم بتنزيل محتوى قسم تطبيق المستخدم',
  'Download Apps Section' => 'تنزيل قسم التطبيقات',
  'By Turning OFF Counter Section' => 'عن طريق إيقاف تشغيل القسم المضاد',
  'Counter section will be disabled. You can enable it in the settings to access its features and functionality' => 'سيتم تعطيل القسم المضاد. يمكنك تمكينه في الإعدادات للوصول إلى ميزاتها ووظائفها',
  'By Turning ON Counter Section' => 'عن طريق تشغيل القسم المضاد',
  'Counter Section is enabled. You can now access its features and functionality' => 'يتم تمكين قسم العداد. يمكنك الآن الوصول إلى ميزاتها ووظائفها',
  'By Turning OFF App Store Button' => 'عن طريق إيقاف تشغيل زر متجر التطبيقات',
  'App store button will be disabled now no one can use or see the button' => 'سيتم تعطيل زر متجر التطبيقات الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'By Turning ON App Store Button' => 'عن طريق تشغيل زر متجر التطبيقات',
  'App store button is enabled now everyone can use or see the button' => 'تم تمكين زر متجر التطبيقات الآن يمكن للجميع استخدامه أو رؤية الزر',
  'By Turning OFF PlayStore Button' => 'عن طريق إيقاف تشغيل زر بلايستور',
  'Playstore button will be disabled now no one can use or see the button' => 'سيتم تعطيل زر PlayStore الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'By Turning ON PlayStore Button' => 'عن طريق تشغيل زر PlayStore',
  'Playstore button will be enabled now everyone can use or see the button' => 'سيتم تمكين زر PlayStore الآن يمكن للجميع استخدامه أو رؤية الزر',
  'Testimonial List Section' => 'قسم قائمة الشهادة',
  'Reviewer Name' => 'اسم المراجع',
  'Ex:  John Doe' => 'على سبيل المثال: جون دو',
  'Designation' => 'تعيين',
  'Ex:  CTO' => 'من: CTO',
  'Very Good Company' => 'شركة جيدة جدا',
  'Reviewer Image *' => 'صورة المراجع *',
  'Company Logo *' => 'شعار الشركة *',
  'Reviews' => 'المراجعات',
  'Reviewer Image' => 'صورة المراجع',
  'Company Image' => 'صورة الشركة',
  'Start Time' => 'وقت البدء',
  'End Time' => 'وقت النهاية',
  'Start Day' => 'تبدأ اليوم',
  'End Day' => 'نهاية اليوم',
  'Notice!' => 'يلاحظ!',
  'If you want to disable or turn off any section please leave that section empty  donât make any changes there!' => 'إذا كنت ترغب في تعطيل أو إيقاف تشغيل أي قسم ، فيرجى ترك هذا القسم فارغًا ، فلا يقوم بإجراء أي تغييرات هناك!',
  'If You Want to Change Language' => 'إذا كنت تريد تغيير اللغة',
  'Change the language on tab bar and input your data again!' => 'قم بتغيير اللغة على شريط علامة التبويب وإدخال بياناتك مرة أخرى!',
  'Letâs See The Changes!' => 'دعونا نرى التغييرات!',
  'Visit landing page to see the changes you made in the settings option!' => 'قم بزيارة الصفحة المقصودة للاطلاع على التغييرات التي أجريتها في خيار الإعدادات!',
  'admin_landing_page_settings' => 'إعدادات الصفحة المقصودة المشرف',
  'flutter_landing_page' => 'الصفحة المقصودة رفرفة',
  'Header' => 'رأس',
  'Company Intro' => 'مقدمة الشركة',
  'Download User App' => 'تنزيل تطبيق المستخدم',
  'Business Section' => 'قسم الأعمال',
  'Header Section' => 'قسم الرأس',
  'Tag Line' => 'سطر العلامة',
  'tag_line...' => 'سطر العلامة ...',
  'Button Content' => 'محتوى الزر',
  'Button Name' => 'اسم الزر',
  'Ex: Order now' => 'على سبيل المثال: النظام الآن',
  'Redirect Link' => 'إعادة توجيه الرابط',
  'Section Title' => 'عنوان القسم',
  'Seller Section Content' => 'محتوى قسم البائع',
  'Button Redirect' => 'زر إعادة توجيه',
  'Delivery Man Section Content' => 'محتوى قسم التسليم رجل',
  'Download Delivery Man App' => 'تطبيق تنزيل تسليم رجل',
  'By Turning OFF User App Button' => 'عن طريق إيقاف تشغيل زر تطبيق المستخدم',
  'User app button will be disabled. Nobody can use or see the button' => 'سيتم تعطيل زر تطبيق المستخدم. لا أحد يستطيع استخدام الزر أو رؤية',
  'By Turning ON User App Button' => 'عن طريق تشغيل زر تطبيق المستخدم',
  'User app button will be enabled. everyone can use or see the button' => 'سيتم تمكين زر تطبيق المستخدم. يمكن للجميع استخدام الزر أو رؤيةه',
  'By Turning OFF Delivery Man App Button' => 'عن طريق إيقاف تشغيل زر تطبيق MAN APP',
  'Seller app button will be disabled. Nobody can use or see the button' => 'سيتم تعطيل زر تطبيق البائع. لا أحد يستطيع استخدام الزر أو رؤية',
  'By Turning ON Delivery Man App Button' => 'عن طريق تشغيل زر تطبيق تطبيق MAN',
  'Playstore button will be enabled. everyone can use or see the button' => 'سيتم تمكين زر Playstore. يمكن للجميع استخدام الزر أو رؤيةه',
  'By Turning OFF Seller App Button' => 'عن طريق إيقاف تشغيل زر تطبيق البائع',
  'By Turning ON Seller App Button' => 'عن طريق تشغيل زر تطبيق البائع',
  'Testimonials' => 'الشهادات - التوصيات',
  'This Testimonial' => 'هذه الشهادة',
  'This testimonial will be disable. You can see this testimonial in review section.' => 'سيتم تعطيل هذه الشهادة. يمكنك رؤية هذه الشهادة في قسم المراجعة.',
  'This testimonial will be enabled. You can see this testimonial in review section.' => 'سيتم تمكين هذه الشهادة. يمكنك رؤية هذه الشهادة في قسم المراجعة.',
  'flutter_web_landing_page' => 'صفحة مقصودة على شبكة الإنترنت رفرفة',
  'special_criteria' => 'معايير خاصة',
  'join_as' => 'انضمام إلى',
  'location_setup' => 'إعداد الموقع',
  'module_setup' => 'إعداد الوحدة النمطية',
  'Feature Special Criteria Status' => 'ميزة حالة المعايير الخاصة',
  'Criteria list will be disabled. You can enable it in the settings to access its features and functionality' => 'سيتم تعطيل قائمة المعايير. يمكنك تمكينه في الإعدادات للوصول إلى ميزاتها ووظائفها',
  'Special Criteria is enabled. You can now access its features and functionality' => 'تم تمكين المعايير الخاصة. يمكنك الآن الوصول إلى ميزاتها ووظائفها',
  'Join as a Seller' => 'انضم كبائع',
  'Select Language' => 'اختار اللغة',
  'Join as a Delivery Man' => 'انضم كرجل توصيل',
  'Join as Seller' => 'انضم إلى البائع',
  'Join as Deliveryman' => 'انضم إلى تسليم',
  'By Turning OFF Download Apps Status' => 'عن طريق إيقاف تنزيل حالة تطبيقات',
  'By Turning ON Download Apps Status' => 'عن طريق تشغيل حالة تطبيقات التنزيل',
  'a_5_digit_verification_code_has_been' => 'كان رمز التحقق من 5 أرقام',
  'sent_to' => 'أرسل إلى',
  'enter_the_verification_code' => 'أدخل رمز التحقق',
  'Didn`t receive the code ' => 'لم يتلق الرمز',
  'Resend_it' => 'اعد ارسالها',
  'Link_expired' => 'انتهت صلاحية الرابط',
  'Failed_to_sent_otp' => 'فشل في إرسال OTP',
  'Otp_successfull_sent' => 'أرسلت OTP الناجحة',
  'By Turning ONN Promotional Banner Section' => 'عن طريق تحويل قسم لافتة ترويجي ONN',
  'short_description_here...' => 'وصف قصير هنا ...',
  'feature_added_successfully' => 'ميزة تمت إضافتها بنجاح',
  'feature_section_updated' => 'تم تحديث قسم الميزات',
  'feature_status_updated' => 'تم تحديث حالة الميزة',
  'feature_deleted_successfully' => 'ميزة حذف بنجاح',
  'Playstore Button Enabled for Delivery Man' => 'تمكين زر PlayStore للتسليم رجل',
  'Playstore Button Disabled for Delivery Man' => 'تعطيل زر Playstore للتسليم',
  'App Store Button Enabled for Delivery Man' => 'ممكّن زر متجر التطبيقات للتسليم رجل',
  'App Store Button Disabled for Delivery Man' => 'زر متجر التطبيق معطل للتسليم رجل',
  'seller_links_updated' => 'روابط البائع المحدثة',
  'delivery_man_links_updated' => 'روابط رجل التسليم محدث',
  'criteria_added_successfully' => 'تمت إضافة المعايير بنجاح',
  'why_choose_section_updated' => 'لماذا تختار القسم المحدث',
  'criteria_status_updated' => 'معايير حالة تحديث',
  'criteria_updated_successfully' => 'المعايير التي تم تحديثها بنجاح',
  'Want to delete this criteria  ' => 'تريد حذف هذه المعايير',
  'criteria' => 'معايير',
  'criteria_deleted_successfully' => 'تم حذف المعايير بنجاح',
  'download_app_section_updated' => 'تنزيل قسم التطبيق المحدث',
  'button_name_here...' => 'اسم الزر هنا ...',
  'sbutton_name_here...' => 'اسم Sbutton هنا ...',
  'Button URL' => 'زر عنوان URL',
  'join_as_seller_data_updated' => 'انضم إلى تحديث بيانات البائع',
  'join_as_delivery_man_data_updated' => 'انضم إلى تحديث بيانات رجل التسليم',
  'earning_section_updated' => 'كسب قسم تحديث',
  'landing_page_header_updated' => 'تحديث رأس الصفحة المقصودة',
  'This review' => 'هذا الاستعراض',
  'Want to delete this review  ' => 'تريد حذف هذا الاستعراض',
  ' Special review' => 'مراجعة خاصة',
  'testimonial_section_updated' => 'قسم الشهادة تم تحديثه',
  'testimonial_added_successfully' => 'تمت إضافة شهادة بنجاح',
  'review_status_updated' => 'مراجعة حالة تحديث',
  'review_deleted_successfully' => 'مراجعة حذف بنجاح',
  'review_updated_successfully' => 'مراجعة تحديث بنجاح',
  'Seller Button Enabled for Delivery Man' => 'تمكين زر البائع للتسليم رجل',
  'Seller Button Disabled for Delivery Man' => 'تعطيل زر البائع للتسليم',
  'Seller button is enabled now everyone can use or see the button' => 'تم تمكين زر البائع الآن يمكن للجميع استخدامه أو رؤية الزر',
  'Seller button is disabled now no one can use or see the button' => 'تم تعطيل زر البائع الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'Delivery Man Button Enabled for Delivery Man' => 'زر التسليم ممكّن لتوصيل رجل التسليم',
  'Delivery Man Button Disabled for Delivery Man' => 'تعطيل زر التسليم لتوصيل رجل',
  'Delivery Man button is enabled now everyone can use or see the button' => 'يتم تمكين زر التسليم الآن على الزر أو رؤية الزر',
  'Delivery Man button is disabled now no one can use or see the button' => 'تم تعطيل زر التسليم الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'User Button Enabled for Delivery Man' => 'ممكّن زر المستخدم للتسليم رجل',
  'User Button Disabled for Delivery Man' => 'تعطيل زر المستخدم للتسليم',
  'User button is enabled now everyone can use or see the button' => 'يتم تمكين زر المستخدم الآن يمكن للجميع استخدامه أو رؤية الزر',
  'User button is disabled now no one can use or see the button' => 'يتم تعطيل زر المستخدم الآن لا يمكن لأحد استخدامه أو رؤية الزر',
  'business_section_updated' => 'تم تحديث قسم الأعمال',
  'header_section_updated' => 'تم تحديث قسم الرأس',
  'Company Section' => 'قسم الشركة',
  'company_section_updated' => 'قسم الشركة المحدث',
  'Start a new message' => 'ابدأ رسالة جديدة',
  'email_template' => 'قالب البريد الإلكتروني',
  'Email Templates' => 'قوالب البريد الإلكتروني',
  'Admin Registration' => 'تسجيل المسؤول',
  'Forgot Password' => 'هل نسيت كلمة السر',
  'New Store Registration' => 'تسجيل متجر جديد',
  'New Delivery Man Registration' => 'تسجيل رجل توصيل جديد',
  'Campaign Join Request' => 'طلب الانضمام إلى الحملة',
  'Login mail' => 'بريد تسجيل الدخول',
  'Send Mail on Place Order  ' => 'أرسل بريدًا على طلب المكان',
  'Read Instructions' => 'قراءة التعليمات',
  'Header Content' => 'محتوى الرأس',
  'Main Title' => 'العنوان الرئيسي',
  'Background image' => 'الصورة الخلفية',
  'Footer Content' => 'محتوى تذييل',
  'Section Text' => 'نص القسم',
  'Page Links' => 'روابط الصفحة',
  'Privacy Policy' => 'سياسة الخصوصية',
  'Contact Us' => 'اتصل بنا',
  'Social Media Links' => 'روابط وسائل التواصل الاجتماعي',
  'Copyright Content' => 'محتوى حقوق الطبع والنشر',
  'Want to disable Place Order' => 'تريد تعطيل ترتيب المكان',
  'User will not get a confirmation email when they placed a order.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تقديم طلب.',
  'Want to enable Place Order' => 'تريد تمكين طلب المكان',
  'User will get a confirmation email when they placed a order.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تقديم طلب.',
  'Send Mail on Forgot Password  ' => 'أرسل بريدًا على كلمة المرور نسيت',
  'Icon / Vector' => 'أيقونة / متجه',
  'Want to disable Forgot Password' => 'تريد تعطيل كلمة المرور نسيت',
  'User will not get a OTP by email when they forgot password. they will get OTP by SMS if sms module is on.' => 'لن يحصل المستخدم على OTP عبر البريد الإلكتروني عندما ينسون كلمة المرور. سيحصلون على OTP بواسطة SMS إذا كانت وحدة SMS قيد التشغيل.',
  'Want to enable Forgot Password' => 'تريد تمكين نسيان كلمة المرور',
  'User will get a OTP by email when they forgot password.' => 'سيحصل المستخدم على OTP عبر البريد الإلكتروني عندما ينسون كلمة المرور.',
  'Get Mail on New Store Registration  ' => 'احصل على البريد على تسجيل المتجر الجديد',
  'Logo' => 'شعار',
  'By Turning OFF Get Mail on New Store Registration' => 'عن طريق إيقاف تشغيل البريد على تسجيل المتجر الجديد',
  'User will not get mail when they register for a new store!' => 'لن يحصل المستخدم على البريد عند التسجيل في متجر جديد!',
  'By Turning ON Get Mail on New Store Registration' => 'عن طريق تشغيل الحصول على البريد على تسجيل المتجر الجديد',
  'User will get mail when they register for a new store!' => 'سيحصل المستخدم على البريد عند التسجيل في متجر جديد!',
  'Select Theme' => 'اختر نمطا',
  'Input a Title' => 'إدخال العنوان',
  'Give email template a descriptive title that will help users identify what it for.' => 'امنح قالب البريد الإلكتروني عنوانًا وصفيًا من شأنه أن يساعد المستخدمين على تحديد ما هو عليه.',
  'Add the message body' => 'أضف جسم الرسالة',
  'Add Button & Link' => 'إضافة زر ورابط',
  'Specify the text and URL for the button that you want to include in your email.' => 'حدد النص وعنوان URL للزر الذي تريد تضمينه في بريدك الإلكتروني.',
  'Choose a background image' => 'اختر صورة خلفية',
  'Select a background image to display behind your email content.' => 'حدد صورة خلفية لعرضها خلف محتوى البريد الإلكتروني الخاص بك.',
  'Select footer content' => 'حدد محتوى تذييل',
  'Add footer content  such as your company address and contact information.' => 'أضف محتوى تذييل مثل عنوان شركتك ومعلومات الاتصال.',
  'Create a copyright notice' => 'إنشاء إشعار حقوق الطبع والنشر',
  'Include a copyright notice at the bottom of your email to protect your content.' => 'قم بتضمين إشعار حقوق الطبع والنشر في أسفل بريدك الإلكتروني لحماية المحتوى الخاص بك.',
  'Save and publish' => 'حفظ ونشر',
  'Once you ve set up all the elements of your email template  save and publish it for use.' => 'بمجرد إعداد جميع عناصر قالب بريدك الإلكتروني ، احفظه ونشره للاستخدام.',
  'Mail Body Message' => 'رسالة جسم البريد',
  'template_added_successfully' => 'القالب أضيفت بنجاح',
  'Icon' => 'أيقونة',
  'Banner image' => 'صورة بانر',
  'email_status_updated' => 'تم تحديث حالة البريد الإلكتروني',
  'Send Mail on Campaign Request  ' => 'أرسل بريدًا عند طلب الحملة',
  'Want to enable Campaign Request' => 'تريد تمكين طلب الحملة',
  'Want to disable Campaign Request' => 'تريد تعطيل طلب الحملة',
  'User will get a confirmation email when they requests to join campaign.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عندما يطلبون الانضمام إلى الحملة.',
  'User will not get a confirmation email when they requests to join campaign.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عندما يطلبون الانضمام إلى الحملة.',
  'Send Mail on Delivery Man Registration  ' => 'أرسل بريدًا على تسجيل رجل التسليم',
  'Want to enable Delivery Man Registration' => 'تريد تمكين تسجيل رجل التسليم',
  'Want to disable Delivery Man Registration' => 'تريد تعطيل تسجيل رجل التسليم',
  'User will get a confirmation email when they newly register.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند التسجيل حديثًا.',
  'User will not get a confirmation email when they newly register.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند التسجيل حديثًا.',
  'Send Mail on Store Registration  ' => 'أرسل بريدًا عن تسجيل المتجر',
  'Want to enable Store Registration' => 'تريد تمكين تسجيل المتجر',
  'Want to disable Store Registration' => 'تريد تعطيل تسجيل المتجر',
  'Send Mail on Withdraw REquest  ' => 'أرسل بريدًا عند طلب السحب',
  'Want to enable Withdraw REquest' => 'تريد تمكين طلب السحب',
  'Want to disable Withdraw REquest' => 'تريد تعطيل طلب السحب',
  'User will get a confirmation email when requests withdraw.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند انسحاب الطلبات.',
  'User will not get a confirmation email when requests withdraw.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند انسحاب الطلبات.',
  'Send Mail on Forget Password  ' => 'أرسل بريدًا على كلمة المرور نسيان',
  'Want to enable Forget Password' => 'تريد تمكين نسيان كلمة المرور',
  'Want to disable Forget Password' => 'تريد تعطيل نسيان كلمة المرور',
  'User will get a confirmation email when requests forget password.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عندما تنسى الطلبات كلمة المرور.',
  'User will not get a confirmation email when requests forget password.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عندما تنسى الطلبات كلمة المرور.',
  'Send Mail on Login  ' => 'أرسل البريد عند تسجيل الدخول',
  'Want to enable Login' => 'تريد تمكين تسجيل الدخول',
  'Want to disable Login' => 'تريد تعطيل تسجيل الدخول',
  'User will get a confirmation email when login.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تسجيل الدخول.',
  'User will not get a confirmation email when login.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تسجيل الدخول.',
  'Send Mail on Refund Request  ' => 'أرسل بريدًا عند طلب الاسترداد',
  'Want to enable Refund Request' => 'تريد تمكين طلب الاسترداد',
  'Want to disable Refund Request' => 'تريد تعطيل طلب الاسترداد',
  'User will get a confirmation email when requests refund.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند استرداد الطلبات.',
  'User will not get a confirmation email when requests refund.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند استرداد الطلبات.',
  'User_app_settings_updated' => 'تحديث إعدادات تطبيق المستخدم',
  'Store_app_settings_updated' => 'تخزين إعدادات التطبيق محدثة',
  'Delivery_app_settings_updated' => 'تحديث إعدادات تطبيق التسليم',
  'Email Verification' => 'تأكيد بواسطة البريد الالكتروني',
  'mail_received_successfully' => 'تم استلام البريد بنجاح',
  'Thanks & Regards' => 'مع الشكر و التقدير',
  'Send Mail on Withdraw Request  ' => 'أرسل بريدًا عند طلب السحب',
  'Want to enable Withdraw Request' => 'تريد تمكين طلب السحب',
  'Want to disable Withdraw Request' => 'تريد تعطيل طلب السحب',
  'Admin_mail_templates' => 'قوالب بريد المسؤول',
  'Store_mail_templates' => 'تخزين قوالب البريد',
  'Delivery_man_mail_templates' => 'قوالب البريد رجل التسليم',
  'User_mail_templates' => 'قوالب بريد المستخدم',
  'New Store Approve' => 'متجر جديد يوافق',
  'New Store Deny' => 'متجر جديد ينكر',
  'Withdraw Approve' => 'سحب الموافقة',
  'Withdraw Deny' => 'سحب الرفض',
  'Campaign Join Approve' => 'حملة الانضمام الموافقة',
  'Campaign Join Deny' => 'حملة الانضمام إلى Deny',
  'Send Mail on Campaign deny  ' => 'أرسل بريدًا على رفض الحملة',
  'Want to enable Campaign deny' => 'تريد تمكين الحملة إنكار',
  'Want to disable Campaign deny' => 'تريد تعطيل الحملة إنكار',
  'User will get a confirmation email when they appove to join campaign.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تطبيقه للانضمام إلى الحملة.',
  'User will not get a confirmation email when they appove to join campaign.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تطبيقه للانضمام إلى الحملة.',
  'Send Mail on Campaign Approve  ' => 'أرسل البريد عند الموافقة على الحملة',
  'Want to enable Campaign Approve' => 'تريد تمكين الموافقة على الحملة',
  'Want to disable Campaign Approve' => 'تريد تعطيل الحملة الموافقة',
  'Send Mail on Withdraw deny  ' => 'أرسل بريدًا عند سحب السحب',
  'Want to enable Withdraw deny' => 'تريد تمكين السحب إنكار',
  'Want to disable Withdraw deny' => 'تريد تعطيل الانسحاب الرفض',
  'User will get a confirmation email when denys withdraw.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند انسحاب دينيس.',
  'User will not get a confirmation email when denys withdraw.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند انسحاب دينيس.',
  'New Delivery Man Approve' => 'يوافق رجل توصيل جديد',
  'New Delivery Man Deny' => 'رجل توصيل جديد ينكر',
  'Suspension' => 'تعليق',
  'Cash Collected' => 'تم جمع النقد',
  'Send Mail on Delivery Man approve  ' => 'أرسل بريدًا عند توصيل رجل التسليم',
  'Want to enable Delivery Man approve' => 'تريد تمكين موافقة رجل التسليم',
  'Want to disable Delivery Man approve' => 'تريد تعطيل الموافقة على رجل التسليم',
  'Send Mail on Delivery Man deny  ' => 'أرسل بريدًا عند تسليم رجل الرفض',
  'Want to enable Delivery Man deny' => 'تريد تمكين رجل التسليم',
  'Want to disable Delivery Man deny' => 'تريد تعطيل رجل التسليم',
  'Send Mail on Suspend  ' => 'أرسل بريدًا عند تعليقه',
  'Want to enable Suspend' => 'تريد تمكين التعليق',
  'Want to disable Suspend' => 'تريد تعطيل التعليق',
  'Send Mail on cash collect  ' => 'أرسل البريد على Cash Collect',
  'Want to enable cash collect' => 'تريد تمكين جمع النقود',
  'Want to disable cash collect' => 'تريد تعطيل جمع النقود',
  'User will get a confirmation email when requests cash collect.' => 'سيحصل المستخدم على بريد إلكتروني تأكيد عند جمع الطلبات النقدية.',
  'User will not get a confirmation email when requests cash collect.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند جمع الطلبات النقدية.',
  'New User Registration' => 'تسجيل مستخدم جديد',
  'Send Mail on Withdraw approve  ' => 'أرسل البريد عند الموافقة على السحب',
  'Want to enable Withdraw approve' => 'تريد تمكين السحب الموافقة',
  'Want to disable Withdraw approve' => 'تريد تعطيل السحب الموافقة',
  'User will get a confirmation email when approves withdraw.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند الموافقة على الانسحاب.',
  'User will not get a confirmation email when approves withdraw.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند الموافقة على الانسحاب.',
  'Send Mail on Store deny  ' => 'أرسل بريدًا إلى المتجر إنكار',
  'Want to enable Store deny' => 'تريد تمكين المتجر إنكار',
  'Want to disable Store deny' => 'تريد تعطيل المتجر الرفض',
  'Send Mail on Store approve  ' => 'أرسل البريد على المتجر الموافقة',
  'Want to enable Store approve' => 'تريد تمكين المتجر الموافقة',
  'Want to disable Store approve' => 'تريد تعطيل المتجر الموافقة',
  'Send Mail on User Registration  ' => 'إرسال البريد على تسجيل المستخدم',
  'Want to enable User Registration' => 'تريد تمكين تسجيل المستخدم',
  'Want to disable User Registration' => 'تريد تعطيل تسجيل المستخدم',
  'New User Registration OTP' => 'تسجيل المستخدم الجديد OTP',
  'New Order Verification' => 'التحقق من الطلب الجديد',
  'Refund Request Deny' => 'طلب استرداد الرفض',
  'Add fund' => 'إضافة الصندوق',
  'Send Mail on add fund  ' => 'أرسل بريدًا على صندوق إضافة',
  'Want to enable add fund' => 'تريد تمكين إضافة صندوق',
  'Want to disable add fund' => 'تريد تعطيل صندوق إضافة',
  'User will get a confirmation email when requests add fund.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند إضافة الطلبات.',
  'User will not get a confirmation email when requests add fund.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند إضافة الطلبات.',
  'Registration OTP' => 'التسجيل OTP',
  'Login OTP' => 'تسجيل الدخول OTP',
  'Send Mail on User login  ' => 'أرسل بريدًا على تسجيل الدخول إلى المستخدم',
  'Want to enable User login' => 'تريد تمكين تسجيل الدخول إلى المستخدم',
  'Want to disable User login' => 'تريد تعطيل تسجيل الدخول للمستخدم',
  'Order Verification' => 'التحقق من الطلب',
  'Send Mail on Refund Order  ' => 'أرسل بريدًا على أمر الاسترداد',
  'Want to enable Refund Order' => 'تريد تمكين ترتيب الاسترداد',
  'Want to disable Refund Order' => 'تريد تعطيل ترتيب الاسترداد',
  'User will get a confirmation email when registration appoved.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند التسجيل.',
  'User will not get a confirmation email when registration appoved.' => 'لن يحصل المستخدم على بريد إلكتروني تأكيد عند التسجيل.',
  'User will get a confirmation email when they request denied to join campaign.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عندما يطلبون رفضهم للانضمام إلى الحملة.',
  'User will not get a confirmation email when they request denied to join campaign.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عندما يطلبون رفضهم للانضمام إلى الحملة.',
  'User will get a confirmation email when request denied to join campaign.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند رفض الطلب للانضمام إلى الحملة.',
  'User will not get a confirmation email when request denied to join campaign.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند رفض الطلب للانضمام إلى الحملة.',
  'User will get a confirmation email when request appoved to join campaign.' => 'سيحصل المستخدم على بريد إلكتروني تأكيد عند تطبيق Appoved للانضمام إلى الحملة.',
  'User will not get a confirmation email when request appoved to join campaign.' => 'لن يحصل المستخدم على رسالة بريد إلكتروني للتأكيد عند تطبيق APPOVED للانضمام إلى الحملة.',
  'User will get a confirmation email when request denied.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند رفض الطلب.',
  'User will not get a confirmation email when request denied.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند رفض الطلب.',
  'User will get a confirmation email when registration approved.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند الموافقة على التسجيل.',
  'User will not get a confirmation email when registration approved.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند الموافقة على التسجيل.',
  'User will get a confirmation email when registration denied.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند رفض التسجيل.',
  'User will not get a confirmation email when registration denied.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند رفض التسجيل.',
  'User will get a confirmation email when admin collect cash from them.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عندما يقوم المسؤول بجمع النقود منها.',
  'User will not get a confirmation email when admin collect cash from them.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عندما يقوم المسؤول بجمع النقود منها.',
  'User will get a confirmation email when they login.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تسجيل الدخول.',
  'User will not get a confirmation email when they login.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تسجيل الدخول.',
  'User will get a confirmation email when order verification is on.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تشغيل الطلب.',
  'User will not get a confirmation email when order verification is on.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تشغيل الطلب.',
  'Send Mail on Order Verification  ' => 'أرسل بريدًا عند التحقق من الطلب',
  'Want to enable Order Verification' => 'تريد تمكين التحقق من الطلب',
  'Want to disable Order Verification' => 'تريد تعطيل التحقق من الطلب',
  'Send Mail on Refund Request Denied  ' => 'أرسل بريدًا عند رفض طلب الاسترداد',
  'Want to enable Refund Request Denied' => 'تريد تمكين طلب الاسترداد رفض',
  'Want to disable Refund Request Denied' => 'تريد تعطيل طلب الاسترداد رفض',
  'User will get a confirmation email when refund request denied.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند رفض طلب الاسترداد.',
  'User will not get a confirmation email when refund request denied.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند رفض طلب الاسترداد.',
  'User will get a confirmation email when admin add fund.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند إضافة صندوق إضافة المسؤول.',
  'User will not get a confirmation email when admin add fund.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند إضافة صندوق إضافة المسؤول.',
  'transaction_id' => 'رقم المعاملة',
  'User will get a confirmation email when they are suspended.' => 'سيحصل المستخدم على بريد إلكتروني للتأكيد عند تعليقه.',
  'User will not get a confirmation email when they are suspended.' => 'لن يحصل المستخدم على بريد إلكتروني للتأكيد عند تعليقه.',
  'view_order_list' => 'عرض قائمة الطلب',
  'Turn ON' => 'شغله',
  'Hour' => 'ساعة',
  'No variation added' => 'لا يوجد تباين',
  'Multiple Selection' => 'اختيار متعددة',
  'Single Selection' => 'اختيار واحد',
  'Default' => 'تقصير',
  'ABC Company' => 'شركة شركة',
  'House#94  Road#8  Abc City' => 'منزل#94 الطريق رقم 8 ABC City',
  'Store Logo & Covers' => 'شعار المتجر والأغطية',
  'Store Cover' => 'غطاء متجر',
  '2:1' => '2:1',
  'Estimated Delivery Time ( Min & Maximum Time)' => 'وقت التسليم المقدر (دقيقة وأقصى وقت)',
  'Minimum Time' => 'الحد الأدنى من الوقت',
  'Maximum Time' => 'الحد الأقصى للوقت',
  'add_store_name' => 'أضف اسم المتجر',
  'done' => 'منتهي',
  'Order Notification' => 'إشعار الطلب',
  'Turning off this  admin will not get a popup notification with sound for all orders.' => 'لن يحصل إيقاف تشغيل هذا المسؤول على إشعار منبثقة مع الصوت لجميع الطلبات.',
  'pages_&_social_media' => 'الصفحات ووسائل التواصل الاجتماعي',
  'system_management' => 'ادارة النظام',
  '3rd_party_&_configurations' => 'الطرف الثالث والتكوينات',
  '3rd_party' => '3rd الحزب',
  'firebase_notification' => 'إشعار Firebase',
  'Change status to cooking  ' => 'تغيير الحالة إلى الطهي',
  'Are you sure  ' => 'هل أنت متأكد',
  'Enter processing time' => 'أدخل وقت المعالجة',
  'Enter Processing time in minutes' => 'أدخل وقت المعالجة في دقائق',
  'No' => 'لا',
  'Cancel Order' => 'الغاء الطلب',
  'Banner Section' => 'قسم لافتة',
  'banner Image' => 'صورة بانر',
  'is_organic' => 'عضوي',
  'plesae_enter_your_registerd_email' => 'PLESAE أدخل بريدك الإلكتروني registerd',
  'Email_does_not_exists' => 'البريد الإلكتروني لا يوجد',
  'company' => 'شركة',
  'new_company' => 'شركة جديدة',
  'digit_after_decimal_point' => 'رقم بعد نقطة عشرية',
  'Ex_:_Copyright_Text' => 'على سبيل المثال: نص حقوق الطبع والنشر',
  'ex_:_2' => 'على سبيل المثال: 2',
  'how_many_fractional_digit_to_show_after_decimal_value' => 'كم عدد الرقم الكسري لإظهاره بعد القيمة العشرية',
  'NB: Must select atlest one payment method' => 'NB: يجب تحديد طريقة دفع ATLEST',
  'Main_Title_or_Subject_of_the_Mail' => 'العنوان الرئيسي أو موضوع البريد',
  'Hi_Sabrina ' => 'مرحبا سابرينا',
  'Order_Info' => 'طلب معلومات',
  'Order_Summary' => 'ملخص الطلب',
  'Order' => 'طلب',
  'Delivery_Address' => 'عنوان التسليم',
  'Price' => 'سعر',
  'Please_contact_us_for_any_queries _weâre_always_happy_to_help.' => 'يرجى الاتصال بنا للحصول على أي استفسارات يسعدنا دائمًا المساعدة.',
  'Thanks_&_Regards' => 'مع الشكر و التقدير',
  'Privacy_Policy' => 'سياسة الخصوصية',
  'Refund_Policy' => 'سياسة الاسترجاع',
  'Cancelation_Policy' => 'سياسة الإلغاء',
  'Contact_us' => 'اتصل بنا',
  'Copyright 2023 6ammart. All right reserved' => 'حقوق الطبع والنشر 2023 6ammart. جميع الحقوق محفوظة',
  'react_site' => 'رد الفعل',
  'Identity_Type' => 'نوع الهوية',
  'driving_license' => 'رخصة قيادة',
  'login_page_setup' => 'إعداد صفحة تسجيل الدخول',
  'Admin_login_url' => 'url تسجيل الدخول إلى Quarity url',
  'For_admin' => 'للمسؤول',
  'admin_login_url' => 'url تسجيل الدخول إلى Quarity url',
  'admin_employee_login_page' => 'صفحة تسجيل الدخول إلى الموظف المشرف',
  'For_admin_employee' => 'للموظف المشرف',
  'admin_employee_login_url' => 'url تسجيل الدخول إلى الموظف url',
  'store_login_page' => 'تخزين صفحة تسجيل الدخول',
  'For_stores' => 'للمتاجر',
  'store_login_url' => 'تخزين url تسجيل الدخول',
  'store_employee_login_page' => 'صفحة تسجيل الدخول إلى المتجر',
  'For_store_employee' => 'لموظف المتجر',
  'store_employee_login_url' => 'url تسجيل الدخول إلى url تسجيل الدخول إلى الموظف',
  'login_url_page' => 'صفحة عنوان URL لتسجيل الدخول',
  'update_successfull' => 'تحديث ناجح',
  'donât_forget_to_click_the_âSave Informationâ_button_below_to_save_changes.' => 'لا تنسى النقر فوق الزر "حفظ المعلومات" أدناه لحفظ التغييرات.',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_âTurn_Offâ _maintenance_mode.' => 'سيتم تعطيل جميع تطبيقاتك وموقع العميل على الويب حتى تقوم بإيقاف تشغيل وضع الصيانة.',
  'make_visitors_aware_of_your_businessâs_rights_&_legal_information.' => 'اجعل الزائرين على دراية بحقوق عملك والمعلومات القانونية.',
  'Business_Rules_setup' => 'إعداد قواعد العمل',
  'Default_Commission_Rate_On_Order' => 'معدل العمولة الافتراضية حسب الطلب',
  'Set_up_âDefault_Commission_Rateâ_on_every_Order._Admin_can_also_set_store-wise_different_commission_rates_from_respective_store_settings.' => 'وضع معدل العمولة الافتراضية على كل أمر. يمكن للمسؤول أيضًا تعيين أسعار العمولة المختلفة من المتجر من إعدادات المتجر المعنية.',
  'Commission_Rate_On_Delivery_Charge' => 'معدل العمولة على رسوم التوصيل',
  'Set_a_default_âCommission_Rateâ_for_freelance_deliverymen_(under_admin)_on_every_deliveryman. ' => 'قم بتعيين معدل العمولة الافتراضي لعمليات التوصيل المستقل (تحت المسؤول) على كل تسليم.',
  'Who_Will_Confirm_Order ' => 'من سيؤكد الأمر؟',
  'After_a_customer_order_placement _Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store _For_example _if_you_choose_âDelivery_manâ _the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_âStoreâ.' => 'بعد أن يتمكن مسؤول وضع طلب العميل من تحديد من الذي سيؤكد الطلب الأول أو المتجر على سبيل المثال ، على سبيل المثال ، إذا اخترت "تسليم Manâ" ، فسيؤكد Developmentman على الطلب وإعادة توجيهه إلى المتجر ذي الصلة لمعالجة الطلب. إنه يعمل بالعكس إذا اخترت Â Storeâ.',
  'If_enabled _the_customer_will_see_the_total_product_price _including_VAT/Tax._If_itâs_disabled _the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'إذا تم تمكين العميل ، فسيشاهد العميل إجمالي سعر المنتج بما في ذلك ضريبة القيمة المضافة/الضريبة. إذا تم تعطيلها ، فسيتم إضافة ضريبة القيمة المضافة/الضريبة بشكل منفصل بالتكلفة الإجمالية للمنتج.',
  'Want_to' => 'اريد ان',
  'âInclude_Tax_Amount â' => 'Â تضمين المبلغ الضريبي Â',
  'Want_to_disable' => 'تريد تعطيل',
  'Tax_Amountâ ' => 'مبلغ الضرائب',
  'If_you_enable_it _customers_will_see_the_product_Price_including_Tax _during_checkout. ' => 'إذا قمت بتمكينها ، فسيشاهد العملاء سعر المنتج بما في ذلك الضريبة أثناء الخروج.',
  'If_you_disable_it _customers_will_see_the_product_or_service_price_without_Tax _during_checkout.' => 'إذا قمت بتعطيلها ، فسيشاهد العملاء المنتج أو سعر الخدمة دون ضريبة أثناء الخروج.',
  'Customerâs_Food_Preference' => 'تفضيل الطعام للعميل',
  'If_this_feature_is_active _customers_can_filter_food_according_to_their_preference_from_the_Customer_App_or_Website.' => 'إذا كانت هذه الميزة ، يمكن للعملاء تصفية الطعام وفقًا لتفضيلاتهم من تطبيق العميل أو موقع الويب.',
  'Want_to_enable_the' => 'تريد تمكين',
  'âVeg/Non-Vegâ_feature ' => 'ميزة الخضار/غير الخضار',
  'the_Veg/Non-Veg_Feature ' => 'ميزة الخضار/غير الخضار؟',
  'If_you_enable_this _customers_can_filter_food_items_by_choosing_food_from_the_Veg/Non-Veg_feature.' => 'إذا قمت بتمكين هؤلاء العملاء ، فيمكنه تصفية المواد الغذائية عن طريق اختيار الطعام من ميزة الخضار/غير الخضار.',
  'If_you_disable_this _the_Veg/Non-Veg_feature_will_be_hidden_in_the_Customer_App_&_Website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة الخضار/غير الخضار في تطبيق وموقع العميل.',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded _the_Delivery_Fee_is_deducted_from_Adminâs_commission_and_added_to_Adminâs_expense.' => 'تعيين قيمة الحد الأدنى للطلب للتسليم المجاني الآلي. إذا تم تجاوز الحد الأدنى للمبلغ ، يتم خصم رسوم التسليم من لجنة المشرف وإضافتها إلى مصاريف المشرف.',
  'Want_to_enable_Free_Delivery_on_Minimum_Orders ' => 'هل تريد تمكين التوصيل المجاني على الحد الأدنى من الطلبات؟',
  'Want_to_disable_Free_Delivery_on_Minimum_Order ' => 'هل تريد تعطيل التوصيل المجاني على الحد الأدنى للطلب؟',
  'If_you_enable_this _customers_can_get_FREE_Delivery_by_fulfilling_the_minimum_order_requirement.' => 'إذا قمت بتمكين هذا العملاء ، يمكنهم الحصول على توصيل مجاني عن طريق الوفاء بالحد الأدنى من متطلبات الطلب.',
  'If_you_disable_this _the_FREE_Delivery_option_will_be_hidden_from_the_Customer_App_or_Website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء خيار التوصيل المجاني عن تطبيق العميل أو موقع الويب.',
  'Order_Notification_for_Admin' => 'إشعار الطلب للمسؤول',
  'Admin_will_get_a_pop-up_notification_with_sounds_for_any_order_placed_by_customers.' => 'سيحصل المسؤول على إشعار منبثق مع الأصوات لأي طلب يقدمه العملاء.',
  'Want_to_enable' => 'تريد تمكين',
  'Order_Notification_for_Admin ' => 'طلب الإخطار للمسؤول؟',
  'If_you_enable_this _the_Admin_will_receive_a_Notification_for_every_order_placed.' => 'إذا قمت بتمكين هذا ، فسيتلقى المسؤول إشعارًا لكل طلب تم تقديمه.',
  'If_you_disable_this _the_Admin_will_NOT_receive_a_Notification_for_every_order_placed.' => 'إذا قمت بتعطيل هذا ، فلن يتلقى المسؤول إشعارًا لكل طلب تم تقديمه.',
  'order_settings' => 'إعدادات الطلب',
  'When_a_deliveryman_arrives_for_delivery _Customers_will_get_a_4-digit_verification_code_on_the_order_details_section_in_the_Customer_App_and_needs_to_provide_the_code_to_the_delivery_man_to_verify_the_order.' => 'عندما يصل تسليم مان لعملاء التسليم ، سيحصل عملاء التحقق من 4 أرقام في قسم تفاصيل الطلب في تطبيق العميل ويحتاج إلى توفير الرمز لرجل التسليم للتحقق من الطلب.',
  'Delivery_Verification ' => 'التحقق من التسليم؟',
  'If you enable this  the Deliveryman has to verify the order during delivery through a 4-digit verification code.' => 'إذا قمت بتمكين هذا ، فيجب أن يتحقق التسليم من الطلب أثناء التسليم من خلال رمز التحقق المكون من 4 أرقام.',
  'If you disable this  Deliveryman will deliver the order and update the status. He doesnât need to verify the order with any code.' => 'إذا قمت بتعطيل هذا التسليم ، فسيقوم بتوصيل الطلب وتحديث الحالة. لا يحتاج إلى التحقق من الطلب مع أي رمز.',
  'With_this_feature _customers_can_place_an_order_by_uploading_prescription._Stores_can_enable/disable_this_feature_from_the_store_settings_if_needed.' => 'مع هذه الميزة ، يمكن للعملاء تقديم طلب عن طريق تحميل وصفة طبية. يمكن للمتاجر تمكين/تعطيل هذه الميزة من إعدادات المتجر إذا لزم الأمر.',
  'Place_Order_by_Prescription ' => 'وضع طلب بوصفة طبية؟',
  'If_you_enable_this _customers_can_place_an_order_by_simply_uploading_their_prescriptions_in_the_Pharmacy_module_from_the_Customer_App_or_Website._Stores_can_enable/disable_this_feature_from_store_settings_if_needed.' => 'إذا قمت بتمكين هذا العملاء ، فيمكنك تقديم طلب بمجرد تحميل الوصفات الطبية الخاصة بهم في وحدة الصيدلة من تطبيق العميل أو موقع الويب. يمكن للمتاجر تمكين/تعطيل هذه الميزة من إعدادات المتجر إذا لزم الأمر.',
  'If_disabled _this_feature_will_be_hidden_from_the_Customer_App _Website _and_Store_App_&_Panel.' => 'إذا تم تعطيل هذه الميزة ، فسيتم إخفاء هذه الميزة من موقع تطبيق العميل وتطبيق ولوحة تخزين.',
  'Place_Order_by_Prescription' => 'ترتيب مكان بوصفة طبية',
  'If_you_enable_this_feature _customers_can_choose_âHome_Deliveryâ_and_get_the_product_delivered_to_their_preferred_location.' => 'إذا قمت بتمكين هذه الميزة ، فيمكن للعملاء اختيار "توصيل المنازل" وتسليم المنتج إلى موقعهم المفضل.',
  'Home_Delivery ' => 'توصيل منزلي؟',
  'If_you_enable_this _customers_can_use_Home_Delivery_Option_during_checkout_from_the_Customer_App_or_Website.' => 'إذا قمت بتمكين هذا العملاء ، فيمكنك استخدام خيار توصيل المنازل أثناء الخروج من تطبيق العميل أو موقع الويب.',
  'If_you_disable_this _the_Home_Delivery_feature_will_be_hidden_from_the_customer_app_and_website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة توصيل المنازل من تطبيق العميل وموقع الويب.',
  'If_you_enable_this_feature _customers_can_place_an_order_and_request_âTakeawaysâ_or_âself-pick-upâ_from_stores.' => 'إذا قمت بتمكين هذه الميزة ، فيمكن لعملاء تقديم طلب وطلب â exeawaysâ أو Â من الانتقاء الذاتي من المتاجر.',
  'the_Takeaway_feature ' => 'ميزة الوجبات الجاهزة',
  'If_you_enable_this _customers_can_use_the_Takeaway_feature_during_checkout_from_the_Customer_App_or_Website.' => 'إذا قمت بتمكين هذا العملاء ، فيمكنك استخدام ميزة الوجبات السريعة أثناء الخروج من تطبيق العميل أو موقع الويب.',
  'If_you_disable_this _the_Takeaway_feature_will_be_hidden_from_the_Customer_App_or_Website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة الوجبات الجاهزة عن تطبيق العميل أو موقع الويب.',
  'Scheduled_Delivery' => 'التسليم المقرر',
  'With_this_feature _customers_can_choose_their_preferred_delivery_slot._Customers_can_select_a_delivery_slot_for_ASAP_or_a_specific_date_(within_2_days_from_the_order).' => 'مع هذه الميزة ، يمكن للعملاء اختيار فتحة التسليم المفضلة لديهم. يمكن للعملاء تحديد فتحة تسليم لـ ASAP أو تاريخ محدد (في غضون يومين من الطلب).',
  'Scheduled Delivery ' => 'التسليم المقرر؟',
  'If_you_enable_this _customers_can_choose_a_suitable_delivery_schedule_during checkout.' => 'إذا قمت بتمكين هذا العملاء ، فيمكنك اختيار جدول توصيل مناسب أثناء الخروج.',
  'If_you_disable_this _the_Scheduled_Delivery_feature_will_be_hidden.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة التسليم المجدولة.',
  'Time_Interval_for_Scheduled_Delivery' => 'الفاصل الزمني للتسليم المجدول',
  'By_activating_this_feature _customers_can_choose_their_suitable_delivery_slot_according_to_a_30-minute_or_1-hour_interval_set_by_the_Admin.' => 'من خلال تنشيط هذه الميزة ، يمكن للعملاء اختيار فتحة التسليم المناسبة وفقًا لفاصل زمني مدته 30 دقيقة أو ساعة واحدة.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_âCancel_Orderâ_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*لا يمكن للمستخدمين إلغاء طلب ما إذا كان المسؤول لا يحدد سببًا للإلغاء على الرغم من أنهم يرون خيار Â Orderâ. لذلك يجب أن يوفر المسؤول سببًا مناسبًا لإلغاء الطلب وحدد المستخدم ذي الصلة.',
  'Choose_different_users_for_different_Order_Cancellation_Reasons _such_as_Customer _Store _Deliveryman _and_Admin.' => 'اختر مستخدمين مختلفين لأسباب مختلفة لإلغاء الطلبات مثل تسليم متجر العميل والمشرف.',
  'Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.' => 'لا يمكن للعملاء طلب استرداد إذا لم يحدد المسؤول سببًا للإلغاء على الرغم من أنهم يرون خيار ترتيب الإلغاء. لذلك يجب على المسؤول توفير سبب استرداد مناسب وحدد المستخدم ذي الصلة.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*إذا كان المسؤول يمكّنًا من وضع "استرداد طلب طلب المبلغ" طلب استرداد.',
  'If_you_want_to_delete_this_reason _please_confirm_your_decision.' => 'إذا كنت ترغب في حذف هذا السبب ، فيرجى تأكيد قرارك.',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*لا يمكن للعملاء طلب استرداد إذا لم يحدد المسؤول سببًا للإلغاء على الرغم من أنهم يرون خيار ترتيب الإلغاء. لذلك يجب على المسؤول توفير سبب استرداد مناسب وحدد المستخدم ذي الصلة.',
  'Please_confirm_your_decision_if_you_want_to_delete_this_âRefund_Reasonâ.' => 'يرجى تأكيد قرارك إذا كنت ترغب في حذف هذا العقل الاسترداد.',
  'Can_a_Store_Cancel_Order ' => 'هل يمكن للمتجر إلغاء طلب؟',
  'Admin_can_enable/disable_Storeâs_order_cancellation_option.' => 'يمكن للمسؤول تمكين/تعطيل خيار إلغاء طلب المتجر.',
  'A_store_can_send_a_registration_request_through_their_store_or_customer.' => 'يمكن للمتجر إرسال طلب تسجيل من خلال متجرهم أو عميله.',
  'Store_Self_Registration ' => 'تخزين التسجيل الذاتي؟',
  'If_you_enable_this _Stores_can_do_self-registration_from_the_store_or_customer_app_or_website.' => 'إذا قمت بتمكين هذه المتاجر ، فيمكنه القيام بالتسجيل الذاتي من المتجر أو تطبيق العميل أو موقع الويب.',
  'If_you_disable_this _the_Store_Self-Registration_feature_will_be_hidden_from_the_store_or_customer_app _website _or_admin_landing_page.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة التسجيل الذاتي للمتجر عن موقع المتجر أو تطبيق تطبيق العميل أو صفحة مقصودة المشرف.',
  'Tips_for_Deliveryman' => 'نصائح للتسليم',
  'Customers_can_give_tips_to_deliverymen_during_checkout_from_the_Customer_App_&_Website._Admin_has_no_commission_on_it.' => 'يمكن للعملاء تقديم نصائح لتوصيل رجال التسليم أثناء الخروج من تطبيق ويب العميل. المسؤول ليس لديه عمولة على ذلك.',
  'Tips_for_Deliveryman_feature ' => 'نصائح لتوصيل ميزة التوصيل؟',
  'If_you_enable_this _Customers_can_give_tips_to_a_deliveryman_during_checkout.' => 'إذا قمت بتمكين هذا العملاء ، فيمكنه تقديم نصائح إلى رجل توصيل أثناء الخروج.',
  'If_you_disable_this _the_Tips_for_Deliveryman_feature_will_be_hidden_from_the_Customer_App_and_Website.' => 'If you disable this  the Tips for Deliveryman feature will be hidden from the Customer App and Website.',
  'With_this_feature _Deliverymen_can_see_their_earnings_on_a_specific_order_while_accepting_it.' => 'مع هذه الميزة ، يمكن أن يرى رجال التوصيل أرباحهم بطلب محدد أثناء قبوله.',
  'Show_Earnings_in_App ' => 'إظهار الأرباح في التطبيق؟',
  'If_you_enable_this _Deliverymen_can_see_their_earning_per_order_request_from_the_Order_Details_page_in_the_Deliveryman_App.' => 'إذا قمت بتمكين هذا التسليم ، فيمكن أن يروا ربحهم لكل طلب من صفحة تفاصيل الطلب في تطبيق DeliveryMan.',
  'If_you_disable_this _the_feature_will_be_hidden_from_the_Deliveryman_App.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء الميزة من تطبيق DeliveryMan.',
  'With_this_feature _deliverymen_can_register_themselves_from_the_Customer_App _Website_or_Deliveryman_App_or_Admin_Landing_Page._The_admin_will_receive_an_email_notification_and_can_accept_or_reject_the_request.' => 'من خلال هذه الميزة ، يمكن للسلطة تسجيل أنفسهم من موقع تطبيق العميل أو تطبيق Deliveryman أو صفحة المقصاة المشرف. سيتلقى المسؤول إشعارًا بريدًا إلكترونيًا ويمكنه قبول الطلب أو رفضه.',
  'Deliveryman_Self_Registration ' => 'التسليم التسليم الذاتي؟',
  'If_you_enable_this _users_can_register_as_Deliverymen_from_the_Customer_App _Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'إذا قمت بتمكين هؤلاء المستخدمين ، فيمكنك التسجيل كتوصيل من موقع تطبيق العميل أو تطبيق Deliveryman أو صفحة مقصودة المشرف.',
  'If_you_disable_this _this_feature_will_be_hidden_from_the_Customer_App _Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'إذا قمت بتعطيل هذه الميزة ، فسيتم إخفاء هذه الميزة من موقع تطبيق العميل أو تطبيق DeliveryMan أو صفحة المقصودة.',
  'Set_the_maximum_order_limit_a_Deliveryman_can_take_at_a_time.' => 'حدد الحد الأقصى للطلب الذي يمكن أن يستغرقه أحد التسليم في وقت واحد.',
  'Can_A_Deliveryman_Cancel_Order ' => 'هل يمكن لتوصيل الطلب إلغاء أمر إلغاء؟',
  'Admin_can_enable/disable_Deliverymanâs_order_cancellation_option_in_the_respective_app.' => 'يمكن للمسؤول تمكين/تعطيل خيار إلغاء الطلب في التسليم في التطبيق المعني.',
  'Here _customers_can_store_their_refunded_order_amount _referral_earnings _and_loyalty_points.' => 'يمكن للعملاء هنا تخزين أرباح الإحالة المبلغ المسترد ونقاط الولاء.',
  'With_this_feature _customers_can_have_virtual_wallets_in_their_account_via_Customer_App_&_Website._They_can_also_earn_(via_referral _refund _or_loyalty_points)_and_buy_with_the_walletâs_amount.' => 'مع هذه الميزة ، يمكن للعملاء الحصول على محافظ افتراضية في حسابهم عبر تطبيق العميل وموقع الويب. يمكنهم أيضًا كسب (عن طريق استرداد الإحالة أو نقاط الولاء) والشراء بمبلغ المحفظة.',
  'the_Wallet_feature ' => 'ميزة المحفظة؟',
  'If_you_enable_this _Customers_can_see_&_use_the_Wallet_option_from_their_profile_in_the_Customer_App_&_Website.' => 'إذا قمت بتمكين هؤلاء العملاء ، فيمكنك رؤية خيار المحفظة واستخدامها من ملفهم الشخصي في تطبيق ويب العميل.',
  'If_you_disable_this _the_Wallet_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة المحفظة من تطبيق ويب العميل.',
  'If_itâs_enabled _Customers_will_automatically_receive_the_refunded_amount_in_their_wallets._But_if_itâs_disabled _the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'إذا كان العملاء الممكّنون سيتلقى تلقائيًا المبلغ المسترد في محافظهم. ولكن إذا تم تعطيله ، فسيتعامل المسؤول مع طلب الاسترداد في قناة المعاملات المريحة.',
  'Refund_to_Wallet_feature ' => 'استرداد ميزة المحفظة؟',
  'If_you_enable_this _Customers_will_automatically_receive_the_refunded_amount_in_their_wallets.' => 'إذا قمت بتمكين هؤلاء العملاء ، فسيتلقى تلقائيًا المبلغ المسترد في محافظهم.',
  'If_you_disable_this _the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'إذا قمت بتعطيل هذا ، فسيتعامل المسؤول مع طلب الاسترداد في قناة معاملته المريحة.',
  'Existing_Customers_can_share_a_referral_code_with_others_to_earn_a_referral_bonus._For_this _the_new_user_MUST_sign_up_using_the_referral_code_and_make_their_first_purchase.' => 'يمكن للعملاء الحاليين مشاركة رمز الإحالة مع الآخرين لكسب مكافأة الإحالة. لهذا ، يجب على المستخدم الجديد التسجيل باستخدام رمز الإحالة وإجراء عملية الشراء الأولى.',
  'Referral_Earning ' => 'كسب الإحالة؟',
  'If_you_enable_this _Customers_can_earn_points_by_referring_others_to_sign_up_&_purchase_from_your_business.' => 'إذا قمت بتمكين هؤلاء العملاء ، فيمكنه كسب نقاط من خلال إحالة الآخرين للتسجيل والشراء من عملك.',
  'If_you_disable_this _the_referral-earning_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'إذا قمت بتعطيل هذا ، فسيتم إخفاء ميزة كسب الإحالة من تطبيق وموقع العميل.',
  'If_you_activate_this_feature _customers_need_to_verify_their_account_information_via_OTP_during_the_signup_process.' => 'إذا قمت بتنشيط هذه الميزة ، فيجب على العملاء التحقق من معلومات حسابهم عبر OTP أثناء عملية التسجيل.',
  'Customer_Verification ' => 'التحقق من العملاء؟',
  'If_you_enable_this _Customers_have_to_verify_their_account_via_OTP.' => 'إذا قمت بتمكين هذا العملاء على التحقق من حسابهم عبر OTP.',
  'If_you_disable_this _Customers_donât_need_to_verify_their_account_via_OTP.' => 'إذا قمت بتعطيل هؤلاء العملاء ، فلا تحتاج إلى التحقق من حسابهم عبر OTP.',
  'ex_:_Item_is_Broken' => 'على سبيل المثال: العنصر مكسور',
  'add_Business_Module' => 'إضافة وحدة العمل',
  'Add_New_Business_Module' => 'أضف وحدة أعمال جديدة',
  'Business_Module' => 'وحدة الأعمال',
  'Ex:_Grocery eCommerce Pharmacy etc.' => 'على سبيل المثال: البقالة ، التجارة الإلكترونية ، الصيدلة إلخ.',
  '*Set_up_your_New_Business_Module_type_theme_icon_&_thumbnail.' => '*قم بإعداد وحدة أعمالك الجديدة ، النوع ، السمة ، الرمز والثنائي المصغرة.',
  'business_modules' => 'وحدات العمل',
  'business_module_type' => 'نوع وحدة العمل',
  'business_module_type_change_warning' => 'NB: لا يمكنك تغيير نوع الوحدة النمطية في المستقبل بعد حفظ التغييرات.',
  'Write_a_short_description_of_your_new_business_module_within_100_words_(550_characters)' => 'اكتب وصفًا قصيرًا لوحدة الأعمال الجديدة ضمن 100 كلمة (550 حرفًا)',
  'Choose_a_Theme_for_the_module' => 'اختر موضوعًا للوحدة',
  'Add_Module' => 'إضافة الوحدة النمطية',
  'Update_Business_Module' => 'تحديث وحدة الأعمال',
  'Edit_Business_Module' => 'تحرير وحدة الأعمال',
  'choose_theme' => 'اختيار موضوع',
  'business_Modules' => 'وحدات العمل',
  'business_Module_list' => 'قائمة وحدة الأعمال',
  'ex_:_Search_Module_by_Name' => 'على سبيل المثال: وحدة البحث بالاسم',
  'business_Module_type' => 'نوع وحدة العمل',
  'Create_Business_Module' => 'إنشاء وحدة أعمال',
  'To_create_a_new_business_module _go_to:_âModule_Setupâ_â_âAdd_Business_Module.â' => 'لإنشاء وحدة أعمال جديدة ، انتقل إلى: Â MODULE SETUPâ Â adto add',
  'Add_Module_to_Zone' => 'أضف وحدة إلى المنطقة',
  'Go_to_âZone_Setupââ_âBusiness_Zone_Listââ_âZone_Settingsââ_Choose_Payment_MethodâAdd_Business_Module_into_Zone_with_Parameters.' => 'انتقل إلى "إعدادات المنطقة" في قائمة الأعمال - اختيار طريقة الدفع - إضافة وحدة أعمال إلى منطقة مع معلمات.',
  'Create_Stores' => 'إنشاء المتاجر',
  'Select_your_Module_from_the_Module_Section _Click_â_âStore_ManagementâââAdd_StoreââAdd_Store_details_&_select_Zone_to_integrate_Module+Zone+Store.' => 'حدد الوحدة النمطية الخاصة بك من قسم الوحدة النمطية ، انقر فوق "إدارة المتجر" إضافة تفاصيل المتجر وحدد المنطقة لدمج الوحدة النمطية+المنطقة+.',
  'Want_to_activate_this' => 'تريد تفعيل هذا',
  'Business_Module ' => 'وحدة الأعمال؟',
  'Want_to_deactivate_this' => 'تريد إلغاء تنشيط هذا',
  'If_you_activate_this_business_module _all_its_features_and_functionalities_will_be_available_and_accessible_to_all_users.' => 'إذا قمت بتنشيط وحدة العمل هذه ، فستكون جميع ميزاتها ووظائفها متاحة ويمكن الوصول إليها لجميع المستخدمين.',
  'If_you_deactivate_this_business_module _all_its_features_and_functionalities_will_be_disabled_and_hidden_from_users.' => 'إذا قمت بإلغاء تنشيط وحدة العمل هذه ، فسيتم تعطيل جميع ميزاتها ووظائفها وإخفائها من المستخدمين.',
  'Add_New_Business_Zone' => 'أضف منطقة أعمال جديدة',
  'Create_&_connect_dots_in_a_specific_area_on_the_map_to_add_a_new_business_zone.' => 'قم بإنشاء وتوصيل النقاط في منطقة معينة على الخريطة لإضافة منطقة أعمال جديدة.',
  'Use_this_âHand_Toolâ_to_find_your_target_zone.' => 'استخدم أداة اليد هذه للعثور على منطقة المستهدف.',
  'Use_this_âShape_Toolâ_to_point_out_the_areas_and_connect_the_dots._Minimum_3_points/dots_are_required.' => 'استخدم أداة الشكل هذه للإشارة إلى المناطق وتوصيل النقاط. الحد الأدنى 3 نقاط/نقاط مطلوبة.',
  'Choose_your_preferred_language_&_set_your_zone_name.' => 'اختر لغتك المفضلة وقم بتعيين اسم منطقتك.',
  'business_Zone' => 'منطقة الأعمال',
  'Write_a_New_Business_Zone_Name' => 'اكتب اسم منطقة عمل جديدة',
  'Search_Business_Zone' => 'البحث عن منطقة الأعمال',
  'zone_Id' => 'معرف المنطقة',
  'Want_to_disable_âCash_On_Deliveryâ ' => 'تريد تعطيل cash على التسليم "',
  'If_yes _the_Cash_on_Delivery_option_will_be_hidden_during_checkout.' => 'إذا كانت الإجابة بنعم ، فسيتم إخفاء خيار النقد عند التسليم أثناء الخروج.',
  'The_Business_Zone_will_NOT_work_if_you_donât_select_your_business_module_&_payment_method.' => 'لن تعمل منطقة الأعمال إذا لم تختار طريقة عملك وطريقة الدفع.',
  'Want_to_enable_âCash_On_Deliveryâ ' => 'تريد تمكين Â النقد عند التسليم "',
  'If_yes _Customers_can_choose_the_âCash_On_Deliveryâ_option_during_checkout.' => 'إذا كان بإمكان العملاء بنعم اختيار خيار Â Cash at Delivery أثناء الخروج.',
  'New_Business_Zone_Created_Successfully!' => 'منطقة أعمال جديدة تم إنشاؤها بنجاح!',
  'NEXT_IMPORTANT_STEP:_You_need_to_select_âPayment_Methodâ_and_add_âBusiness_Modulesâ_with_other_details_from_the_Zone_Settings._If_you_donât_finish_the_setup _the_Zone_you_created_wonât_function_properly.' => 'الخطوة المهمة التالية: تحتاج إلى تحديد "طريقة الدفع" وإضافة وحدات عمل مع تفاصيل أخرى من إعدادات المنطقة. إذا لم تنهي الإعداد ، فلن تعمل المنطقة التي أنشأتها بشكل صحيح.',
  'Go_to_zone_Settings' => 'انتقل إلى إعدادات المنطقة',
  'Want_to_enable_âDigital_Paymentâ ' => 'تريد تمكين payment "',
  'If_yes _Customers_can_choose_the_âDigital_Paymentâ_option_during_checkout.' => 'إذا كان بإمكان العملاء بنعم اختيار خيار "الدفع الرقمي" أثناء الخروج.',
  'Want_to_disable_âDigital_Paymentâ ' => 'تريد تعطيل "الدفع الرقمي"',
  'If_yes _the_digital_payment_option_will_be_hidden_during_checkout.' => 'إذا كانت الإجابة بنعم ، فسيتم إخفاء خيار الدفع الرقمي أثناء الخروج.',
  'Want_to_Delete_this_Zone ' => 'تريد حذف هذه المنطقة؟',
  'If_yes _all_its_modules _stores _and_products_will_be_DELETED_FOREVER.' => 'إذا كانت الإجابة بنعم ، فسيتم حذف جميع متاجرها ومنتجاتها إلى الأبد.',
  'Want_to_activate_this_Zone ' => 'تريد تفعيل هذه المنطقة؟',
  'Want_to_deactivate_this_Zone ' => 'تريد إلغاء تنشيط هذه المنطقة؟',
  'If_you_activate_this_zone _Customers_can_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'إذا قمت بتنشيط هذه المنطقة ، فيمكن للعملاء رؤية جميع المتاجر والمنتجات المتاحة تحت هذه المنطقة من تطبيق العميل وموقع الويب.',
  'If_you_deactivate_this_zone _Customers_Will_NOT_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'إذا قمت بإلغاء تنشيط هذه المنطقة ، فلن يشاهد العملاء جميع المتاجر والمنتجات المتاحة تحت هذه المنطقة من تطبيق العميل وموقع الويب.',
  'Zone_Settings' => 'إعدادات المنطقة',
  'Select_Payment_Method' => 'اختار طريقة الدفع',
  'NB:_MUST_select_at_least_âoneâ_payment_method.' => 'NB: يجب تحديد طريقة الدفع على الأقل.',
  'Choose_Business_Module' => 'اختر وحدة الأعمال',
  'enter_Amount' => 'أدخل المبلغ',
  'Ex:_Item_is_Broken' => 'على سبيل المثال: العنصر مكسور',
  'When this field is active  user can cancel an order with proper reason.' => 'عندما يكون هذا الحقل نشطًا ، يمكن للمستخدم إلغاء طلب بالسبب الصحيح.',
  'Refund_Reason' => 'سبب استرداد',
  'Admin_Mail_Templates' => 'قوالب بريد المسؤول',
  'Store_Mail_Templates' => 'تخزين قوالب البريد',
  'Delivery_Man_Mail_Templates' => 'قوالب البريد رجل التسليم',
  'User_Mail_Templates' => 'قوالب بريد المستخدم',
  'Want_to_enable_Forget_Password_mail ' => 'هل تريد تمكين نسيان بريد كلمة المرور؟',
  'Want_to_disable_Forget_Password_mail ' => 'تريد تعطيل نسيان بريد كلمة المرور؟',
  'Want_to_enable_Store_Registration_mail ' => 'هل تريد تمكين بريد التسجيل؟',
  'Want_to_disable_Store_Registration_mail ' => 'هل تريد تعطيل بريد التسجيل؟',
  'Want_to_enable_Delivery_Man_Registration_mail ' => 'هل تريد تمكين بريد التسليم رجل التسجيل؟',
  'Want_to_disable_Delivery_Man_Registration_mail ' => 'هل تريد تعطيل بريد التسليم على رجل التسليم؟',
  'Want_to_enable_Withdraw_Request_mail ' => 'تريد تمكين سحب طلب طلب؟',
  'Want_to_disable_Withdraw_Request_mail ' => 'هل تريد تعطيل بريد سحب البريد؟',
  'Want_to_enable_Campaign_Request_mail ' => 'هل تريد تمكين بريد طلب الحملة؟',
  'Want_to_disable_Campaign_Request_mail ' => 'هل تريد تعطيل بريد طلب الحملة؟',
  'Want_to_enable_Refund_Request_mail ' => 'هل تريد تمكين بريد استرداد طلب المبلغ؟',
  'Want_to_disable_Refund_Request_mail ' => 'هل تريد تعطيل بريد رد المبلغ؟',
  'Want_to_enable_Login_mail ' => 'تريد تمكين تسجيل الدخول؟',
  'Want_to_disable_Login_mail ' => 'تريد تعطيل بريد تسجيل الدخول؟',
  'Want_to_enable_Store_approve_mail ' => 'تريد تمكين المتجر الموافقة على البريد؟',
  'Want_to_disable_Store_approve_mail ' => 'تريد تعطيل المتجر الموافقة على البريد؟',
  'Want_to_enable_Store_deny_mail ' => 'هل تريد تمكين المتجر إنكار البريد؟',
  'Want_to_disable_Store_deny_mail ' => 'تريد تعطيل المتجر إنكار البريد؟',
  'Want_to_enable_Withdraw_approve_mail ' => 'تريد تمكين سحب الموافقة على البريد؟',
  'Want_to_disable_Withdraw_approve_mail ' => 'تريد تعطيل سحب الموافقة على البريد؟',
  'Want_to_enable_Withdraw_deny_mail ' => 'هل تريد تمكين سحب البريد رفض؟',
  'Want_to_disable_Withdraw_deny_mail ' => 'هل تريد تعطيل سحب البريد الرفض؟',
  'Want_to_enable_Campaign_Approve_mail ' => 'تريد تمكين الحملة الموافقة على البريد؟',
  'Want_to_disable_Campaign_Approve_mail ' => 'تريد تعطيل الحملة الموافقة على البريد؟',
  'Want_to_enable_Campaign_deny_mail ' => 'تريد تمكين حملة رفض البريد؟',
  'Want_to_disable_Campaign_deny_mail ' => 'تريد تعطيل حملة رفض البريد؟',
  'Want_to_enable_Delivery_Man_approve_mail ' => 'تريد تمكين تسليم رجل الموافقة على البريد؟',
  'Want_to_disable_Delivery_Man_approve_mail ' => 'تريد تعطيل تسليم رجل الموافقة على البريد؟',
  'Want_to_enable_Delivery_Man_deny_mail ' => 'هل تريد تمكين رجل التسليم رفض البريد؟',
  'Want_to_disable_Delivery_Man_deny_mail ' => 'هل تريد تعطيل تسليم رجل إنكار البريد؟',
  'Want_to_enable_Suspend_mail ' => 'تريد تمكين تعليق البريد؟',
  'Want_to_disable_Suspend_mail ' => 'تريد تعطيل البريد تعليق؟',
  'Want_to_enable_cash_collect_mail ' => 'هل تريد تمكين بريد جمع النقد؟',
  'Want_to_disable_cash_collect_mail ' => 'هل تريد تعطيل بريد جمع النقد؟',
  'Want_to_enable_User_Registration_mail ' => 'هل تريد تمكين بريد تسجيل المستخدم؟',
  'Want_to_disable_User_Registration_mail ' => 'هل تريد تعطيل بريد تسجيل المستخدم؟',
  'Want_to_enable_User_login_mail ' => 'هل تريد تمكين بريد تسجيل الدخول إلى المستخدم؟',
  'Want_to_disable_User_login_mail ' => 'هل تريد تعطيل بريد تسجيل الدخول إلى المستخدم؟',
  'Want_to_enable_Order_Verification_mail ' => 'هل تريد تمكين بريد التحقق من الطلب؟',
  'Want_to_disable_Order_Verification_mail ' => 'هل تريد تعطيل بريد التحقق؟',
  'Want_to_enable_Place_Order_mail ' => 'تريد تمكين بريد المكان؟',
  'Want_to_disable_Place_Order_mail ' => 'تريد تعطيل بريد المكان؟',
  'Want_to_enable_Refund_Order_mail ' => 'هل تريد تمكين بريد الاسترداد؟',
  'Want_to_disable_Refund_Order_mail ' => 'هل تريد تعطيل بريد الاسترداد؟',
  'Want_to_enable_Refund_Request_Denied_mail ' => 'هل تريد تمكين طلب الاسترداد رفض البريد؟',
  'Want_to_disable_Refund_Request_Denied_mail ' => 'هل تريد تعطيل طلب الاسترداد الذي تم رفضه؟',
  'Want_to_enable_add_fund_mail ' => 'هل تريد تمكين إضافة بريد الأموال؟',
  'Want_to_disable_add_fund_mail ' => 'هل تريد تعطيل إضافة بريد الأموال؟',
  'Send_Mail_On_âForgot_Passwordâ ' => 'أرسل بريدًا على "نسيان كلمة المرور"',
  'If_a_user_clicks_âForgot_Passwordâ_during_login _an_automated_email_will_be_sent_to_the_admin.' => 'إذا نقر المستخدم على "نسيان كلمة المرور" أثناء تسجيل الدخول ، فسيتم إرسال بريد إلكتروني آلي إلى المسؤول.',
  'Choose_a_related_email_template_theme_for_the_purpose_for_which_you_are_creating_the_email.' => 'اختر موضوع قالب البريد الإلكتروني ذي الصلة للغرض الذي تقوم بإنشائه عبر البريد الإلكتروني.',
  'Select_Theme' => 'اختر نمطا',
  'Choose_Logo' => 'اختر الشعار',
  'Upload_your_company_logo_in_1:1_format._This_will_show_above_the_Main_Title_of_the_email.' => 'قم بتحميل شعار شركتك بتنسيق 1: 1. سيظهر هذا أعلى العنوان الرئيسي للبريد الإلكتروني.',
  'Write_a_Title' => 'اكتب عنوانًا',
  'Give_your_email_a_âCatchy_Titleâ_to_help_the_reader_understand_easily.' => 'امنح بريدك الإلكتروني "عنوانًا جذابًا" لمساعدة القارئ على الفهم بسهولة.',
  'Write_a_message_in_the_Email_Body' => 'اكتب رسالة في هيئة البريد الإلكتروني',
  'you_can_add_your_message_using_placeholders_to_include_dynamic_content._Here_are_some_examples_of_placeholders_you_can_use:' => 'يمكنك إضافة رسالتك باستخدام العناصر النائبة لتضمين المحتوى الديناميكي. فيما يلي بعض الأمثلة على العناصر النائبة التي يمكنك استخدامها:',
  'the_name_of_the_user.' => 'اسم المستخدم.',
  'the_name_of_the_delivery_person.' => 'اسم شخص التسليم.',
  'the_name_of_the_store.' => 'اسم المتجر.',
  'the_order_id.' => 'معرف الطلب.',
  'the_transaction_id.' => 'معرف المعاملة.',
  'Add_Button_&_Link' => 'إضافة زر ورابط',
  'Specify_the_text_and_URL_for_the_button_that_you_want_to_include_in_your_email.' => 'حدد النص وعنوان URL للزر الذي تريد تضمينه في بريدك الإلكتروني.',
  'Change_Banner_Image_if_needed' => 'تغيير صورة لافتة إذا لزم الأمر',
  'Choose_the_relevant_banner_image_for_the_email_theme_you_use_for_this_mail.' => 'اختر صورة اللافتة ذات الصلة لموضوع البريد الإلكتروني الذي تستخدمه لهذا البريد.',
  'Add_Content_to_Email_Footer' => 'أضف محتوى إلى تذييل البريد الإلكتروني',
  'Write_text_on_the_footer_section_of_the_email _and_choose_important_page_links_and_social_media_links.' => 'اكتب نصًا في قسم تذييل البريد الإلكتروني واختر روابط الصفحات المهمة وروابط الوسائط الاجتماعية.',
  'Create_a_copyright_notice' => 'إنشاء إشعار حقوق الطبع والنشر',
  'Include_a_copyright_notice_at_the_bottom_of_your_email_to_protect_your_content.' => 'قم بتضمين إشعار حقوق الطبع والنشر في أسفل بريدك الإلكتروني لحماية المحتوى الخاص بك.',
  'Save_and_publish' => 'حفظ ونشر',
  'Once_you ve_set_up_all_the_elements_of_your_email_template _save_and_publish_it_for_use.' => 'بمجرد إعداد جميع عناصر قالب بريدك الإلكتروني ، احفظه ونشره للاستخدام.',
  'Got_It' => 'فهمتها',
  'Receive_Mail_On_âNew_Store_Registrationâ ' => 'تلقي البريد على "تسجيل المتجر الجديد"',
  'If_a_store_registers_from_the_customer_website_or_app_or_store_app _admin_will_receive_an_automated_email.' => 'إذا سجل المتجر من موقع العميل أو التطبيق أو مسؤول التطبيق ، فسيتلقى مسؤول تطبيق APPEL بريدًا إلكترونيًا تلقائيًا.',
  'If_enabled _the_admin_will_get_an_automated_email_when_a_store_registers.' => 'إذا تم تمكين ، فسيحصل المسؤول على بريد إلكتروني آلي عندما يسجل المتجر.',
  'If_disabled _the_admin_will_not_get_an_automated_email_when_a_store_registers.' => 'إذا تم تعطيله ، فلن يحصل المسؤول على بريد إلكتروني آلي عندما يسجل المتجر.',
  'Receive_Mail_On_âNew_Deliveryman_Registrationâ ' => 'تلقي البريد على "التسليم الجديد" التسجيل "',
  'If_Deliveryman_registers_from_the_customer_App_or_Website_or_Deliveryman_App _Admin_receive_an_automated_email.' => 'إذا سجلت التسليم من تطبيق العميل أو موقع الويب أو APP App Delivery Assist ، يتلقى بريدًا إلكترونيًا تلقائيًا.',
  'If_enabled _the_admin_will_get_an_automated_email_when_a_deliveryman_registers.' => 'إذا تم تمكين ، فسيحصل المسؤول على بريد إلكتروني آلي عندما يسجل تسليم.',
  'If_disabled _the_admin_will_not_get_an_automated_email_when_a_deliveryman_registers.' => 'إذا تم تعطيله ، فلن يحصل المسؤول على بريد إلكتروني آلي عندما يسجل تسليم.',
  'If_a_store_requests_for_a_withdrawal _admin_will_receive_an_automated_email.' => 'إذا طلب المتجر للحصول على مسؤول انسحاب ، فسوف يتلقى بريد إلكتروني آلي.',
  'If_enabled _admin_will_receive_an_email_when_a_store_requests_a_withdrawal.' => 'إذا كان المسؤول الممكّن سيتلقى بريدًا إلكترونيًا عندما يطلب المتجر سحبًا.',
  'If_disabled _admin_will_not_receive_an_email_when_a_store_requests_a_withdrawal.' => 'إذا لم يتلق المسؤول المعاق بريدًا إلكترونيًا عندما يطلب المتجر سحبًا.',
  'Receive_Mail_on_âCampaign_Join_Requestâ ' => 'تلقي البريد على Â حملة انضمام طلب',
  'If_a_store_requests_to_join_campaign_an_automated_email_will_be_sent_to_the_admin.' => 'إذا طلب المتجر الانضمام إلى الحملة ، فسيتم إرسال بريد إلكتروني آلي إلى المسؤول.',
  'If_enabled _the_admin_will_receive_a_mail_when_a_store_requests_to_join_a_campaign.' => 'إذا تم تمكين المشرف ، فسيتلقى المسؤول بريدًا عندما يطلب المتجر الانضمام إلى حملة.',
  'If_disabled _the_admin_will_not_receive_mail_when_a_store_requests_to_join_a_campaign.' => 'إذا تم تعطيله ، فلن يتلقى المسؤول البريد عندما يطلب المتجر الانضمام إلى حملة.',
  'Receive_Mail_on_âRefund_Requestâ ' => 'تلقي البريد على Â REWSTRING requestâ',
  'If_a_customer_requests_a_refund _the_admin_will_receive_an_automated_email_on_the_customer`s_Refund_Request.' => 'إذا طلب العميل استردادًا ، فسيتلقى المسؤول بريدًا إلكترونيًا تلقائيًا على طلب استرداد العميل.',
  'If_enabled _the_admin_will_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website.' => 'إذا تم التمكين ، فسيتلقى المسؤول بريدًا إلكترونيًا عندما يطلب العميل استردادًا من تطبيق العميل أو موقع الويب.',
  'If_disabled _the_admin_will_not_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website. ' => 'إذا تم تعطيله ، فلن يتلقى المسؤول بريدًا إلكترونيًا عندما يطلب العميل استردادًا من تطبيق العميل أو موقع الويب.',
  'Receive_Login_Notification_via_Mail ' => 'تلقي إشعار تسجيل الدخول عبر البريد',
  'If_a_user_login_from_their_respective_system _the_Admin_gets_notified_via_email.' => 'إذا قام المستخدم بتسجيل الدخول من نظامه الخاص ، يتم إخطار المسؤول عبر البريد الإلكتروني.',
  'If_enabled _Admin_will_receive_an_automated_email_on_every_login_from_all_users.' => 'إذا كان المسؤول الممكّن سيتلقى بريدًا إلكترونيًا تلقائيًا على كل تسجيل دخول من جميع المستخدمين.',
  'If_disabled _Admin_will_not_receive_an_automated_email_on_every_login_of_users.' => 'إذا لم يتلق المسؤول المعاق بريدًا إلكترونيًا تلقائيًا على كل تسجيل دخول للمستخدمين.',
  'If_enabled _admin_will_receive_an_email_when_a_user_click_on_âForgot_Password.â' => 'إذا كان المسؤول الممكّن سيتلقى بريدًا إلكترونيًا عندما ينقر المستخدم على "نسيان كلمة المرور."',
  'If_disabled _admin_will_not_receive_an_email_when_a_user_click_on_âForgot_Password.â' => 'إذا لم يتلق المسؤول المعاق بريدًا إلكترونيًا عندما ينقر المستخدم على "نسيان كلمة المرور."',
  'If_a_Store_registers_from_the_Customer_app_or_Website _Admin_Landing_Page_or_Store_app _they_will_get_a_confirmation_email.' => 'إذا سجل المتجر من تطبيق العميل أو صفحة Handing Admin أو تطبيق تخزين الموقع ، فسوف يحصلون على بريد إلكتروني للتأكيد.',
  'If_enabled _stores_will_get_a_registration_confirmation_email_when_they_register.' => 'إذا كانت المتاجر الممكّرة ستحصل على بريد إلكتروني لتأكيد التسجيل عند التسجيل.',
  'If_disabled _stores_will_not_get_a_registration_confirmation_email_when_a_store_registers.' => 'إذا لم تحصل المتاجر المعطلة على بريد إلكتروني لتأكيد التسجيل عند سجل المتجر.',
  'New_Store_Approval' => 'موافقة المتجر الجديدة',
  'Send_Mail_on_New_Store_Approval ' => 'أرسل بريدًا بموافقة المتجر الجديدة',
  'If_Admin_accepts_a_Storeâs_self-registration _the_store_will_get_an_automatic_approval_mail_from_the_system.' => 'إذا قبل المسؤول عن التسجيل الذاتي للمتجر ، فسوف يحصل المتجر على بريد موافقة تلقائي من النظام.',
  'If_enabled _Users_will_get_a_confirmation_email_when_the_Admin_approves_the_registration.' => 'إذا سيحصل المستخدمون الممكّنون على بريد إلكتروني للتأكيد عندما يوافق المسؤول على التسجيل.',
  'If_disabled _Users_will_not_get_a_registration_approval_email.' => 'إذا لم يحصل المستخدمون المعاقون على بريد إلكتروني للموافقة على التسجيل.',
  'New_Store_Rejection' => 'رفض المتجر الجديد',
  'Send_Mail_on_âNew_Store_Rejectionâ ' => 'أرسل بريدًا على "رفض المتجر الجديد"',
  'If_Admin_rejects_a_Storeâs_self-registration _the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'إذا رفض المسؤول التسجيل الذاتي للمتجر ، فسوف يحصل المتجر على بريد الرفض التلقائي من النظام.',
  'If_enabled _Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_registration_request.' => 'إذا حصل المستخدمون الممكّنون على بريد إلكتروني للتأكيد عندما يرفض المسؤول طلب التسجيل الخاص بهم.',
  'If_disabled __Users_will_not_get_a_registration_rejection_mail.' => 'إذا لم يحصل المستخدمون المعاقون على بريد رفض التسجيل.',
  'If disabled  Stores will not receive any Withdraw Approval mail.' => 'إذا لم تتلقى المتاجر المعوقة أي بريد موافقة سحب.',
  'Withdraw_Approval' => 'سحب الموافقة',
  'Send_Mail_On_Withdraw_approve ' => 'أرسل البريد عند الموافقة على السحب',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_approves_it _the_Store_will_get_an_automated_Withdraw_Approval_email_from_the_system' => 'إذا طلب المتجر للانسحاب ووافق المسؤول على ذلك ، سيحصل المتجر على بريد إلكتروني آلي للموافقة على السحب من النظام',
  'If_enabled _Stores_will_receive_an_approval_mail_for_requesting_a_withdrawal.' => 'إذا كانت المتاجر التي تم تمكينها ستتلقى بريدًا موافقة لطلب السحب.',
  'If_disabled _Stores_will_not_receive_any_Withdraw_Approval_mail.' => 'إذا لم تتلقى المتاجر المعوقة أي بريد موافقة سحب.',
  'Withdraw_Rejection' => 'سحب الرفض',
  'Send_Mail_on_âWithdraw_Rejectionâ ' => 'إرسال بريد عند رفض الرفض "',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_rejects_it _the_Store_will_get_an_automated_Withdraw_Rejection_email_from_the_system.' => 'إذا طلب المتجر سحب السحب ورفضه المسؤول ، فسوف يحصل المتجر على بريد إلكتروني لرفض السحب التلقائي من النظام.',
  'If_enabled _Stores_will_not_receive_any_Withdrawal_Rejection_mail.' => 'إذا لم تتلقى المتاجر الممكّنة أي بريد رفض سحب.',
  'If_disabled _Stores_will_receive_an_automated_mail_from_the_system_when_the_Admin_Rejects_their_Withdraw_Request.' => 'إذا كانت المتاجر المعوقة ستتلقى بريدًا آليًا من النظام عندما يرفض المسؤول طلب السحب الخاص بهم.',
  'If_a_Store_requests_to_join_a_campaign _they_will_receive_an_automated_mail_for_successful_registration.' => 'إذا طلب المتجر الانضمام إلى حملة ، فسيتلقى بريدًا آليًا للتسجيل الناجح.',
  'If_enabled _Stores_will_receive_an_automated_confirmation_mail_that_their_join_request_is_successful.' => 'إذا كانت المتاجر التي تم تمكينها ستتلقى بريدًا تأكيدًا تلقائيًا ، فإن طلب انضمامهم قد ناجح.',
  'If_disabled _Stores_will_not_receive_any_confirmation_mail_on_campaign_join_request.' => 'إذا لم تتلقى المتاجر المعوقة أي بريد تأكيد على طلب الانضمام إلى الحملة.',
  'Campaign_Join_Approval' => 'حملة الانضمام إلى الموافقة',
  'Campaign_Join_Rejection' => 'حملة انضم إلى الرفض',
  'Send_Mail_on_âCampaign_Join_Approvalâ ' => 'أرسل بريدًا على "حملة الانضمام إلى الموافقة"',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_approves_their_joining_request _they_get_an_automated_Approval_email_from_the_system.' => 'إذا طلب المتجر الانضمام إلى حملة وموافقة المسؤول على طلب الانضمام ، فإنهم يحصلون على بريد إلكتروني للموافقة الآلية من النظام.',
  'If_enabled _Stores_will_receive_an_email_when_Admin_approves_their_Campaign_Join_Request.' => 'إذا كانت المتاجر التي تم تمكينها ستتلقى بريدًا إلكترونيًا عندما يوافق المسؤول على طلب الانضمام إلى حملتهم.',
  'Send_Mail_on_âCampaign_Join_Rejectionâ ' => 'أرسل بريدًا على "حملة انضم إلى الرفض"',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_rejects_their_joining_request _they_will_get_an_automated_Rejection_email_from_the_system.' => 'إذا طلب أحد المتجر الانضمام إلى حملة ورفض المسؤول طلب الانضمام الخاص بهم ، فسوف يحصلون على بريد إلكتروني للرفض الآلي من النظام.',
  'If_enabled _Stores_will_receive_an_email_on_campaign_joining_Rejection.' => 'إذا كانت المتاجر التي تم تمكينها ستتلقى بريدًا إلكترونيًا على الرفض.',
  'If_disabled _Stores_will_not_receive_any_email_on_campaign_joining_Rejection.' => 'إذا لم تتلقى المتاجر المعوقة أي بريد إلكتروني على الرفض.',
  'New_Deliveryman_Registration' => 'التسليم الجديد التسليم',
  'New_Deliveryman_Approval' => 'موافقة توصيل جديدة',
  'New_Deliveryman_Rejection' => 'الرفض الجديد لتوصيل',
  'Account_Suspension' => 'تعليق الحساب',
  'Cash_Collection' => 'استلام النقود',
  'Forgot_Password' => 'هل نسيت كلمة السر',
  'Send_Mail_on_New_Deliveryman_Registration ' => 'أرسل بريدًا على تسجيل التسليم الجديد',
  'If_a_Deliveryman_registers_from_the_Customer_app_or_Website _Admin_Landing_Page_or_Store_app _they_will_get_a_Registration_Confirmation_email.' => 'إذا قام أحد التسليم بسجلات من تطبيق العميل أو صفحة HANDING ADMILE أو تطبيق المتجر ، فسوف يحصلون على بريد إلكتروني لتأكيد التسجيل.',
  'If_enabled _Deliverymen_will_receive_an_automated_mail_from_the_system_when_their_registration_is_successful.' => 'إذا كان التسليم الممكّن سيتلقون بريدًا تلقائيًا من النظام عندما يكون تسجيلهم ناجحًا.',
  'If_disabled _Deliverymen_will_not_receive_any_registration_confirmation_email.' => 'إذا لم يتلق رجال التسليم المعاق أي بريد إلكتروني لتأكيد التسجيل.',
  'Send_Mail_on_New_Deliveryman_Rejection ' => 'أرسل البريد على رفض التوصيل الجديد',
  'If_Admin_rejects_a_Deliverymanâs_self-registration _the_Deliveryman_will_get_an_automatic_rejection_mail.' => 'إذا رفض المسؤول التسجيل الذاتي لتوصيله ، فسوف يحصل تسليم مان على بريد رفض تلقائي.',
  'If_enabled __Users_will_receive_an_email_when_the_admin_rejects_their_registration_request.' => 'إذا كان المستخدمون سيتلقون بريدًا إلكترونيًا عندما يرفض المسؤول طلب التسجيل الخاص بهم.',
  'If_disabled _Users_will_not_receive_any_email_upon_rejection_for_registration.' => 'إذا لم يتلق المستخدمون المعاقون أي بريد إلكتروني عند رفض التسجيل.',
  'Send_Mail_On_Deliverymanâs_âAccount_Suspensionâ ' => 'أرسل بريدًا عند التسليم على حساب Assuction "',
  'If_Store/Admin_wants _they_can_suspend_a_Deliverymanâs_account._If_a_Store_or_Admin_suspends_a_Deliverymanâs_account _he_will_receive_an_automated_email.' => 'إذا أراد المتجر/المشرف ، فيمكنهم تعليق حساب التوصيل. إذا قام أحد المتجر أو المسؤول بتعليق حساب التسليم ، فسيتلقى بريدًا إلكترونيًا تلقائيًا.',
  'If_enabled _deliverymen_will_receive_an_email_for_account_suspension.' => 'إذا كان التسليم الممكّن سيتلقون بريدًا إلكترونيًا لتعليق الحساب.',
  'If_disabled _deliverymen_will_not_receive_an_email_for_account_suspension.' => 'إذا لم يتلق رجال التسليم المعاق بريدًا إلكترونيًا لتعليق الحساب.',
  'Send_Mail_on_âCash_Collectionâ ' => 'أرسل بريدًا على Â Cash Collectionâ',
  'If_Admin_or_Store_collects_cash_from_a_Deliveryman _he_will_receive_an_automated_email_from_the_system_showing_how_much_cash_is_collected.' => 'إذا قام المسؤول أو المتجر بجمع النقود من رجل التسليم ، فسوف يتلقى بريدًا إلكترونيًا تلقائيًا من النظام يوضح مقدار النقود التي يتم جمعها.',
  'If_enabled _the_Deliveryman_will_receive_an_email_after_the_Admin/Store_collects_cash_from_him.' => 'إذا تم تمكين التسليم ، فسيتلقى مان مان مكونًا بريدًا إلكترونيًا بعد أن يقوم المسؤول/المتجر بجمع النقود منه.',
  'If_disabled _the_Deliveryman_will_not_receive_any_email_on_Cash_Collection.' => 'إذا تم تعطيله ، فلن يتلقى Servinationman أي بريد إلكتروني على جمع النقد.',
  'If_a_Deliveryman_tap_on_âForgot_Passwordâ_during_login _an_automated_email_will_be_sent_from_the_system_with_a_Reset_Password_Link.' => 'إذا انقر فوق "تسليم" على "نسيان كلمة المرور" أثناء تسجيل الدخول ، فسيتم إرسال بريد إلكتروني آلي من النظام مع رابط إعادة تعيين كلمة مرور.',
  'If_enabled _the_Deliveryman_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'إذا تم التمكين ، فسيتلقى Servinationman رسالة بريد إلكتروني تلقائية مع رابط كلمة مرور إعادة تعيين.',
  'If_disabled _the_Deliveryman_will_not_receive_any_for_password_reset.' => 'إذا تم تعطيله ، فلن يتلقى Deliveryman أي إعادة تعيين كلمة المرور.',
  'Customer_Mail_Templates' => 'قوالب بريد العميل',
  'New_Customer_Registration' => 'تسجيل العملاء الجديد',
  'Receive_Mail_On_âNew_Customer_Registrationâ ' => 'تلقي البريد على "تسجيل العملاء الجديد"',
  'If_a_user_registers_or_sign_up_from_the_Customer_App_or_Website _they_will_receive_an_automated_confirmation.' => 'إذا سجل المستخدم أو التسجيل من تطبيق العميل أو موقع الويب ، فسيتلقى تأكيدًا تلقائيًا.',
  'If_enabled _customers_will_receive_a_confirmation_email_that_their_registration_was_successful.' => 'إذا كان العملاء الممكّبين سيتلقون رسالة تأكيد بريد إلكتروني بأن تسجيلهم كان ناجحًا.',
  'If_disabled _customers_will_receive_a_registration_confirmation_email.' => 'إذا كان العملاء المعوقين سيتلقون بريدًا إلكترونيًا لتأكيد التسجيل.',
  'Send_Mail_On_âRegistration_OTPâ ' => 'إرسال البريد على Â التسجيل otpâ',
  'Customers_will_receive_an_automated_email_with_an_OTP_to_confirm_their_registration.' => 'سيتلقى العملاء بريدًا إلكترونيًا تلقائيًا مع OTP لتأكيد تسجيلهم.',
  'If_enabled _Customers_will_receive_OTP_in_their_mail_to_confirm_registration.' => 'إذا كان العملاء الممكّنون سيتلقون OTP في بريدهم لتأكيد التسجيل.',
  'If_disabled _Customers_will_not_receive_any_email_on_registration_OTP.' => 'إذا لم يتلق العملاء المعاقون أي بريد إلكتروني على التسجيل OTP.',
  'Send_Mail_On_âLogin_OTPâ ' => 'إرسال البريد على Â تسجيل الدخول otpâ',
  'Customers_will_receive_an_OTP_every_time_they_log_in_to_their_account_via_the_Customer_App_or_Website.' => 'سيتلقى العملاء OTP في كل مرة يقومون فيها بتسجيل الدخول إلى حسابهم عبر تطبيق العميل أو موقع الويب.',
  'If_enabled _customers_will_receive_a_login_OTP_email_every_time_they_log_in_to_their_account.' => 'إذا كان العملاء الممكّنون سيتلقون بريدًا إلكترونيًا تسجيل الدخول OTP في كل مرة يقومون فيها بتسجيل الدخول إلى حسابهم.',
  'If_disabled _customers_will_not_receive_any_OTP_email_during_Login.' => 'إذا لم يتلق العملاء المعاقون أي بريد إلكتروني لـ OTP أثناء تسجيل الدخول.',
  'Delivery_Verification' => 'التحقق من التسليم',
  'Send_Mail_On_âDelivery_Verificationâ ' => 'إرسال بريد عند التحقق من التسليم "',
  'Customers_will_receive_a_Delivery_Verification_code_via_email_during_delivery._The_Customer_then_gives_the_code_to_the_Deliveryman_to_confirm_delivery.' => 'سيتلقى العملاء رمز التحقق من التسليم عبر البريد الإلكتروني أثناء التسليم. ثم يعطي العميل الرمز إلى ServinationMan لتأكيد التسليم.',
  'If_enabled _Customers_will_receive_a_Verification_code_via_mail_during_delivery_and_Deliveryman_can_verify_the_order_with_the_given_code.' => 'إذا كان العملاء الممكّنون سيتلقون رمز التحقق عبر البريد أثناء التسليم ويمكن أن يتحقق مان لتوصيله من الرمز المحدد.',
  'If_disabled _Customers_will_not_receive_any_Verification_code_via_mail_for_delivery_verification.' => 'إذا لم يتلق العملاء المعوقين أي رمز التحقق عبر البريد للتحقق من التسليم.',
  'Order_Placement' => 'وضع الترتيب',
  'Send_Mail_On_âOrder_Placementâ ' => 'إرسال البريد على Â Order Placementâ',
  'Customers_will_receive_an_automated_email_after_a_successful_order_placement.' => 'سيتلقى العملاء بريدًا إلكترونيًا تلقائيًا بعد وضع طلب ناجح.',
  'If_enabled _customers_will_get_an_automatic_confirmation_mail_for_successful_Order_Placement_with_an_invoice.' => 'إذا كان العملاء الممكّنون سيحصلون على بريد تأكيد تلقائي لوضع طلب ناجح مع فاتورة.',
  'If_disabled _customers_will_NOT_get_any_Order_Placement_email.' => 'إذا لم يحصل العملاء المعاقون على أي بريد إلكتروني في وضع الطلب.',
  'Customers_will_get_an_automated_email_when_they_receive_a_refund_to_their_wallet_from_Admin_with_refund_details.' => 'سيحصل العملاء على بريد إلكتروني آلي عندما يتلقون استردادًا لمحفظتهم من المسؤول مع تفاصيل رد الأموال.',
  'If_enabled _Customers_will_get_an_automated_email_when_they_receive_a_refund.' => 'إذا كان العملاء الممكّنون سيحصلون على بريد إلكتروني تلقائي عند استلامهم للاسترداد.',
  'If_disabled _Customers_will_not_receive_any_mail_on_Refund_Orders.' => 'إذا لم يتلق العملاء المعاقون أي بريد على أوامر الاسترداد.',
  'If_a_Customer_clicks_on_âForgot_Passwordâ_during_login _an_automated_email_will_be_sent_with_a_Reset_Password_Link.' => 'إذا نقر العميل على "نسيان كلمة المرور" أثناء تسجيل الدخول ، فسيتم إرسال بريد إلكتروني آلي باستخدام رابط إعادة تعيين كلمة مرور.',
  'If_enabled _the_Customer_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'إذا تم تمكين العميل ، فسيتلقى العميل بريدًا إلكترونيًا تلقائيًا مع رابط كلمة مرور إعادة تعيين.',
  'Refund_Request_Rejected' => 'طلب استرداد الرفض',
  'Fund_Add' => 'صندوق إضافة',
  'Send_Mail_On_âRefund_Request_Rejectedâ ' => 'أرسل بريدًا على Â REWSTREAD request rejected "',
  'Customers_will_receive_an_automated_mail_from_the_system_if_the_Admin_rejects_their_Refund_Request.' => 'سيتلقى العملاء بريدًا تلقائيًا من النظام إذا رفض المسؤول طلب الاسترداد الخاص بهم.',
  'If_enabled _Customers_will_receive_a_mail_when_Admin_rejects_their_Refund_Request.' => 'إذا كان العملاء الممكّنون سيتلقون بريدًا عندما يرفض المسؤول طلب الاسترداد الخاص بهم.',
  'If_disabled _Customers_will_not_receive_any_mail_for_Refund_Request_rejection.' => 'إذا لم يتلق العملاء المعوقين أي بريد لرفض طلب المبلغ المبلغ.',
  'Send_Mail_On_âFund_Addâ ' => 'إرسال البريد على Â صندوق إضافة "',
  'Customers_will_receive_an_automated_mail_from_the_system_when_Admin_add_fund_to_their_Wallet.' => 'سيتلقى العملاء بريدًا تلقائيًا من النظام عند إضافة صندوق إضافة محفظتهم.',
  'If_enabled _customers_will_receive_an_email_when_Admin_adds_funds_to_their_wallet.' => 'إذا كان العملاء الممكّنون سيتلقون بريدًا إلكترونيًا عندما يضيف المسؤول أموالًا إلى محفظتهم.',
  'If_disabled _Customers_will_not_receive_an_email_on_Added_Funds.' => 'إذا لم يتلق العملاء المعاقون رسالة بريد إلكتروني على أموال مضافة.',
  'When_disabled _item_management_feature_will_be_hidden_from_store_panel_&_store_app' => 'عندما يتم إخفاء ميزة إدارة العناصر المعوقة من تطبيق متجر المتجر وتطبيق المتجر',
  'When_enabled _store_owners_can_see_customer_feedback_in_the_store_panel_&_store_app.' => 'عند تمكين ، يمكن لأصحاب المتاجر رؤية ملاحظات العملاء في لوحة المتجر وتطبيق المتجر.',
  'Enable_or_Disable_Point_of_Sale_(POS)_in_the_store_panel.' => 'تمكين أو تعطيل نقطة البيع (POS) في لوحة المتجر.',
  'When_enabled _store_owner_can_take_scheduled_orders_from_customers.' => 'عند تمكين مالك المتجر ، يمكنه أخذ الطلبات المجدولة من العملاء.',
  'Store-managed_Delivery' => 'تسليم تديره المتجر',
  'When_this_option_is_enabled _stores_must_deliver_orders_using_their_own_deliverymen._Plus _stores_will_get_the_option_to_add_their_own_deliverymen_from_the_store_panel.' => 'عندما يتم تمكين هذا الخيار ، يجب على المتاجر تقديم الطلبات باستخدام رجال التسليم الخاص بهم. ستحصل المتاجر بالإضافة إلى خيار إضافة رجال التسليم الخاص بهم من لوحة المتجر.',
  'When_enabled _customers_can_make_home_delivery_orders_from_this_store.' => 'عند تمكين العملاء ، يمكن للعملاء تقديم طلبات توصيل المنازل من هذا المتجر.',
  'takeaway' => 'يبعد',
  'When_enabled _customers_can_place_takeaway_orders_from_this_store.' => 'عند تمكين العملاء ، يمكن للعملاء وضع أوامر الوجبات الجاهزة من هذا المتجر.',
  'Specify_the_minimum_order_amount_required_for_customers_when_ordering_from_this_store.' => 'حدد الحد الأدنى لمبلغ الطلب المطلوب للعملاء عند الطلب من هذا المتجر.',
  'Set_the_total_time_to_process_the_order_after_order_confirmation.' => 'حدد إجمالي الوقت لمعالجة الطلب بعد تأكيد الطلب.',
  'Set_the_total_time_to_deliver_products.' => 'تعيين إجمالي الوقت لتقديم المنتجات.',
  'When_enabled _admin_will_only_receive_the_certain_commission_percentage_he_set_for_this_store._Otherwise _the_system_default_commission_will_be_applied.' => 'عند تمكين المشرف سيتلقى فقط نسبة العمولة المعينة التي حددها لهذا المتجر. وإلا سيتم تطبيق اللجنة الافتراضية للنظام.',
  'Want_to_delete_this_schedule ' => 'تريد حذف هذا الجدول؟',
  'If_you_select_Yes _the_time_schedule_will_be_deleted' => 'إذا حددت نعم ، فسيتم حذف الجدول الزمني.',
  'Define_the_food_type_this_store_can_sell.' => 'تحديد نوع الطعام الذي يمكن أن يبيعه هذا المتجر.',
  'See_how_it_works!' => 'انظر كيف يعمل!',
  'Visit_Now' => 'زيارة الآن',
  'Ex_:_Manage_your_daily_life_on_one_platform' => 'على سبيل المثال: إدارة حياتك اليومية على منصة واحدة',
  'Write_the_title_within_50_characters' => 'اكتب العنوان ضمن 50 حرفًا',
  'Write_the_sub_title_within_50_characters' => 'اكتب العنوان الفرعي ضمن 50 حرفًا',
  'Ex_:_More_than_just_a_reliable_eCommerce_platform' => 'على سبيل المثال: أكثر من مجرد منصة للتجارة الإلكترونية الموثوقة',
  'Ex_:_Your_eCommerce_venture_starts_here' => 'على سبيل المثال: يبدأ مشروع التجارة الإلكترونية هنا',
  'Ex_:_Enjoy_all_services_in_one_platform' => 'على سبيل المثال: استمتع بجميع الخدمات في منصة واحدة',
  'NB_:_All_the_modules_and_their_information_will_be_dynamically_added_from_the_module_setup_section._You_just_need_to_add_the_title_and_subtitle_of_the_Module_List_Section.' => 'NB: سيتم إضافة جميع الوحدات ومعلوماتها ديناميكيًا من قسم إعداد الوحدة النمطية. تحتاج فقط إلى إضافة العنوان وبين الترجمة لقسم قائمة الوحدات النمطية.',
  'Ex_:_Earn_Point' => 'على سبيل المثال: كسب نقطة',
  'Ex_:_By_referring_your_friend' => 'على سبيل المثال: من خلال إحالة صديقك',
  'Promotional_Banner_List' => 'قائمة لافتة ترويجية',
  'Features_List' => 'قائمة الصور',
  'Ex_:_Remarkable_Features_that_You_Can_Count' => 'على سبيل المثال: ميزات رائعة يمكنك حسابها',
  'Ex_:_Jam-packed_with_outstanding_featuresâ¦' => 'على سبيل المثال: مربى مع ميزات رائعة',
  'Ex_:_Shopping' => 'على سبيل المثال: التسوق',
  'Ex_:_Best_shopping_experience' => 'على سبيل المثال: أفضل تجربة تسوق',
  'See_the_changes_here.' => 'انظر التغييرات هنا.',
  'Download_Store_App_Section' => 'تنزيل قسم تطبيق المتجر',
  'When_disabled _the_Play_Store_download_button_will_be_hidden_from_the_landing_page' => 'عند تعطيل ، سيتم إخفاء زر تنزيل متجر Play من الصفحة المقصودة',
  'Want_to_enable_the_Play_Store_button_for_Store_App ' => 'تريد تمكين زر متجر التشغيل لتطبيق المتجر',
  'Want_to_disable_the_Play_Store_button_for_Store_App ' => 'تريد تعطيل زر متجر التشغيل لتطبيق المتجر',
  'If_enabled _the_Store_app_download_button_will_be_visible_on_the_Landing_page.' => 'إذا تم التمكين ، فسيكون زر تنزيل تطبيق المتجر مرئيًا على الصفحة المقصودة.',
  'If_disabled _this_button_will_be_hidden_from_the_landing_page.' => 'إذا تم تعطيل هذا الزر سيتم إخفاءه من الصفحة المقصودة.',
  'When_disabled _the_App_Store_download_button_will_be_hidden_from_the_landing_page' => 'عند تعطيل ، سيتم إخفاء زر تنزيل متجر التطبيقات من الصفحة المقصودة',
  'Want_to_enable_the_App_Store_button_for_Store_App ' => 'تريد تمكين زر متجر التطبيقات لتطبيق المتجر',
  'Want_to_disable_the_App_Store_button_for_Store_App ' => 'تريد تعطيل زر متجر التطبيق لتطبيق المتجر',
  'Download_Deliveryman_App_Section' => 'قسم تطبيق تنزيل التسليم',
  'Want_to_enable_the_Play_Store_button_for_Deliveryman_App ' => 'تريد تمكين زر متجر Play للتسليم',
  'Want_to_disable_the_Play_Store_button_for_Deliveryman_App ' => 'تريد تعطيل زر متجر Play للتسليم',
  'If_enabled _the_Deliveryman_app_download_button_will_be_visible_on_the_Landing_page.' => 'إذا تم التمكين ، فسيكون زر تنزيل تطبيق Deliveryman مرئيًا على الصفحة المقصودة.',
  'Want_to_enable_the_App_Store_button_for_Deliveryman_App ' => 'تريد تمكين زر متجر التطبيقات لتطبيق التسليم',
  'Want_to_disable_the_App_Store_button_for_Deliveryman_App ' => 'تريد تعطيل زر متجر التطبيقات للتسليم',
  'Text_:_Icon_ratio_(1:1)_and_max_size_2_MB.' => 'النص: نسبة الأيقونة (1: 1) وحجم أقصى 2 ميغابايت.',
  'Want_to_enable_this_feature ' => 'تريد تمكين هذه الميزة',
  'Want_to_disable_this_feature ' => 'تريد تعطيل هذه الميزة',
  'It_will_be_available_on_the_landing_page.' => 'سيكون متاحًا على الصفحة المقصودة.',
  'It_will_be_hidden_from_the_landing_page.' => 'سيتم إخفاءه من الصفحة المقصودة.',
  'see_the_changes_here' => 'انظر التغييرات هنا',
  'Write_the_title_within_200_characters' => 'اكتب العنوان ضمن 200 حرف',
  'Ex_:_Contact_Us' => 'على سبيل المثال: اتصل بنا',
  'Ex_:_Any_questions_or_remarks_ _Just_write_us_a_message!' => 'على سبيل المثال: أي أسئلة أو ملاحظات فقط اكتب لنا رسالة!',
  'cancellation_policy' => 'سياسة الإلغاء',
  'status updated!' => 'تم تحديث الحالة!',
  'cancellation_policy_updated' => 'تحديث سياسة الإلغاء',
  'refund_policy' => 'سياسة الاسترجاع',
  'Minimum_User_App_Version' => 'أدنى إصدار تطبيق المستخدم',
  'The_minimum_user_app_version_required_for_the_app_functionality.' => 'الحد الأدنى لإصدار تطبيق المستخدم المطلوب لوظيفة التطبيق.',
  'Download_URL_for_User_App' => 'تنزيل عنوان URL لتطبيق المستخدم',
  'Users_will_download_the_latest_user_app_version_using_this_URL.' => 'سيقوم المستخدمون بتنزيل أحدث إصدار من تطبيقات المستخدم باستخدام عنوان URL هذا.',
  'Store_App_Version_Control' => 'تخزين التحكم في إصدار التطبيق',
  'Minimum_Store_App_Version' => 'أدنى إصدار تطبيق المتجر',
  'The_minimum_store_app_version_required_for_the_app_functionality.' => 'الحد الأدنى لإصدار تطبيق المتجر المطلوب لوظيفة التطبيق.',
  'Download_URL_for_Store_App' => 'تنزيل URL لتطبيق المتجر',
  'Users_will_download_the_latest_store_app_using_this_URL.' => 'سيقوم المستخدمون بتنزيل أحدث تطبيق للمتجر باستخدام عنوان URL هذا.',
  'Users_will_download_the_latest_store_app_version_using_this_URL.' => 'سيقوم المستخدمون بتنزيل أحدث إصدار لتطبيق المتجر باستخدام عنوان URL هذا.',
  'Deliveryman_App_Version_Control' => 'التحكم في إصدار App App Deliveryman',
  'Minimum_Deliveryman_App_Version' => 'إصدار تطبيق الحد الأدنى للتسليم',
  'The_minimum_deliveryman_app_version_required_for_the_app_functionality.' => 'الحد الأدنى لإصدار تطبيق Deliveryman المطلوب لوظيفة التطبيق.',
  'Download_URL_for_Deliveryman_App' => 'قم بتنزيل URL للتسليم',
  'Users_will_download_the_latest_deliveryman_app_version_using_this_URL.' => 'سيقوم المستخدمون بتنزيل أحدث إصدار تطبيق DeliveryMan باستخدام عنوان URL هذا.',
  'What_is_App_Version ' => 'ما هو إصدار التطبيق',
  'This_app_version_defines_the_Store _Deliveryman _and_User_app_version_of_6amMart.' => 'يعرّف إصدار التطبيق هذا تسليم المتجر وإصدار تطبيق المستخدم من 6Ammart.',
  'It_doesnât_represent_the_Play_Store_or_App_Store_version.' => 'لا يمثل إصدار متجر Play أو App Store.',
  'The_app_download_link_is_the_URL_from_which_users_can_update_the_app_by_clicking_the_`Update_App`_button_from_their_app.' => 'رابط تنزيل التطبيق هو عنوان URL الذي يمكن للمستخدمين من خلاله تحديث التطبيق بالنقر فوق الزر "Update App" من تطبيقهم.',
  'Admin_login_page' => 'صفحة تسجيل الدخول المشرف',
  'Icon_ratio_(1:1)_and_max_size_2_MB.' => 'نسبة الأيقونة (1: 1) وحجم أقصى 2 ميغابايت.',
  'Special_Feature_Section ' => 'قسم ميزة خاصة',
  'Special_Feature_List_Section ' => 'قسم قائمة الميزات الخاصة',
  'Want_to_delete_this_feature_ ' => 'تريد حذف هذه الميزة',
  'If_yes _It_will_be_removed_from_this_list_and_the_landing_page.' => 'إذا كانت الإجابة بنعم ، فسيتم إزالتها من هذه القائمة والصفحة المقصودة.',
  'this_feature ' => 'هذه الميزة',
  'If_yes _it_will_be_available_on_the_landing_page.' => 'إذا كانت الإجابة بنعم ، فسيكون متاحًا على الصفحة المقصودة.',
  'If_yes _it_will_be_hidden_from_the_landing_page.' => 'إذا كانت الإجابة بنعم ، فسيتم إخفاؤها من الصفحة المقصودة.',
  'Join_as_a_Seller_Section' => 'انضم إلى قسم البائع',
  'Write_the_title_within_20_characters' => 'اكتب العنوان ضمن 20 حرفًا',
  'The_website_page_where_people_will_register_as_sellers.' => 'صفحة الموقع حيث سيقوم الأشخاص بالتسجيل كبائعين.',
  'Join_as_a_Deliveryman_Section' => 'انضم إلى قسم التسليم',
  'The_website_page_where_people_will_register_as_deliveryman.' => 'صفحة الموقع حيث سيقوم الأشخاص بالتسجيل كـ DeliveryMan.',
  'Want_to_enable_the_Play_Store_button_for_User_App ' => 'تريد تمكين زر متجر التشغيل لتطبيق المستخدم',
  'Want_to_disable_the_Play_Store_button_for_User_App ' => 'تريد تعطيل زر متجر التشغيل لتطبيق المستخدم',
  'If_enabled _the_User_app_download_button_will_be_visible_on_the_Landing_page.' => 'إذا تم التمكين ، فسيكون زر تنزيل تطبيق المستخدم مرئيًا على الصفحة المقصودة.',
  'Want_to_enable_the_App_Store_button_for_User_App ' => 'تريد تمكين زر متجر التطبيق لتطبيق المستخدم',
  'Want_to_disable_the_App_Store_button_for_User_App ' => 'تريد تعطيل زر متجر التطبيق لتطبيق المستخدم',
  'contact_section_updated' => 'تم تحديث قسم الاتصال',
  'newsletter' => 'النشرة الإخبارية',
  'Ex_:_Sign_Up_to_Our_Newsletter' => 'على سبيل المثال: اشترك في النشرة الإخبارية لدينا',
  'Ex_:_Receive_Latest_News _Updates_and_Many_Other_News_Every_Week' => 'على سبيل المثال: تلقي آخر تحديثات الأخبار والعديد من الأخبار الأخرى كل أسبوع',
  'Footer_Article' => 'مقال تذييل',
  'Ex_:_6amMart_is_a_complete_package!__It`s_time_to_empower_your_multivendor_online_business_with__powerful_features!' => 'على سبيل المثال: 6Ammart هي حزمة كاملة! لقد حان الوقت لتمكين أعمالك المتعددة عبر الإنترنت بميزات قوية!',
  '(size: 1:1)' => '(الحجم: 1: 1)',
  'Download_Seller_App_From_Playstore' => 'تنزيل تطبيق البائع من PlayStore',
  'Download_the_User_App_from_Playstore' => 'قم بتنزيل تطبيق المستخدم من PlayStore',
  'Download_the_User_App_from_Applestore' => 'قم بتنزيل تطبيق المستخدم من Applestore',
  'The_button_will_direct_users_to_the_link_contained_within_this_box.' => 'سيقوم الزر بتوجيه المستخدمين إلى الرابط الموجود في هذا المربع.',
  'When_disabled _the_Play_Store_download_button_will_be_hidden_from_the_React_landing_page.' => 'عند تعطيل ، سيتم إخفاء زر تنزيل متجر Play من صفحة React Landing.',
  'If_enabled _the_User_app_download_button_will_be_visible_on_React_Landing_page.' => 'إذا تم تمكين ، فسيكون زر تنزيل تطبيق المستخدم مرئيًا على صفحة React Handing.',
  'If_disabled _this_button_will_be_hidden_from_the_React_landing_page.' => 'إذا تم تعطيل هذا الزر سيتم إخفاءه من صفحة React Landing.',
  'When_disabled _the_User_app_download_button_will_be_hidden_on_React_Landing_page.' => 'عند تعطيل ، سيتم إخفاء زر تنزيل تطبيق المستخدم على صفحة React Landing.',
  'If_enabled _the_Store_app_download_button_will_be_visible_on_React_Landing_page.' => 'إذا تم التمكين ، فسيكون زر تنزيل تطبيق المتجر مرئيًا على صفحة React Handing.',
  'When_disabled _the_App_Store_download_button_will_be_hidden_from_the_React_landing_page' => 'عند تعطيل ، سيتم إخفاء زر تنزيل متجر التطبيقات من صفحة React Landing',
  'select_all' => 'اختر الكل',
  'provide_dm_earning' => 'توفير كسب DM',
  'Transaction Overview' => 'نظرة عامة على المعاملات',
  'Hello  here you can manage your transactions.' => 'مرحبًا هنا ، يمكنك إدارة معاملاتك.',
  'customer_management' => 'ادارة الزبائن',
  'Hello _here_you_can_manage_your_users_by_zone.' => 'مرحبًا هنا ، يمكنك إدارة المستخدمين حسب المنطقة.',
  'All_Zones' => 'جميع المناطق',
  'Jan' => 'يناير',
  'Feb' => 'فبراير',
  'Mar' => 'مارس',
  'Apr' => 'أبريل',
  'May' => 'يمكن',
  'Jun' => 'يونيو',
  'Jul' => 'يوليو',
  'Aug' => 'أغسطس',
  'Sep' => 'سبتمبر',
  'Oct' => 'أكتوبر',
  'Nov' => 'نوفمبر',
  'Dec' => 'ديسمبر',
  'Deliveryman_Section_Content' => 'محتوى قسم التسليم',
  'Download the Deliveryman App' => 'قم بتنزيل تطبيق Deliveryman',
  'Footer_Content' => 'محتوى تذييل',
  'fixed_Data' => 'بيانات ثابتة',
  'Newsleter Section Content ' => 'محتوى قسم النشرة الإخبارية',
  'short_Description' => 'وصف قصير',
  'promotional_Banner' => 'لافتة ترويجية',
  '(size: 1:5)' => '(الحجم: 1: 5)',
  'landing_page_newsletter_content_updated' => 'تم تحديث محتوى النشرة الإخبارية للصفحة المقصودة',
  'landing_page_footer_content_updated' => 'تم تحديث محتوى تذييل الصفحة المقصودة',
  'Ex_:_Search_by_type...' => 'على سبيل المثال: البحث حسب النوع ...',
  'Vehicle_status_updated' => 'تم تحديث حالة السيارة',
  'edit_vehicle' => 'تحرير السيارة',
  'delete_vehicle' => 'حذف السيارة',
  'Previous_customer' => 'العميل السابق',
  'Next_customer' => 'العميل القادم',
  'If_you_select_Yes _the_time_schedule_will_be_deleted.' => 'إذا حددت نعم ، فسيتم حذف الجدول الزمني.',
  'ex_:_bike' => 'على سبيل المثال: الدراجة',
  'This_Year' => 'هذا العام',
  'This_Month' => 'هذا الشهر',
  'This_Week' => 'هذا الاسبوع',
  'donât_forget_to_click_the_âSave Informationâ_button_below_to_save_changes.' => 'لا تنسى النقر فوق الزر "حفظ المعلومات" أدناه لحفظ التغييرات.',
  'make_visitors_aware_of_your_businessâs_rights_&_legal_information.' => 'اجعل الزائرين على دراية بحقوق عملك والمعلومات القانونية.',
  'Set_up_âDefault_Commission_Rateâ_on_every_Order._Admin_can_also_set_store-wise_different_commission_rates_from_respective_store_settings.' => 'وضع معدل العمولة الافتراضية على كل أمر. يمكن للمسؤول أيضًا تعيين أسعار العمولة المختلفة من المتجر من إعدادات المتجر المعنية.',
  'Set_a_default_âCommission_Rateâ_for_freelance_deliverymen_(under_admin)_on_every_deliveryman. ' => 'قم بتعيين معدل العمولة الافتراضي لعمليات التوصيل المستقل (تحت المسؤول) على كل تسليم.',
  'After_a_customer_order_placement _Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store _For_example _if_you_choose_âDelivery_manâ _the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_âStoreâ.' => 'بعد أن يتمكن مسؤول وضع طلب العميل من تحديد من الذي سيؤكد الطلب الأول أو المتجر على سبيل المثال ، على سبيل المثال ، إذا اخترت "تسليم Manâ" ، فسيؤكد Developmentman على الطلب وإعادة توجيهه إلى المتجر ذي الصلة لمعالجة الطلب. إنه يعمل العكس إذا اخترت Â Storeâ.',
  'If_enabled _the_customer_will_see_the_total_product_price _including_VAT/Tax._If_itâs_disabled _the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'إذا تم تمكين العميل ، فسيشاهد العميل إجمالي سعر المنتج بما في ذلك ضريبة القيمة المضافة/الضريبة. إذا تم تعطيلها ، فسيتم إضافة ضريبة القيمة المضافة/الضريبة بشكل منفصل بالتكلفة الإجمالية للمنتج.',
  'âInclude_Tax_Amount â' => 'Â تضمين المبلغ الضريبي Â',
  'Tax_Amountâ ' => 'مبلغ الضرائب',
  'Customerâs_Food_Preference' => 'تفضيل الطعام للعميل',
  'âVeg/Non-Vegâ_feature ' => 'ميزة الخضار/غير الخضار',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded _the_Delivery_Fee_is_deducted_from_Adminâs_commission_and_added_to_Adminâs_expense.' => 'تعيين قيمة الحد الأدنى للطلب للتسليم المجاني الآلي. إذا تم تجاوز الحد الأدنى للمبلغ ، يتم خصم رسوم التسليم من لجنة المشرف وإضافتها إلى مصاريف المشرف.',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_âTurn_Offâ _maintenance_mode.' => 'سيتم تعطيل جميع تطبيقاتك وموقع العميل على الويب حتى تقوم بإيقاف تشغيل وضع الصيانة.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*إذا كان المسؤول يمكّنًا من وضع "استرداد طلب طلب المبلغ" طلب استرداد.',
  'Admin_can_enable/disable_Deliverymanâs_order_cancellation_option_in_the_respective_app.' => 'يمكن للمسؤول تمكين/تعطيل خيار إلغاء الطلب في التسليم في التطبيق المعني.',
  'If you want to disable or turn off any section please leave that section empty  donât make any changes there!' => 'إذا كنت ترغب في تعطيل أو إيقاف تشغيل أي قسم ، فيرجى ترك هذا القسم فارغًا ، فلا يقوم بإجراء أي تغييرات هناك!',
  'Letâs See The Changes!' => 'دعونا نرى التغييرات!',
  'Sun' => 'شمس',
  'Mon' => 'لي',
  'Tue' => 'الثلاثاء',
  'Wed' => 'تزوج',
  'Thu' => 'يجمع',
  'Fri' => 'الجمعة',
  'Sat' => 'قعد',
  'Send_Mail_On_âForgot_Passwordâ ' => 'أرسل بريدًا على "نسيان كلمة المرور"',
  'If_a_user_clicks_âForgot_Passwordâ_during_login _an_automated_email_will_be_sent_to_the_admin.' => 'إذا نقر المستخدم على "نسيان كلمة المرور" أثناء تسجيل الدخول ، فسيتم إرسال بريد إلكتروني آلي إلى المسؤول.',
  'If_enabled _admin_will_receive_an_email_when_a_user_click_on_âForgot_Password.â' => 'إذا كان المسؤول الممكّن سيتلقى بريدًا إلكترونيًا عندما ينقر المستخدم على "نسيان كلمة المرور."',
  'If_disabled _admin_will_not_receive_an_email_when_a_user_click_on_âForgot_Password.â' => 'إذا لم يتلق المسؤول المعاق بريدًا إلكترونيًا عندما ينقر المستخدم على "نسيان كلمة المرور."',
  'Give_your_email_a_âCatchy_Titleâ_to_help_the_reader_understand_easily.' => 'امنح بريدك الإلكتروني "عنوانًا جذابًا" لمساعدة القارئ على الفهم بسهولة.',
  'Ø®Ø¶Ø±ÙØ§Øª' => 'Ø®ø¶ø ± المنطقة',
  'ÙÙØ§Ø¨Ù' => 'ÙÙØ§Ø¨Ù',
  'ÙØÙ' => 'ÙØÙ',
  'Ø·Ø¹Ø§Ù' => 'Ø Ø Ø',
  'ÙØ·Ø¹Ø©' => 'Ø Ø · Ø © ©',
  'If you disable this  Deliveryman will deliver the order and update the status. He doesnât need to verify the order with any code.' => 'إذا قمت بتعطيل هذا التسليم ، فسيقوم بتوصيل الطلب وتحديث الحالة. لا يحتاج إلى التحقق من الطلب مع أي رمز.',
  'If_you_enable_this_feature _customers_can_choose_âHome_Deliveryâ_and_get_the_product_delivered_to_their_preferred_location.' => 'إذا قمت بتمكين هذه الميزة ، فيمكن للعملاء اختيار "توصيل المنازل" وتسليم المنتج إلى موقعهم المفضل.',
  'If_you_enable_this_feature _customers_can_place_an_order_and_request_âTakeawaysâ_or_âself-pick-upâ_from_stores.' => 'إذا قمت بتمكين هذه الميزة ، فيمكن لعملاء تقديم طلب وطلب â exeawaysâ أو Â من الانتقاء الذاتي من المتاجر.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_âCancel_Orderâ_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*لا يمكن للمستخدمين إلغاء طلب ما إذا كان المسؤول لا يحدد سببًا للإلغاء على الرغم من أنهم يرون خيار Â Orderâ. لذلك يجب أن يوفر المسؤول سببًا مناسبًا لإلغاء الطلب وحدد المستخدم ذي الصلة.',
  'mm/dd/yyyy' => 'Ø · Ø',
  'System_Configeration_Instruction' => 'تعليمات تكوين النظام',
  'Note_about_transaction_or_request' => 'ملاحظة حول المعاملة أو الطلب',
  'Monitor_storeâs_business_analytics_&_Reports' => 'مراقبة التحليلات والتقارير التجارية لمتجر',
  'Total_canceled' => 'Ø · Ø¹ø§ù إلغاء',
  'Total_ongoing' => 'إجمالي مستمر',
  'Total_delivered' => 'إجمالي تسليمه',
  '  +data[count].name +  ' => '+البيانات [العد] .NAME +',
  'Write_a_message_in_the_Notification_Body' => 'اكتب رسالة في هيئة الإخطار',
  'Donât_forget_to_click_the_âAdd_Moduleâ_button_below_to_save_the_new_business_module.' => 'لا تنسى النقر فوق الزر "إضافة الوحدة النمطية" أدناه لحفظ وحدة الأعمال الجديدة.',
  'To_create_a_new_business_module _go_to:_âModule_Setupâ_â_âAdd_Business_Module.â' => 'لإنشاء وحدة أعمال جديدة ، انتقل إلى: Â MODULE SETUPâ Â ad add',
  'Go_to_âZone_Setupââ_âBusiness_Zone_Listââ_âZone_Settingsââ_Choose_Payment_MethodâAdd_Business_Module_into_Zone_with_Parameters.' => 'انتقل إلى Â zone Zone Setupâ Â Â ‘‘ ‘reats Â Â Â stations â Â rection rection styps admonder addual adduction add addual add ad exption add exption ad extrale method exptive adminal extrals.',
  'Select_your_Module_from_the_Module_Section _Click_â_âStore_ManagementâââAdd_StoreââAdd_Store_details_&_select_Zone_to_integrate_Module+Zone+Store.' => 'حدد الوحدة النمطية الخاصة بك من قسم الوحدة النمطية ، انقر فوق "إدارة المتجر" إضافة تفاصيل المتجر وحدد المنطقة لدمج الوحدة النمطية+المنطقة+.',
  'Site' => 'موقع',
  'Key' => 'مفتاح',
  'Secret' => 'سر',
  'Filters' => 'المرشحات',
  'Clear_all_filters' => 'مسح جميع المرشحات',
  '$mod- module_name' => 'اسم الوحدة النمطية $',
  'Admin Comission' => 'المسؤول',
  'Delivery Comission' => 'توصيل كوم',
  'Proceed _If_thermal_printer_is_ready.' => 'المضي قدما إذا كانت الطابعة الحرارية جاهزة.',
  'Total canceled' => 'إجمالي إلغاء',
  'Total ongoing' => 'إجمالي مستمر',
  'Total delivered' => 'إجمالي تسليمه',
  'Display_name' => 'اسم العرض',
  'Want to update admin info  ' => 'تريد تحديث معلومات المسؤول',
  'Cookies Text' => 'نص ملفات تعريف الارتباط',
  'Ex_:_Cookies_Text' => 'على سبيل المثال: نص ملفات تعريف الارتباط',
  'ok' => 'نعم',
  'Delete Bank Info  ' => 'حذف معلومات البنك',
  'withdraw_list' => 'سحب قائمة',
  'Select All' => 'اختر الكل',
  'provide dm earning' => 'توفير كسب DM',
  'withdraw list' => 'سحب قائمة',
  'customer management' => 'ادارة الزبائن',
  'custom role' => 'دور مخصص',
  'Use_this_âHand_Toolâ_to_find_your_target_zone.' => 'استخدم أداة اليد هذه للعثور على منطقة المستهدف.',
  'Use_this_âShape_Toolâ_to_point_out_the_areas_and_connect_the_dots._Minimum_3_points/dots_are_required.' => 'استخدم أداة الشكل هذه للإشارة إلى المناطق وتوصيل النقاط. الحد الأدنى 3 نقاط/نقاط مطلوبة.',
  'Want_to_enable_âDigital_Paymentâ ' => 'تريد تمكين "الدفع الرقمي"',
  'If_yes _Customers_can_choose_the_âDigital_Paymentâ_option_during_checkout.' => 'إذا كان بإمكان العملاء بنعم اختيار خيار "الدفع الرقمي" أثناء الخروج.',
  'Want_to_enable_âCash_On_Deliveryâ ' => 'تريد تمكين Â النقد عند التسليم "',
  'If_yes _Customers_can_choose_the_âCash_On_Deliveryâ_option_during_checkout.' => 'إذا كان بإمكان العملاء بنعم اختيار خيار Â Cash at Delivery أثناء الخروج.',
  'The_Business_Zone_will_NOT_work_if_you_donât_select_your_business_module_&_payment_method.' => 'لن تعمل منطقة الأعمال إذا لم تختار طريقة عملك وطريقة الدفع.',
  'Want_to_disable_âDigital_Paymentâ ' => 'تريد تعطيل "الدفع الرقمي"',
  'Want_to_disable_âCash_On_Deliveryâ ' => 'تريد تعطيل cash على التسليم "',
  'NEXT_IMPORTANT_STEP:_You_need_to_select_âPayment_Methodâ_and_add_âBusiness_Modulesâ_with_other_details_from_the_Zone_Settings._If_you_donât_finish_the_setup _the_Zone_you_created_wonât_function_properly.' => 'الخطوة المهمة التالية: تحتاج إلى تحديد "طريقة الدفع" وإضافة وحدات عمل مع تفاصيل أخرى من إعدادات المنطقة. إذا لم تنهي الإعداد ، فلن تعمل المنطقة التي أنشأتها بشكل صحيح.',
  'By switching the status to âONâ   this zone and under all the functionality of this zone will be turned on' => 'عن طريق تغيير الحالة إلى Â على هذه المنطقة وتحت كل وظائف هذه المنطقة سيتم تشغيلها',
  'Join us' => 'انضم إلينا',
  'Turotial' => 'التورني',
  'Tour' => 'رحلة',
  'خضروات' => 'خضروات',
  'طعام' => 'طعام',
  'قطعة' => 'قطعة',
  'Monitor_storeâs_business_analytics_&_Reports' => 'مراقبة التحليلات والتقارير التجارية لمتجر',
  'react_site_setup' => 'رد فعل الموقع',
  'React Site Setup' => 'رد فعل الموقع',
  'React license code' => 'رد فعل رمز الترخيص',
  'React Domain' => 'رد فعل المجال',
  'Ex:_Copyright_2023_6amMart._All_right_reserved' => 'على سبيل المثال: حقوق الطبع والنشر 2023 6Ammart. جميع الحقوق محفوظة',
  'Send_Mail_On_Delivery_Man_approve ' => 'أرسل بريدًا على رجل التسليم الموافقة',
  'If_Admin_accepts_a_Deliverymanâs_self-registration _the_Deliveryman_will_get_an_automatic_approval_mail.' => 'إذا قبل المسؤول عن التسجيل الذاتي لتوصيل رجل التسليم ، فسوف يحصل التسليم على بريد موافقة تلقائي.',
  'If_enabled _Deliverymen_will_get_a_confirmation_email_when_registration_is_approved_by_the_Admin.' => 'إذا حصل رجال التسليم الممكّن على بريد إلكتروني للتأكيد عند الموافقة على التسجيل من قبل المسؤول.',
  'If_disabled _Deliverymen_will_not_get_a_registration_approval_email.' => 'إذا لم يحصل رجال التسليم المعاقين على بريد إلكتروني للموافقة على التسجيل.',
  'Write_the_sub_title_within_100_characters' => 'اكتب العنوان الفرعي ضمن 100 حرف',
  'Write_the_title_within_40_characters' => 'اكتب العنوان ضمن 40 حرفًا',
  'Write_the_sub_title_within_80_characters' => 'اكتب العنوان الفرعي ضمن 80 حرفًا',
  'Write_the_title_within_180_characters' => 'اكتب العنوان ضمن 180 حرفًا',
  'Write_the_title_within_80_characters' => 'اكتب العنوان ضمن 80 حرفًا',
  'Write_the_title_within_240_characters' => 'اكتب العنوان ضمن 240 حرفًا',
  'Are_you_sure_you_want_to_remove_this_image' => 'هل أنت متأكد أنك تريد إزالة هذه الصورة',
  'Are_you_sure_you_want_to_remove_this_image.' => 'هل أنت متأكد من أنك تريد إزالة هذه الصورة.',
  'Write_the_title_within_250_characters' => 'اكتب العنوان ضمن 250 حرفًا',
  'Write_the_title_within_120_characters' => 'اكتب العنوان ضمن 120 حرفًا',
  'If You Want to Change Text Color To Primary Color' => 'إذا كنت تريد تغيير لون النص إلى اللون الأساسي',
  'Replace the text with ($ text $) format' => 'استبدال النص بتنسيق ($ text $)',
  'Write_the_title_within_60_characters' => 'اكتب العنوان ضمن 60 حرفًا',
  'Write_the_title_within_30_characters' => 'اكتب العنوان ضمن 30 حرفًا',
  'Write_the_title_within_65_characters' => 'اكتب العنوان ضمن 65 حرفًا',
  'If_you_want_to_upload_one_banner_then_you_have_to_upload_it_in_6:1_ratio_otherwise_the_ratio_will_be_same_as_before.' => 'إذا كنت ترغب في تحميل لافتة واحدة ، فعليك تحميلها في نسبة 6: 1 وإلا فإن النسبة ستكون كما كانت من قبل.',
  'Write_the_title_within_35_characters' => 'اكتب العنوان ضمن 35 حرفًا',
  'Write_the_title_within_140_characters' => 'اكتب العنوان ضمن 140 حرفًا',
  'Write_the_title_within_100_characters' => 'اكتب العنوان ضمن 100 حرف',
  'Write_the_title_within_15_characters' => 'اكتب العنوان ضمن 15 حرفًا',
  'don’t_forget_to_click_the_‘Save Information’_button_below_to_save_changes.' => 'Don’t forget to click the ‘Save Information’ button below to save changes.',
  'make_visitors_aware_of_your_business‘s_rights_&_legal_information.' => 'Make visitors aware of your business‘s rights & legal information.',
  'Set_up_‘Default_Commission_Rate’_on_every_Order._Admin_can_also_set_store-wise_different_commission_rates_from_respective_store_settings.' => 'Set up ‘Default Commission Rate’ on every Order. Admin can also set store-wise different commission rates from respective store settings.',
  'Set_a_default_‘Commission_Rate’_for_freelance_deliverymen_(under_admin)_on_every_deliveryman. ' => 'Set a default ‘Commission Rate’ for freelance deliverymen (under admin) on every deliveryman. ',
  'After_a_customer_order_placement _Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store _For_example _if_you_choose_‘Delivery_man’ _the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_‘Store’.' => 'After a customer order placement  Admin can define who will confirm the order first- Deliveryman or Store  For example  if you choose ‘Delivery man’  the deliveryman nearby will confirm the order and forward it to the related store to process the order. It works vice-versa if you choose ‘Store’.',
  'If_enabled _the_customer_will_see_the_total_product_price _including_VAT/Tax._If_it’s_disabled _the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'If enabled  the customer will see the total product price  including VAT/Tax. If it’s disabled  the VAT/Tax will be added separately with the total cost of the product.',
  '‘Include_Tax_Amount ’' => '‘Include Tax Amount ’',
  'Tax_Amount’ ' => 'Tax Amount’ ',
  'Customer’s_Food_Preference' => 'Customer’s Food Preference',
  '‘Veg/Non-Veg’_feature ' => '‘Veg/Non-Veg’ feature ',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded _the_Delivery_Fee_is_deducted_from_Admin’s_commission_and_added_to_Admin’s_expense.' => 'Set a minimum order value for automated free delivery. If the minimum amount is exceeded  the Delivery Fee is deducted from Admin’s commission and added to Admin’s expense.',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_‘Turn_Off’ _maintenance_mode.' => 'All your apps and customer website will be disabled until you ‘Turn Off’  maintenance mode.',
  'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned' => 'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned',
  'If you disable this  Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.' => 'If you disable this  Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.',
  'If_you_enable_this_feature _customers_can_choose_‘Home_Delivery’_and_get_the_product_delivered_to_their_preferred_location.' => 'If you enable this feature  customers can choose ‘Home Delivery’ and get the product delivered to their preferred location.',
  'If_you_enable_this_feature _customers_can_place_an_order_and_request_‘Takeaways’_or_‘self-pick-up’_from_stores.' => 'If you enable this feature  customers can place an order and request ‘Takeaways’ or ‘self-pick-up’ from stores.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_‘Cancel_Order‘_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '* لا يمكن للمستخدمين إلغاء أي طلب إذا لم يحدد المسؤول سببًا للإلغاء على الرغم من أنهم يرون خيار "إلغاء الطلب". ',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’ _customers_can_request_a_refund.' => '*If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'Admin_can_enable/disable_Store’s_order_cancellation_option.' => 'Admin can enable/disable Store’s order cancellation option.',
  'Admin_can_enable/disable_Deliveryman’s_order_cancellation_option_in_the_respective_app.' => 'Admin can enable/disable Deliveryman’s order cancellation option in the respective app.',
  'With_this_feature _customers_can_have_virtual_wallets_in_their_account_via_Customer_App_&_Website._They_can_also_earn_(via_referral _refund _or_loyalty_points)_and_buy_with_the_wallet’s_amount.' => 'With this feature  customers can have virtual wallets in their account via Customer App & Website. They can also earn (via referral  refund  or loyalty points) and buy with the wallet’s amount.',
  'If_it’s_enabled _Customers_will_automatically_receive_the_refunded_amount_in_their_wallets._But_if_it’s_disabled _the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If it’s enabled  Customers will automatically receive the refunded amount in their wallets. But if it’s disabled  the Admin will handle the Refund Request in his convenient transaction channel.',
  'If_you_disable_this _Customers_don’t_need_to_verify_their_account_via_OTP.' => 'If you disable this  Customers don’t need to verify their account via OTP.',
  'New_Customer_Growth' => 'New Customer Growth',
  'Don`t_Logout' => 'Don`t Logout',
  'Send_Mail_On_‘Forgot_Password’ ' => 'Send Mail On ‘Forgot Password’ ',
  'If_a_user_clicks_‘Forgot_Password’_during_login _an_automated_email_will_be_sent_to_the_admin.' => 'If a user clicks ‘Forgot Password’ during login  an automated email will be sent to the admin.',
  'If_enabled _admin_will_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If enabled  admin will receive an email when a user click on ‘Forgot Password.’',
  'If_disabled _admin_will_not_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If disabled  admin will not receive an email when a user click on ‘Forgot Password.’',
  'Please_contact_us_for_any_queries _we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Give_your_email_a_‘Catchy_Title’_to_help_the_reader_understand_easily.' => 'Give your email a ‘Catchy Title’ to help the reader understand easily.',
  'Receive_Mail_on_‘Campaign_Join_Request’ ' => 'Receive Mail on ‘Campaign Join Request’ ',
  'Receive_Mail_on_‘Refund_Request’ ' => 'Receive Mail on ‘Refund Request’ ',
  'Receive_Mail_On_‘New_Store_Registration’ ' => 'Receive Mail On ‘New Store Registration’ ',
  'If_Admin_accepts_a_Deliveryman’s_self-registration _the_Deliveryman_will_get_an_automatic_approval_mail.' => 'If Admin accepts a Deliveryman’s self-registration  the Deliveryman will get an automatic approval mail.',
  'Send_Mail_on_‘Cash_Collection’ ' => 'Send Mail on ‘Cash Collection’ ',
  'Current_value' => 'Current value',
  'system_addons' => 'إضافات النظام',
  'addon_menus' => 'addon menus',
  'payment_setup' => 'إعداد الدفع',
  'sms_setup' => 'sms setup',
  'Do you want to logout?' => 'Do you want to logout ',
  'You have new order, Check Please.' => 'You have new order  Check Please.',
  'Ok, let me check' => 'Ok  let me check',
  'Are you sure?' => 'Are you sure ',
  'Hello, Here You Can Manage Your' => 'Hello  Here You Can Manage Your',
  'partially_paid' => 'partially paid',
  'additional_charge' => 'additional charge',
  'partial payment' => 'partial payment',
  'ssl commerz' => 'ssl commerz',
  'Monitor_store’s_business_analytics_&_Reports' => 'Monitor store’s business analytics & Reports',
  'filter_criteria' => 'filter criteria',
  'order_type' => 'order type',
  'erwrw' => 'erwrw',
  100093 => '100093',
  'search_bar_content' => 'search bar content',
  'Today\'s Statistics' => 'Today s Statistics',
  'zones' => 'zones',
  'Who_Will_Confirm_Order?' => 'Who Will Confirm Order ',
  'After_a_customer_order_placement,_Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store?_For_example,_if_you_choose_‘Delivery_man’,_the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_‘Store’.' => 'After a customer order placement  Admin can define who will confirm the order first- Deliveryman or Store  For example  if you choose ‘Delivery man’  the deliveryman nearby will confirm the order and forward it to the related store to process the order. It works vice-versa if you choose ‘Store’.',
  'If_enabled,_the_customer_will_see_the_total_product_price,_including_VAT/Tax._If_it’s_disabled,_the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'If enabled  the customer will see the total product price  including VAT/Tax. If it’s disabled  the VAT/Tax will be added separately with the total cost of the product.',
  '‘Include_Tax_Amount?’' => '‘Include Tax Amount ’',
  'Tax_Amount’?' => 'Tax Amount’ ',
  'If_you_enable_it,_customers_will_see_the_product_Price_including_Tax,_during_checkout. ' => 'If you enable it  customers will see the product Price including Tax  during checkout. ',
  'If_you_disable_it,_customers_will_see_the_product_or_service_price_without_Tax,_during_checkout.' => 'If you disable it  customers will see the product or service price without Tax  during checkout.',
  'If_this_feature_is_active,_customers_can_filter_food_according_to_their_preference_from_the_Customer_App_or_Website.' => 'If this feature is active  customers can filter food according to their preference from the Customer App or Website.',
  '‘Veg/Non-Veg’_feature?' => '‘Veg/Non-Veg’ feature ',
  'the_Veg/Non-Veg_Feature?' => 'the Veg/Non-Veg Feature ',
  'If_you_enable_this,_customers_can_filter_food_items_by_choosing_food_from_the_Veg/Non-Veg_feature.' => 'If you enable this  customers can filter food items by choosing food from the Veg/Non-Veg feature.',
  'If_you_disable_this,_the_Veg/Non-Veg_feature_will_be_hidden_in_the_Customer_App_&_Website.' => 'If you disable this  the Veg/Non-Veg feature will be hidden in the Customer App & Website.',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded,_the_Delivery_Fee_is_deducted_from_Admin’s_commission_and_added_to_Admin’s_expense.' => 'Set a minimum order value for automated free delivery. If the minimum amount is exceeded  the Delivery Fee is deducted from Admin’s commission and added to Admin’s expense.',
  'Want_to_enable_Free_Delivery_on_Minimum_Orders?' => 'Want to enable Free Delivery on Minimum Orders ',
  'Want_to_disable_Free_Delivery_on_Minimum_Order?' => 'Want to disable Free Delivery on Minimum Order ',
  'If_you_enable_this,_customers_can_get_FREE_Delivery_by_fulfilling_the_minimum_order_requirement.' => 'If you enable this  customers can get FREE Delivery by fulfilling the minimum order requirement.',
  'If_you_disable_this,_the_FREE_Delivery_option_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the FREE Delivery option will be hidden from the Customer App or Website.',
  'Order_Notification_for_Admin?' => 'Order Notification for Admin ',
  'If_you_enable_this,_the_Admin_will_receive_a_Notification_for_every_order_placed.' => 'If you enable this  the Admin will receive a Notification for every order placed.',
  'If_you_disable_this,_the_Admin_will_NOT_receive_a_Notification_for_every_order_placed.' => 'If you disable this  the Admin will NOT receive a Notification for every order placed.',
  'With_this_feature,_customers_can_choose_additional_charge_with_wallet_and_another_payment_method.' => 'With this feature  customers can choose additional charge with wallet and another payment method.',
  'Want_to_enable_additional_charge?' => 'Want to enable additional charge ',
  'Want_to_disable_additional_charge?' => 'Want to disable additional charge ',
  'If_you_enable_this,_additional_charge_will_be_added_with_order_amount,_it_will_be_added_in_admin_wallet' => 'If you enable this  additional charge will be added with order amount  it will be added in admin wallet',
  'If_you_disable_this,_additional_charge_will_not_be_added_with_order_amount.' => 'If you disable this  additional charge will not be added with order amount.',
  'additional_charge_name' => 'additional charge name',
  'Set_a_value_that_will_be_added_to_Admin’s_commission.' => 'Set a value that will be added to Admin’s commission.',
  'charge_amount' => 'charge amount',
  'partial_payment' => 'partial payment',
  'With_this_feature,_customers_can_choose_partial_payment_with_wallet_and_another_payment_method.' => 'With this feature  customers can choose partial payment with wallet and another payment method.',
  'partial_payment_?' => 'partial payment  ',
  'If_you_enable_this,_customers_can_choose_partial_payment_during_checkout.' => 'If you enable this  customers can choose partial payment during checkout.',
  'If_you_disable_this,_the_partial_payment_feature_will_be_hidden.' => 'If you disable this  the partial payment feature will be hidden.',
  'can_combine_payment' => 'can combine payment',
  'cod' => 'cod',
  'Here,_customers_can_store_their_refunded_order_amount,_referral_earnings,_and_loyalty_points.' => 'Here  customers can store their refunded order amount  referral earnings  and loyalty points.',
  'With_this_feature,_customers_can_have_virtual_wallets_in_their_account_via_Customer_App_&_Website._They_can_also_earn_(via_referral,_refund,_or_loyalty_points)_and_buy_with_the_wallet’s_amount.' => 'With this feature  customers can have virtual wallets in their account via Customer App & Website. They can also earn (via referral  refund  or loyalty points) and buy with the wallet’s amount.',
  'the_Wallet_feature?' => 'the Wallet feature ',
  'If_you_enable_this,_Customers_can_see_&_use_the_Wallet_option_from_their_profile_in_the_Customer_App_&_Website.' => 'If you enable this  Customers can see & use the Wallet option from their profile in the Customer App & Website.',
  'If_you_disable_this,_the_Wallet_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the Wallet feature will be hidden from the Customer App & Website.',
  'If_it’s_enabled,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets._But_if_it’s_disabled,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If it’s enabled  Customers will automatically receive the refunded amount in their wallets. But if it’s disabled  the Admin will handle the Refund Request in his convenient transaction channel.',
  'Refund_to_Wallet_feature?' => 'Refund to Wallet feature ',
  'If_you_enable_this,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets.' => 'If you enable this  Customers will automatically receive the refunded amount in their wallets.',
  'If_you_disable_this,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If you disable this  the Admin will handle the Refund Request in his convenient transaction channel.',
  'customer_can_add_fund_to_wallet' => 'customer can add fund to wallet',
  'With_this_feature,_customers_can_add_fund_to_wallet_if_the_payment_module_is_available.' => 'With this feature  customers can add fund to wallet if the payment module is available.',
  'add_fund_status' => 'add fund status',
  'add_fund_to_Wallet_feature?' => 'add fund to Wallet feature ',
  'If_you_enable_this,_Customers_can_add_fund_to_wallet_using_payment_module' => 'If you enable this  Customers can add fund to wallet using payment module',
  'If_you_disable_this,_add_fund_to_wallet_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  add fund to wallet will be hidden from the Customer App & Website.',
  'Existing_Customers_can_share_a_referral_code_with_others_to_earn_a_referral_bonus._For_this,_the_new_user_MUST_sign_up_using_the_referral_code_and_make_their_first_purchase.' => 'Existing Customers can share a referral code with others to earn a referral bonus. For this  the new user MUST sign up using the referral code and make their first purchase.',
  'Referral_Earning?' => 'Referral Earning ',
  'If_you_enable_this,_Customers_can_earn_points_by_referring_others_to_sign_up_&_purchase_from_your_business.' => 'If you enable this  Customers can earn points by referring others to sign up & purchase from your business.',
  'If_you_disable_this,_the_referral-earning_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the referral-earning feature will be hidden from the Customer App & Website.',
  'If_you_activate_this_feature,_customers_need_to_verify_their_account_information_via_OTP_during_the_signup_process.' => 'If you activate this feature  customers need to verify their account information via OTP during the signup process.',
  'Customer_Verification?' => 'Customer Verification ',
  'If_you_enable_this,_Customers_have_to_verify_their_account_via_OTP.' => 'If you enable this  Customers have to verify their account via OTP.',
  'If_you_disable_this,_Customers_don’t_need_to_verify_their_account_via_OTP.' => 'If you disable this  Customers don’t need to verify their account via OTP.',
  'default_title_is_required' => 'default title is required',
  'default_description_is_required' => 'default description is required',
  'Want to delete this item ?' => 'Want to delete this item  ',
  'meta_data' => 'meta data',
  'Want to delete this banner ?' => 'Want to delete this banner  ',
  'Want to delete this coupon ?' => 'Want to delete this coupon  ',
  'Basic_Campaign_List' => 'Basic Campaign List',
  'Message_Analytics' => 'Message Analytics',
  'Total_Campaign' => 'Total Campaign',
  'Currently_Running' => 'Currently Running',
  'Cmapaign_Name' => 'Cmapaign Name',
  'Start_Date' => 'Start Date',
  'End_Date' => 'End Date',
  'Daily_Start_Time' => 'Daily Start Time',
  'Daily_End_Time' => 'Daily End Time',
  'Total_Store_Joined' => 'Total Store Joined',
  'Search_Criteria' => 'Search Criteria',
  'Search_Bar_Conten' => 'Search Bar Conten',
  'N/A' => 'N/A',
  'Campaign uploaded successfully' => 'Campaign uploaded successfully',
  'Item_Campaign_List' => 'Item Campaign List',
  'Filter_Criteria' => 'Filter Criteria',
  'Module' => 'Module',
  'Search_Bar_Content' => 'Search Bar Content',
  'Item_Name' => 'Item Name',
  'Categrory_Name' => 'Categrory Name',
  'Change status to pending ?' => 'Change status to pending  ',
  'Change status to confirmed ?' => 'Change status to confirmed  ',
  'Change status to processing ?' => 'Change status to processing  ',
  'Change status to handover ?' => 'Change status to handover  ',
  'Change status to out for delivery ?' => 'Change status to out for delivery  ',
  'Change status to delivered (payment status will be paid if not)?' => 'Change status to delivered (payment status will be paid if not) ',
  'order_proof' => 'order proof',
  'add_order_proof' => 'add order proof',
  'Are you sure ?' => 'Are you sure  ',
  'Change status to canceled ?' => 'Change status to canceled  ',
  'Want to delete this unit ?' => 'Want to delete this unit  ',
  'maximum_cart_quantity' => 'maximum cart quantity',
  'default_name_is_required' => 'default name is required',
  'Campaign updated successfully' => 'Campaign updated successfully',
  'please_select_a_store_first' => 'please select a store first',
  'Proceed, If thermal printer is ready.' => 'Proceed  If thermal printer is ready.',
  'Sorry, product out of stock' => 'Sorry  product out of stock',
  'Sorry, you can not add multiple stores data in same cart' => 'Sorry  you can not add multiple stores data in same cart',
  'Sorry, the minimum value was reached' => 'Sorry  the minimum value was reached',
  'Sub_Categrory_Name' => 'Sub Categrory Name',
  'Item_Unit' => 'Item Unit',
  'Available_Variations' => 'Available Variations',
  'Discount_Type' => 'Discount Type',
  'Available_Stock' => 'Available Stock',
  'Store_Name' => 'Store Name',
  'If the order is successfully refunded, the full order amount goes to this section without the delivery fee and delivery tips.' => 'If the order is successfully refunded  the full order amount goes to this section without the delivery fee and delivery tips.',
  'If self-delivery is off, deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.' => 'If self-delivery is off  deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.',
  'Deducting the admin commission on the delivery fee, the delivery fee & tips amount goes to
                                        earning section.' => 'Deducting the admin commission on the delivery fee  the delivery fee & tips amount goes to
                                        earning section.',
  'order_transactions_report' => 'order transactions report',
  'Transaction_Analytics' => 'Transaction Analytics',
  'Completed_Transactions' => 'Completed Transactions',
  'Refunded_Transactions' => 'Refunded Transactions',
  'Earning_Analytics' => 'Earning Analytics',
  'Admin_Earnings' => 'Admin Earnings',
  'Store_Earnings' => 'Store Earnings',
  'Delivery_Man_Earnings' => 'Delivery Man Earnings',
  'item_name' => 'item name',
  'total_order_count' => 'total order count',
  'unit_price' => 'unit price',
  'total_ratings_given' => 'total ratings given',
  'all_time' => 'all time',
  'store_summary_reports' => 'store summary reports',
  'Analytics' => 'Analytics',
  'new_registered_store' => 'new registered store',
  'completed_orders' => 'completed orders',
  'incomplete_orders' => 'incomplete orders',
  'Payment_Statistics' => 'Payment Statistics',
  'cash_payments' => 'cash payments',
  'digital_payments' => 'digital payments',
  'wallet_payments' => 'wallet payments',
  'Total_Refund_requests' => 'Total Refund requests',
  'Pending_Refund_requests' => 'Pending Refund requests',
  'store_sales_reports' => 'store sales reports',
  'total_tax' => 'total tax',
  'total_commission' => 'total commission',
  'total_store_earning' => 'total store earning',
  'Product_name' => 'Product name',
  'QTY_Sold' => 'QTY Sold',
  'Gross_Sale' => 'Gross Sale',
  'Discount_Given' => 'Discount Given',
  'limited_stock_report' => 'limited stock report',
  'current_stock' => 'current stock',
  'module_name' => 'module name',
  'product_image' => 'product image',
  'store_withdraw_transactions' => 'store withdraw transactions',
  'request_status' => 'request status',
  'requested_amount' => 'requested amount',
  'bank_account_no.' => 'bank account no.',
  'transaction_time' => 'transaction time',
  'collected_amount' => 'collected amount',
  'collected_from' => 'collected from',
  'user_type' => 'user type',
  'references' => 'references',
  'collect_cash_transactions' => 'collect cash transactions',
  'delivery_man_payments' => 'delivery man payments',
  'provided_st' => 'provided st',
  'delivery_man_name' => 'delivery man name',
  'Hello,_here_you_can_manage_your_users_by_zone.' => 'Hello  here you can manage your users by zone.',
  'bonus' => 'bonus',
  'select_payment_mode' => 'select payment mode',
  'EX of SMS provider`s template : your OTP is XXXX here, please check.' => 'EX of SMS provider`s template : your OTP is XXXX here  please check.',
  'bonuses' => 'bonuses',
  'wallet_bonus_setup' => 'wallet bonus setup',
  'bonus_type' => 'bonus type',
  'percentage' => 'percentage',
  'bonus_amount' => 'bonus amount',
  'minimum_add_amount' => 'minimum add amount',
  'maximum_bonus' => 'maximum bonus',
  'bonus_list' => 'bonus list',
  'Ex_:_bonus_title' => 'Ex : bonus title',
  'bonus_title' => 'bonus title',
  'bonus_info' => 'bonus info',
  'started_on' => 'started on',
  'expires_on' => 'expires on',
  'Want to delete this bonus ?' => 'Want to delete this bonus  ',
  'customer_list' => 'customer list',
  'Customer_Analytics' => 'Customer Analytics',
  'Total_Customer' => 'Total Customer',
  'Active_Customer' => 'Active Customer',
  'Inactive_Customer' => 'Inactive Customer',
  'saved_address' => 'saved address',
  'total_wallet_amount' => 'total wallet amount',
  'total_loyality_points' => 'total loyality points',
  'Want to delete this addon ?' => 'Want to delete this addon  ',
  'Addon_List' => 'Addon List',
  'Addon_Name' => 'Addon Name',
  'Store_name' => 'Store name',
  'Food_Campaign_List' => 'Food Campaign List',
  'Categories' => 'Categories',
  'Category_Name' => 'Category Name',
  'Sub_Category_Name' => 'Sub Category Name',
  'Food_Type' => 'Food Type',
  'Available_Addons' => 'Available Addons',
  'Available_From' => 'Available From',
  'Available_Till' => 'Available Till',
  'Tags' => 'Tags',
  'Non_Veg' => 'Non Veg',
  'All' => 'All',
  'Use_this_‘Hand_Tool’_to_find_your_target_zone.' => 'Use this ‘Hand Tool’ to find your target zone.',
  'Use_this_‘Shape_Tool’_to_point_out_the_areas_and_connect_the_dots._Minimum_3_points/dots_are_required.' => 'Use this ‘Shape Tool’ to point out the areas and connect the dots. Minimum 3 points/dots are required.',
  'Want_to_activate_this_Zone?' => 'Want to activate this Zone ',
  'Want_to_deactivate_this_Zone?' => 'Want to deactivate this Zone ',
  'If_you_activate_this_zone,_Customers_can_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you activate this zone  Customers can see all stores & products available under this Zone from the Customer App & Website.',
  'If_you_deactivate_this_zone,_Customers_Will_NOT_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you deactivate this zone  Customers Will NOT see all stores & products available under this Zone from the Customer App & Website.',
  'Want_to_disable_‘Digital_Payment’?' => 'Want to disable ‘Digital Payment’ ',
  'If_yes,_the_digital_payment_option_will_be_hidden_during_checkout.' => 'If yes  the digital payment option will be hidden during checkout.',
  'Want_to_disable_‘Cash_On_Delivery’?' => 'Want to disable ‘Cash On Delivery’ ',
  'If_yes,_the_Cash_on_Delivery_option_will_be_hidden_during_checkout.' => 'If yes  the Cash on Delivery option will be hidden during checkout.',
  'The_Business_Zone_will_NOT_work_if_you_don’t_select_your_business_module_&_payment_method.' => 'The Business Zone will NOT work if you don’t select your business module & payment method.',
  'Want_to_Delete_this_Zone?' => 'Want to Delete this Zone ',
  'If_yes,_all_its_modules,_stores,_and_products_will_be_DELETED_FOREVER.' => 'If yes  all its modules  stores  and products will be DELETED FOREVER.',
  'Want_to_enable_‘Digital_Payment’?' => 'Want to enable ‘Digital Payment’ ',
  'If_yes,_Customers_can_choose_the_‘Digital_Payment’_option_during_checkout.' => 'If yes  Customers can choose the ‘Digital Payment’ option during checkout.',
  'Want_to_enable_‘Cash_On_Delivery’?' => 'Want to enable ‘Cash On Delivery’ ',
  'If_yes,_Customers_can_choose_the_‘Cash_On_Delivery’_option_during_checkout.' => 'If yes  Customers can choose the ‘Cash On Delivery’ option during checkout.',
  'NEXT_IMPORTANT_STEP:_You_need_to_select_‘Payment_Method’_and_add_‘Business_Modules’_with_other_details_from_the_Zone_Settings._If_you_don’t_finish_the_setup,_the_Zone_you_created_won’t_function_properly.' => 'NEXT IMPORTANT STEP: You need to select ‘Payment Method’ and add ‘Business Modules’ with other details from the Zone Settings. If you don’t finish the setup  the Zone you created won’t function properly.',
  'Don\'t show this anymore' => 'Don t show this anymore',
  'By switching the status to “ON”,  this zone and under all the functionality of this zone will be turned on' => 'By switching the status to “ON”   this zone and under all the functionality of this zone will be turned on',
  'customer_order_list' => 'customer order list',
  'customer_id' => 'customer id',
  11 => '11',
  'Ellen Reyna' => 'Ellen Reyna',
  '+8801763388119' => '+8801763388119',
  '<EMAIL>' => '<EMAIL>',
  8 => '8',
  'subscriber_list' => 'subscriber list',
  'subscribed_at' => 'subscribed at',
  'Wallet_transaction_history' => 'Wallet transaction history',
  'transaction_date' => 'transaction date',
  'Ashek Elahe' => 'Ashek Elahe',
  'Want to remove this deliveryman ?' => 'Want to remove this deliveryman  ',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter_and_symbol,_and_at_least_8_or_more_characters' => 'Must contain at least one number and one uppercase and lowercase letter and symbol  and at least 8 or more characters',
  'inactive_delivery_man' => 'inactive delivery man',
  'delivery_man_type' => 'delivery man type',
  'total_completed' => 'total completed',
  'total_running_orders' => 'total running orders',
  'identity_type' => 'identity type',
  'identinty_number' => 'identinty number',
  'delivery_man_review_list' => 'delivery man review list',
  'review_list' => 'review list',
  'delivery_man_info' => 'delivery man info',
  'total_rating' => 'total rating',
  'average_review' => 'average review',
  'delivery_man_earning_list' => 'delivery man earning list',
  'delivery_fee_earned' => 'delivery fee earned',
  'tips' => 'tips',
  'Send_Mail_On_‘Forgot_Password’?' => 'Send Mail On ‘Forgot Password’ ',
  'If_a_user_clicks_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_to_the_admin.' => 'If a user clicks ‘Forgot Password’ during login  an automated email will be sent to the admin.',
  'Want_to_enable_Forget_Password_mail?' => 'Want to enable Forget Password mail ',
  'Want_to_disable_Forget_Password_mail?' => 'Want to disable Forget Password mail ',
  'If_enabled,_admin_will_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If enabled  admin will receive an email when a user click on ‘Forgot Password.’',
  'If_disabled,_admin_will_not_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If disabled  admin will not receive an email when a user click on ‘Forgot Password.’',
  'Please_contact_us_for_any_queries;_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Write_text_on_the_footer_section_of_the_email,_and_choose_important_page_links_and_social_media_links.' => 'Write text on the footer section of the email  and choose important page links and social media links.',
  'Once_you\'ve_set_up_all_the_elements_of_your_email_template,_save_and_publish_it_for_use.' => 'Once you ve set up all the elements of your email template  save and publish it for use.',
  'Receive_Mail_On_‘New_Customer_Registration’?' => 'Receive Mail On ‘New Customer Registration’ ',
  'If_a_user_registers_or_sign_up_from_the_Customer_App_or_Website,_they_will_receive_an_automated_confirmation.' => 'If a user registers or sign up from the Customer App or Website  they will receive an automated confirmation.',
  'Want_to_enable_User_Registration_mail?' => 'Want to enable User Registration mail ',
  'Want_to_disable_User_Registration_mail?' => 'Want to disable User Registration mail ',
  'If_enabled,_customers_will_receive_a_confirmation_email_that_their_registration_was_successful.' => 'If enabled  customers will receive a confirmation email that their registration was successful.',
  'If_disabled,_customers_will_receive_a_registration_confirmation_email.' => 'If disabled  customers will receive a registration confirmation email.',
  'Please_contact_us_for_any_queries,_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'If_a_Customer_clicks_on_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_with_a_Reset_Password_Link.' => 'If a Customer clicks on ‘Forgot Password’ during login  an automated email will be sent with a Reset Password Link.',
  'If_enabled,_the_Customer_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'If enabled  the Customer will receive an automated email with a Reset Password link.',
  'If_disabled,_the_Deliveryman_will_not_receive_any_for_password_reset.' => 'If disabled  the Deliveryman will not receive any for password reset.',
  'Hi_Sabrina,' => 'Hi Sabrina ',
  'Click Plus icon -> select App IDs -> click on Continue' => 'Click Plus icon -  select App IDs -  click on Continue',
  'Again click Plus icon -> select Service IDs -> click on Continue' => 'Again click Plus icon -  select Service IDs -  click on Continue',
  'active_employee' => 'active employee',
  'inactive_employee' => 'inactive employee',
  'joining_date' => 'joining date',
  'cutlery' => 'cutlery',
  'partially_paid_amount' => 'partially paid amount',
  'Change status to cooking ?' => 'Change status to cooking  ',
  'office' => 'office',
  'unavailable_item_note' => 'unavailable item note',
  'When_disabled,_item_management_feature_will_be_hidden_from_store_panel_&_store_app' => 'When disabled  item management feature will be hidden from store panel & store app',
  'When_enabled,_store_owners_can_see_customer_feedback_in_the_store_panel_&_store_app.' => 'When enabled  store owners can see customer feedback in the store panel & store app.',
  'When_enabled,_store_owner_can_take_scheduled_orders_from_customers.' => 'When enabled  store owner can take scheduled orders from customers.',
  'When_this_option_is_enabled,_stores_must_deliver_orders_using_their_own_deliverymen._Plus,_stores_will_get_the_option_to_add_their_own_deliverymen_from_the_store_panel.' => 'When this option is enabled  stores must deliver orders using their own deliverymen. Plus  stores will get the option to add their own deliverymen from the store panel.',
  'When_enabled,_customers_can_make_home_delivery_orders_from_this_store.' => 'When enabled  customers can make home delivery orders from this store.',
  'When_enabled,_customers_can_place_takeaway_orders_from_this_store.' => 'When enabled  customers can place takeaway orders from this store.',
  'When_enabled,_admin_will_only_receive_the_certain_commission_percentage_he_set_for_this_store._Otherwise,_the_system_default_commission_will_be_applied.' => 'When enabled  admin will only receive the certain commission percentage he set for this store. Otherwise  the system default commission will be applied.',
  'Want_to_delete_this_schedule?' => 'Want to delete this schedule ',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted' => 'If you select Yes  the time schedule will be deleted',
  'Can_a_Store_Cancel_Order?' => 'Can a Store Cancel Order ',
  'Store_Self_Registration?' => 'Store Self Registration ',
  'If_you_enable_this,_Stores_can_do_self-registration_from_the_store_or_customer_app_or_website.' => 'If you enable this  Stores can do self-registration from the store or customer app or website.',
  'If_you_disable_this,_the_Store_Self-Registration_feature_will_be_hidden_from_the_store_or_customer_app,_website,_or_admin_landing_page.' => 'If you disable this  the Store Self-Registration feature will be hidden from the store or customer app  website  or admin landing page.',
  'When_a_deliveryman_arrives_for_delivery,_Customers_will_get_a_4-digit_verification_code_on_the_order_details_section_in_the_Customer_App_and_needs_to_provide_the_code_to_the_delivery_man_to_verify_the_order.' => 'When a deliveryman arrives for delivery  Customers will get a 4-digit verification code on the order details section in the Customer App and needs to provide the code to the delivery man to verify the order.',
  'Delivery_Verification?' => 'Delivery Verification ',
  'If you enable this, the Deliveryman has to verify the order during delivery through a 4-digit verification code.' => 'If you enable this  the Deliveryman has to verify the order during delivery through a 4-digit verification code.',
  'If you disable this, Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.' => 'If you disable this  Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.',
  'With_this_feature,_customers_can_place_an_order_by_uploading_prescription._Stores_can_enable/disable_this_feature_from_the_store_settings_if_needed.' => 'With this feature  customers can place an order by uploading prescription. Stores can enable/disable this feature from the store settings if needed.',
  'Place_Order_by_Prescription?' => 'Place Order by Prescription ',
  'If_you_enable_this,_customers_can_place_an_order_by_simply_uploading_their_prescriptions_in_the_Pharmacy_module_from_the_Customer_App_or_Website._Stores_can_enable/disable_this_feature_from_store_settings_if_needed.' => 'If you enable this  customers can place an order by simply uploading their prescriptions in the Pharmacy module from the Customer App or Website. Stores can enable/disable this feature from store settings if needed.',
  'If_disabled,_this_feature_will_be_hidden_from_the_Customer_App,_Website,_and_Store_App_&_Panel.' => 'If disabled  this feature will be hidden from the Customer App  Website  and Store App & Panel.',
  'If_you_enable_this_feature,_customers_can_choose_‘Home_Delivery’_and_get_the_product_delivered_to_their_preferred_location.' => 'If you enable this feature  customers can choose ‘Home Delivery’ and get the product delivered to their preferred location.',
  'Home_Delivery?' => 'Home Delivery ',
  'If_you_enable_this,_customers_can_use_Home_Delivery_Option_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use Home Delivery Option during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Home_Delivery_feature_will_be_hidden_from_the_customer_app_and_website.' => 'If you disable this  the Home Delivery feature will be hidden from the customer app and website.',
  'If_you_enable_this_feature,_customers_can_place_an_order_and_request_‘Takeaways’_or_‘self-pick-up’_from_stores.' => 'If you enable this feature  customers can place an order and request ‘Takeaways’ or ‘self-pick-up’ from stores.',
  'the_Takeaway_feature?' => 'the Takeaway feature ',
  'If_you_enable_this,_customers_can_use_the_Takeaway_feature_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use the Takeaway feature during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Takeaway_feature_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the Takeaway feature will be hidden from the Customer App or Website.',
  'With_this_feature,_customers_can_choose_their_preferred_delivery_slot._Customers_can_select_a_delivery_slot_for_ASAP_or_a_specific_date_(within_2_days_from_the_order).' => 'With this feature  customers can choose their preferred delivery slot. Customers can select a delivery slot for ASAP or a specific date (within 2 days from the order).',
  'Scheduled Delivery?' => 'Scheduled Delivery ',
  'If_you_enable_this,_customers_can_choose_a_suitable_delivery_schedule_during checkout.' => 'If you enable this  customers can choose a suitable delivery schedule during checkout.',
  'If_you_disable_this,_the_Scheduled_Delivery_feature_will_be_hidden.' => 'If you disable this  the Scheduled Delivery feature will be hidden.',
  'By_activating_this_feature,_customers_can_choose_their_suitable_delivery_slot_according_to_a_30-minute_or_1-hour_interval_set_by_the_Admin.' => 'By activating this feature  customers can choose their suitable delivery slot according to a 30-minute or 1-hour interval set by the Admin.',
  'When this field is active, user can cancel an order with proper reason.' => 'When this field is active  user can cancel an order with proper reason.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_‘Cancel_Order‘_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the ‘Cancel Order‘ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  'If_you_want_to_delete_this_reason,_please_confirm_your_decision.' => 'If you want to delete this reason  please confirm your decision.',
  'Tips_for_Deliveryman_feature?' => 'Tips for Deliveryman feature ',
  'If_you_enable_this,_Customers_can_give_tips_to_a_deliveryman_during_checkout.' => 'If you enable this  Customers can give tips to a deliveryman during checkout.',
  'If_you_disable_this,_the_Tips_for_Deliveryman_feature_will_be_hidden_from_the_Customer_App_and_Website.' => 'If you disable this  the Tips for Deliveryman feature will be hidden from the Customer App and Website.',
  'With_this_feature,_Deliverymen_can_see_their_earnings_on_a_specific_order_while_accepting_it.' => 'With this feature  Deliverymen can see their earnings on a specific order while accepting it.',
  'Show_Earnings_in_App?' => 'Show Earnings in App ',
  'If_you_enable_this,_Deliverymen_can_see_their_earning_per_order_request_from_the_Order_Details_page_in_the_Deliveryman_App.' => 'If you enable this  Deliverymen can see their earning per order request from the Order Details page in the Deliveryman App.',
  'If_you_disable_this,_the_feature_will_be_hidden_from_the_Deliveryman_App.' => 'If you disable this  the feature will be hidden from the Deliveryman App.',
  'With_this_feature,_deliverymen_can_register_themselves_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page._The_admin_will_receive_an_email_notification_and_can_accept_or_reject_the_request.' => 'With this feature  deliverymen can register themselves from the Customer App  Website or Deliveryman App or Admin Landing Page. The admin will receive an email notification and can accept or reject the request.',
  'Deliveryman_Self_Registration?' => 'Deliveryman Self Registration ',
  'If_you_enable_this,_users_can_register_as_Deliverymen_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you enable this  users can register as Deliverymen from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you disable this  this feature will be hidden from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'Can_A_Deliveryman_Cancel_Order?' => 'Can A Deliveryman Cancel Order ',
  'take_picture_before_complete' => 'take picture before complete',
  'dm_picture_upload_status' => 'dm picture upload status',
  'picture_upload_before_complete?' => 'picture upload before complete ',
  'edit_bonus' => 'edit bonus',
  'wallet_bonus_update' => 'wallet bonus update',
  'bonus_updated_successfully' => 'bonus updated successfully',
  'How_the_Setting_Works' => 'How the Setting Works',
  'get_your_zip_file_from_the_purchased_theme_and_upload_it_and_activate_theme_with_your_Codecanyon_username_and_purchase_code' => 'get your zip file from the purchased theme and upload it and activate theme with your Codecanyon username and purchase code',
  'now_you’ll_be_successfully_able_to_use_the_theme_for_your_6Valley_website' => 'now you’ll be successfully able to use the theme for your 6Valley website',
  'N:B you_can_upload_only_6Valley’s_theme_templates' => 'N:B you can upload only 6Valley’s theme templates',
  'upload_theme' => 'upload theme',
  'instructions' => 'instructions',
  'please_make_sure' => 'please make sure',
  'your_server_php' => 'your server php',
  'value_is_grater
                                   _or_equal_to_20MB' => 'value is grater
                                    or equal to 20MB',
  'current_value_is' => 'current value is',
  'value_is_grater_or_equal_to_20MB' => 'value is grater or equal to 20MB',
  'are_you_sure?' => 'are you sure ',
  'want_to_change_status' => 'want to change status',
  'codecanyon_username' => 'codecanyon username',
  'purchase_code' => 'purchase code',
  'activate' => 'activate',
  'updated successfully!' => 'updated successfully!',
  'admin_default_landing_page' => 'admin default landing page',
  'integrate_landing_page_via' => 'integrate landing page via',
  'file_upload' => 'file upload',
  'none' => 'none',
  'landing_page_url' => 'landing page url',
  'want_to_turn_off_admin_landing_page' => 'want to turn off admin landing page',
  'updated_successfully!' => 'updated successfully!',
  'landing_page_is_on.' => 'landing page is on.',
  'want_to_turn_on_admin_landing_page' => 'want to turn on admin landing page',
  'background_colors' => 'background colors',
  'Ex_:_Receive_Latest_News,_Updates_and_Many_Other_News_Every_Week' => 'Ex : Receive Latest News  Updates and Many Other News Every Week',
  'Browse Web Button' => 'Browse Web Button',
  'Web Link' => 'Web Link',
  'Browse Web Button Enabled for Landing Page' => 'تصفح زر الويب ممكّن للصفحة المقصودة',
  'Browse Web Button Disabled for Landing Page' => 'Browse Web Button Disabled for Landing Page',
  'Browse Web button is enabled now everyone can use or see the button' => 'Browse Web button is enabled now everyone can use or see the button',
  'Browse Web button is disabled now no one can use or see the button' => 'Browse Web button is disabled now no one can use or see the button',
  'Ex: https://6ammart-web.6amtech.com/' => 'Ex: https://6ammart-web.6amtech.com/',
  'If you want to disable or turn off any section please leave that section empty, don’t make any changes there!' => 'If you want to disable or turn off any section please leave that section empty  don’t make any changes there!',
  'Let’s See The Changes!' => 'Let’s See The Changes!',
  'landing_page_is_off.' => 'landing page is off.',
  'url_saved_successfully!' => 'url saved successfully!',
  'If_you_enable_this,_delivery_man_can_upload_order_proof_before_order_delivery.' => 'If you enable this  delivery man can upload order proof before order delivery.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_delivery_man_app.' => 'If you disable this  this feature will be hidden from the delivery man app.',
  'Review_List' => 'Review List',
  'Order_ID' => 'Order ID',
  'Customer_Name' => 'Customer Name',
  'Rating' => 'Rating',
  'Review' => 'Review',
  'Store_List' => 'Store List',
  'Total_Store' => 'Total Store',
  'Active_Store' => 'Active Store',
  'Inactive_Store' => 'Inactive Store',
  'Newly_Joined' => 'Newly Joined',
  'Store_ID' => 'Store ID',
  'Store_Logo' => 'Store Logo',
  'Ratings' => 'Ratings',
  'Owner_Information' => 'Owner Information',
  'Total_Items' => 'Total Items',
  'Total_Orders' => 'Total Orders',
  'Featured_?' => 'Featured  ',
  'Total_reviews' => 'Total reviews',
  'Category' => 'Category',
  'Store_Wise_Review_List' => 'Store Wise Review List',
  'Store_details' => 'Store details',
  'store_deleted' => 'store deleted',
  'Want to delete this notification ?' => 'Want to delete this notification  ',
  'Food_List' => 'Food List',
  'Zone' => 'Zone',
  'Total_items' => 'Total items',
  'Active_Items' => 'Active Items',
  'Inactive_items' => 'Inactive items',
  'Veg' => 'Veg',
  'Item_List' => 'Item List',
  'Store_Cash_Transactions' => 'Store Cash Transactions',
  'Transaction_ID' => 'Transaction ID',
  'Transaction_Time' => 'Transaction Time',
  'Balance_Before_Transaction' => 'Balance Before Transaction',
  'Transaction_Amount' => 'Transaction Amount',
  'Payment_method' => 'Payment method',
  'delivery_man_id' => 'delivery man id',
  'store_amount' => 'store amount',
  'updated_at' => 'updated at',
  'original_delivery_charge' => 'original delivery charge',
  'parcel_catgory_id' => 'parcel catgory id',
  'dm_tips' => 'dm tips',
  'delivery_fee_comission' => 'delivery fee comission',
  'admin_expense' => 'admin expense',
  'store_expense' => 'store expense',
  'discount_amount_by_store' => 'discount amount by store',
  'Store_Order_Transactions' => 'Store Order Transactions',
  'Order_Time' => 'Order Time',
  'Total_order_amount' => 'Total order amount',
  'Delivery_Fee' => 'Delivery Fee',
  'Vat/Tax' => 'Vat/Tax',
  'Store_Withdraw_Transactions' => 'Store Withdraw Transactions',
  'Requested_Created_At' => 'Requested Created At',
  'Requested_Amount' => 'Requested Amount',
  'Request_Created_At' => 'Request Created At',
  'Store_Details' => 'Store Details',
  'Total_Order' => 'Total Order',
  'Scheduled_Order' => 'Scheduled Order',
  'Pending_Order' => 'Pending Order',
  'Delivered_Order' => 'Delivered Order',
  'Canceled_Order' => 'Canceled Order',
  'Refunded_Order' => 'Refunded Order',
  'Order_Date' => 'Order Date',
  'Item_Price' => 'Item Price',
  'Item_Discount' => 'Item Discount',
  'Coupon_Discount' => 'Coupon Discount',
  'Discounted_Amount' => 'Discounted Amount',
  'Total_Amount' => 'Total Amount',
  'Payment_Status' => 'Payment Status',
  'Order_Status' => 'Order Status',
  'Order_Type' => 'Order Type',
  'This Month\'s Statistics' => 'This Month s Statistics',
  'Your browser doesn\'t support geolocation' => 'Your browser doesn t support geolocation',
  'Sorry, stock limit exceeded.' => 'Sorry  stock limit exceeded.',
  'store_meta_data' => 'store meta data',
  'meta_title' => 'meta title',
  'meta_description' => 'meta description',
  'store_meta_image' => 'store meta image',
  'meta_image' => 'meta image',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted.' => 'If you select Yes  the time schedule will be deleted.',
  'Delete Bank Info ?' => 'Delete Bank Info  ',
  'Want to delete this' => 'Want to delete this',
  'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery  Coupon discount & item discounts(partial according to order commission).',
  'expense_reports' => 'expense reports',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over, store discount, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & item discounts(partial according to order commission).',
  'Invalid date range!' => 'Invalid date range!',
  'invalid_customer' => 'invalid customer',
  'bank info' => 'bank info',
  'collect cash' => 'collect cash',
  'customerList' => 'customerList',
  'Bonus_Title' => 'Bonus Title',
  'Ex:_EID_Dhamaka' => 'Ex: EID Dhamaka',
  'Ex:_100' => 'Ex: 100',
  'Ex:_10' => 'Ex: 10',
  'Ex:_1000' => 'Ex: 1000',
  'Ex_:_Search_by_bonus_title' => 'Ex : Search by bonus title',
  'Wallet_bonus_is_only_applicable_when_a_customer_add_fund_to_wallet_via_outside_payment_gateway_!' => 'Wallet bonus is only applicable when a customer add fund to wallet via outside payment gateway !',
  'Customer_will_get_extra_amount_to_his_/_her_wallet_additionally_with_the_amount_he_/_she_added_from_other_payment_gateways._The_bonus_amount_will_be_deduct_from_admin_wallet_&_will_consider_as_admin_expense.' => 'Customer will get extra amount to his / her wallet additionally with the amount he / she added from other payment gateways. The bonus amount will be deduct from admin wallet & will consider as admin expense.',
  'Ex:_Service_Charge' => 'Ex: Service Charge',
  'Want_to_Integrate_Your_Own_Customised_Landing_Page_?' => 'Want to Integrate Your Own Customised Landing Page  ',
  'Read_Instructions' => 'Read Instructions',
  'If_you_want_to_set_up_your_own_landing_page_please_flow_tha_instructions_below' => 'If you want to set up your own landing page please flow tha instructions below',
  'You_can_add_your_customised_landing_page_via_URL_or_upload_ZIP_file_of_the_landing_page.' => 'You can add your customised landing page via URL or upload ZIP file of the landing page.',
  'If_you_want_to_use_URL_option._Just_host_you_landing_page_and_copy_the_page_URL_and_click_save_information.' => 'If you want to use URL option. Just host you landing page and copy the page URL and click save information.',
  'If_you_want_to_Upload_your_landing_page_source_code_file.' => 'If you want to Upload your landing page source code file.',
  'a._Create_an_html_file_named_index.blade.php_and_insert_your_landing_page_design_code_and_make_a_zip_file.' => 'a. Create an html file named index.blade.php and insert your landing page design code and make a zip file.',
  'b._upload_the_zip_file_in_file_upload_section_and_click_save_information.' => 'b. upload the zip file in file upload section and click save information.',
  'a._Create_an_html_file_named' => 'a. Create an html file named',
  '_and_insert_your_landing_page_design_code_and_make_a_zip_file.' => ' and insert your landing page design code and make a zip file.',
  'The_Zip_will_content_only_one_file_named' => 'The Zip will content only one file named',
  'Take_Picture_For_Completing_Delivery' => 'Take Picture For Completing Delivery',
  'If_enabled,_deliverymen_will_see_an_option_to_take_pictures_of_the_delivered_products_when_he_swipes_the_delivery_confirmation_slide.' => 'If enabled  deliverymen will see an option to take pictures of the delivered products when he swipes the delivery confirmation slide.',
  'If_enabled,_customers_need_to_pay_an_extra_charge_while_checking_out_orders.' => 'If enabled  customers need to pay an extra charge while checking out orders.',
  'Set_a_name_for_the_additional_charge,_e.g._“Processing_Fee”.' => 'Set a name for the additional charge  e.g. “Processing Fee”.',
  'Ex:_Processing_Fee' => 'Ex: Processing Fee',
  'Set_the_value_(amount)_customers_need_to_pay_as_additional_charge.' => 'Set the value (amount) customers need to pay as additional charge.',
  'If_enabled,_customers_can_make_partial_payments._For_example,_a_customer_can_pay_$20_initially_out_of_their_$50_payment_&_use_other_payment_methods_for_the_rest._Partial_payments_must_be_made_through_their_wallets.' => 'If enabled  customers can make partial payments. For example  a customer can pay $20 initially out of their $50 payment & use other payment methods for the rest. Partial payments must be made through their wallets.',
  'Can_Pay_the_Rest_Amount_using' => 'Can Pay the Rest Amount using',
  'Set_the_method(s)_that_customers_can_pay_the_remainder_after_partial_payment.' => 'Set the method(s) that customers can pay the remainder after partial payment.',
  'Currently_you_are_using_6amMart_Default_Admin_Landing_Page_Theme.' => 'Currently you are using 6amMart Default Admin Landing Page Theme.',
  'Visit_Landing_Page' => 'Visit Landing Page',
  'Save_Information' => 'Save Information',
  'brrm' => 'brrm',
  'Business_Module?' => 'Business Module ',
  'If_you_activate_this_business_module,_all_its_features_and_functionalities_will_be_available_and_accessible_to_all_users.' => 'If you activate this business module  all its features and functionalities will be available and accessible to all users.',
  'If_you_deactivate_this_business_module,_all_its_features_and_functionalities_will_be_disabled_and_hidden_from_users.' => 'If you deactivate this business module  all its features and functionalities will be disabled and hidden from users.',
  'Hello, here you can manage your dispatch orders.' => 'Hello  here you can manage your dispatch orders.',
  'محل' => 'محل',
  'مقابل' => 'مقابل',
  'Otherwise this zone won\'t function properly & will work show anything against this zone' => 'Otherwise this zone won t function properly & will work show anything against this zone',
  'How does it works ?' => 'How does it works  ',
  1 => '1',
  'To_create_a_new_business_module,_go_to:_‘Module_Setup’_→_‘Add_Business_Module.’' => 'To create a new business module  go to: ‘Module Setup’ → ‘Add Business Module.’',
  2 => '2',
  'Go_to_‘Zone_Setup’→_‘Business_Zone_List’→_‘Zone_Settings’→_Choose_Payment_Method→Add_Business_Module_into_Zone_with_Parameters.' => 'Go to ‘Zone Setup’→ ‘Business Zone List’→ ‘Zone Settings’→ Choose Payment Method→Add Business Module into Zone with Parameters.',
  3 => '3',
  'Select_your_Module_from_the_Module_Section,_Click_→_’Store_Management’→’Add_Store’→Add_Store_details_&_select_Zone_to_integrate_Module+Zone+Store.' => 'Select your Module from the Module Section  Click → ’Store Management’→’Add Store’→Add Store details & select Zone to integrate Module+Zone+Store.',
  'Maximum_Purchase_Quantity_Limit' => 'Maximum Purchase Quantity Limit',
  'If_this_limit_is_exceeded,_customers_can_not_buy_the_item_in_a_single_purchase.' => 'If this limit is exceeded  customers can not buy the item in a single purchase.',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores.' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores.',
  'Bonus_Type' => 'Bonus Type',
  'Bonus_Amount' => 'Bonus Amount',
  'Set_the_bonus_amount/percentage_a_customer_will_receive_after_adding_money_to_his_wallet.' => 'Set the bonus amount/percentage a customer will receive after adding money to his wallet.',
  'Minimum_Add_Money_Amount' => 'Minimum Add Money Amount',
  'Set_the_minimum_add_money_amount_for_a_customer_to_be_eligible_for_the_bonus.' => 'Set the minimum add money amount for a customer to be eligible for the bonus.',
  'Maximum_Bonus' => 'Maximum Bonus',
  'Set_the_maximum_bonus_amount_a_customer_can_receive_for_adding_money_to_his_wallet.' => 'Set the maximum bonus amount a customer can receive for adding money to his wallet.',
  'bonus_added_successfully' => 'bonus added successfully',
  'Short_Description' => 'Short Description',
  'Ex : Search' => 'Ex : Search',
  'Are you sure' => 'هل أنت متأكد',
  'guest_varified' => 'Guest varified',
  'New_Store_Registration' => 'New Store Registration',
  'Email_Template' => 'Email Template',
  'Store_Registration' => 'Store Registration',
  'Other_Promotional_Content_Setup' => 'Other Promotional Content Setup',
  'video' => 'Video',
  'Video_/_Image' => 'Video / Image',
  'Section_Title' => 'Section Title',
  'Ex:Enter_section_title' => 'Ex:Enter section title',
  'Upload_Content' => 'Upload Content',
  'Image_Size_Min_615_x_350_px' => 'Image Size Min 615 x 350 px',
  'image_format_:_jpg_,_png_,_jpeg_|_maximum_size:_2_MB' => 'Image format : jpg   png   jpeg | maximum size: 2 MB',
  'Video_Link' => 'Video Link',
  'Enter_URL' => 'Enter URL',
  'Video_/_Image_Content' => 'Video / Image Content',
  'content-1' => 'Content-1',
  'Ex_:_Enter_Title' => 'Ex : Enter Title',
  'Ex_:_Enter_Subtitle' => 'Ex : Enter Subtitle',
  'content-2' => 'Content-2',
  'content-3' => 'Content-3',
  'order_management' => 'Order management',
  'all_orders' => 'All orders',
  'pending_orders' => 'Pending orders',
  'Offline_Payments' => 'Offline Payments',
  'product_management' => 'Product management',
  'delivery_section' => 'Delivery section',
  'delivery_management' => 'Delivery management',
  'deliveryman_section' => 'Deliveryman section',
  'deliveryman_management' => 'Deliveryman management',
  'customer_section' => 'Customer section',
  'employee_Role' => 'Employee Role',
  'add_new_Employee' => 'Add new Employee',
  'Employee_list' => 'Employee list',
  'collect_Cash' => 'Collect Cash',
  'delete_role' => 'Delete role',
  'business_settings' => 'Business settings',
  'websocket' => 'Websocket',
  'company_name' => 'Company name',
  'Order_Notification_Type' => 'Order Notification Type',
  'For_Firebase,_a_single_real-time_notification_will_be_sent_upon_order_placement,_with_no_repetition._For_the_Manual_option,_notifications_will_appear_at_10-second_intervals_until_the_order_is_viewed.' => 'For Firebase  a single real-time notification will be sent upon order placement  with no repetition. For the Manual option  notifications will appear at 10-second intervals until the order is viewed.',
  'manual' => 'Manual',
  'guest_checkout' => 'Guest checkout',
  'If_enabled,_customers_do_not_have_to_login_while_checking_out_orders.' => 'If enabled  customers do not have to login while checking out orders.',
  'Want_to_enable_guest_checkout?' => 'Want to enable guest checkout ',
  'Want_to_disable_guest_checkout?' => 'Want to disable guest checkout ',
  'If_you_enable_this,_guest_checkout_will_be_visible_when_customer_is_not_logged_in.' => 'If you enable this  guest checkout will be visible when customer is not logged in.',
  'If_you_disable_this,_guest_checkout_will_not_be_visible_when_customer_is_not_logged_in.' => 'If you disable this  guest checkout will not be visible when customer is not logged in.',
  'business_management' => 'Business management',
  'pages_setup' => 'Pages setup',
  'business_pages' => 'Business pages',
  'Offline_Payment_Setup' => 'Offline Payment Setup',
  'payment_gateway_setup' => 'Payment gateway setup',
  'If_enabled_Customers_will_be_able_to_select_COD_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select COD as a payment method during checkout',
  'If_enabled_Customers_will_be_able_to_select_digital_payment_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select digital payment as a payment method during checkout',
  'Offline_Payment' => 'Offline Payment',
  'If_enabled_Customers_will_be_able_to_select_offline_payment_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select offline payment as a payment method during checkout',
  'By Turning ON Offline_Payment Option' => 'By Turning ON Offline Payment Option',
  'By Turning OFF Offline_Payment Option' => 'By Turning OFF Offline Payment Option',
  'Customers will not be able to select Offline_Payment as a payment method during checkout. Please review your settings and enable Offline_Payment if you wish to offer this payment option to customers.' => 'Customers will not be able to select Offline Payment as a payment method during checkout. Please review your settings and enable Offline Payment if you wish to offer this payment option to customers.',
  'Customers will be able to select Offline_Payment as a payment method during checkout.' => 'Customers will be able to select Offline Payment as a payment method during checkout.',
  'Test' => 'Test',
  'payment_gateway_title' => 'Payment gateway title',
  'order_delivery_verification' => 'Order delivery verification',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’,_customers_can_request_a_refund.' => '*If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'Refund Request_Mode' => 'Refund Request Mode',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  'Want to delete this refund reason ?' => 'Want to delete this refund reason  ',
  'Refund_Reason_Update' => 'Refund Reason Update',
  'Product_Gallery' => 'Product Gallery',
  'If_enabled,can_create_duplicate_products.' => 'If enabled can create duplicate products.',
  'Want_to_enable_product_gallery?' => 'Want to enable product gallery ',
  'Want_to_disable_product_gallery?' => 'Want to disable product gallery ',
  'If_you_enable_this,can_create_duplicate_products' => 'If you enable this can create duplicate products',
  'If_you_disable_this,can_not_create_duplicate_products.' => 'If you disable this can not create duplicate products.',
  'access_all_products' => 'Access all products',
  'Want_to_enable_access_all_products?' => 'Want to enable access all products ',
  'Want_to_disable_access_all_products?' => 'Want to disable access all products ',
  'If_you_enable_this,_Stores_can_access_all_products_of_other_available_stores' => 'If you enable this  Stores can access all products of other available stores',
  'If_you_disable_this,_Stores_can_not_access_all_products_of_other_stores.' => 'If you disable this  Stores can not access all products of other stores.',
  'Need_Approval_for_Products' => 'Need Approval for Products',
  'If_enabled,_this_option_to_require_admin_approval_for_products_to_be_displayed_on_the_user_side.' => 'If enabled  this option to require admin approval for products to be displayed on the user side.',
  'Want_to_enable_product_approval?' => 'Want to enable product approval ',
  'Want_to_disable_product_approval?' => 'Want to disable product approval ',
  'If_you_enable_this,_option_to_require_admin_approval_for_products_to_be_displayed_on_the_user_side' => 'If you enable this  option to require admin approval for products to be displayed on the user side',
  'If_you_disable_this,products_will_to_be_displayed_on_the_user_side_without_admin_approval.' => 'If you disable this products will to be displayed on the user side without admin approval.',
  'Need_Approval_When' => 'Need Approval When',
  'Add_new_product' => 'Add new product',
  'Update_product_price' => 'Update product price',
  'Update_product_variation' => 'Update product variation',
  'Update_anything_in_product_details' => 'Update anything in product details',
  'On_every_purchase_this_percent_of_amount_will_be_added_as_loyalty_point_on_his_account' => 'On every purchase this percent of amount will be added as loyalty point on his account',
  'offline_Payment_Method' => 'Offline Payment Method',
  'Offline_Payment_Method_Setup' => 'Offline Payment Method Setup',
  'add_New_Method' => 'Add New Method',
  'payment_Method_Name' => 'Payment Method Name',
  'payment_Info' => 'Payment Info',
  'required_Info_From_Customer' => 'Required Info From Customer',
  'Want_to_enable_this_offline_payment_method?' => 'Want to enable this offline payment method ',
  'Want_to_disable_this_offline_payment_method?' => 'Want to disable this offline payment method ',
  'It_will_be_available_on_the_user_views.' => 'It will be available on the user views.',
  'It_will_be_hidden_from_the_user_views.' => 'It will be hidden from the user views.',
  'Want_to_delete_this_offline_payment_method' => 'Want to delete this offline payment method',
  'firebase_push_notification_setup' => 'Firebase push notification setup',
  'select_modules' => 'Select modules',
  'order_pending_message' => 'Order pending message',
  'order_confirmation_message' => 'Order confirmation message',
  'order_processing_message' => 'Order processing message',
  'order_Handover_message' => 'Order Handover message',
  'order_out_for_delivery_message' => 'Order out for delivery message',
  'order_delivered_message' => 'Order delivered message',
  'deliveryman_assign_message' => 'Deliveryman assign message',
  'deliveryman_delivered_message' => 'Deliveryman delivered message',
  'order_canceled_message' => 'Order canceled message',
  'order_refunded_message' => 'Order refunded message',
  'refund_request_canceled_message' => 'Refund request canceled message',
  'offline_order_accept_message' => 'Offline order accept message',
  'By Turning ON Offline Order ' => 'By Turning ON Offline Order ',
  'accept Message' => 'Accept Message',
  'By Turning OFF Offline Order ' => 'By Turning OFF Offline Order ',
  'User will get a clear message to know that offline order is accepted' => 'User will get a clear message to know that offline order is accepted',
  'User can not get a clear message to know that offline order is accepted or not' => 'User can not get a clear message to know that offline order is accepted or not',
  'offline_order_deny_message' => 'Offline order deny message',
  'deny Message' => 'Deny Message',
  'User will get a clear message to know that offline order is denied' => 'User will get a clear message to know that offline order is denied',
  'User can not get a clear message to know that offline order is denied or not' => 'User can not get a clear message to know that offline order is denied or not',
  'In the left-hand menu, click on the "Settings" gear icon, and then select "Project settings" from the dropdown.' => 'In the left-hand menu  click on the  Settings  gear icon  and then select  Project settings  from the dropdown.',
  'In the Project settings page, click on the "Cloud Messaging" tab from the top menu.' => 'In the Project settings page  click on the  Cloud Messaging  tab from the top menu.',
  'In the Firebase Project settings page, click on the "General" tab from the top menu.' => 'In the Firebase Project settings page  click on the  General  tab from the top menu.',
  'Under the "Your apps" section, click on the "Web" app for which you want to configure FCM.' => 'Under the  Your apps  section  click on the  Web  app for which you want to configure FCM.',
  'Then Obtain API Key, FCM Project ID, Auth Domain, Storage Bucket, Messaging Sender ID.' => 'Then Obtain API Key  FCM Project ID  Auth Domain  Storage Bucket  Messaging Sender ID.',
  'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation, terms of service, and any applicable laws and regulations.' => 'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation  terms of service  and any applicable laws and regulations.',
  'User can\'t get a clear message to know that order is pending or not' => 'User can t get a clear message to know that order is pending or not',
  'Product_approved' => 'Product approved',
  'Product_Rejection' => 'Product Rejection',
  'Send Mail on Store Registration ?' => 'Send Mail on Store Registration  ',
  'If_a_Store_registers_from_the_Customer_app_or_Website,_Admin_Landing_Page_or_Store_app,_they_will_get_a_confirmation_email.' => 'If a Store registers from the Customer app or Website  Admin Landing Page or Store app  they will get a confirmation email.',
  'Want_to_enable_Store_Registration_mail?' => 'Want to enable Store Registration mail ',
  'Want_to_disable_Store_Registration_mail?' => 'Want to disable Store Registration mail ',
  'If_enabled,_stores_will_get_a_registration_confirmation_email_when_they_register.' => 'If enabled  stores will get a registration confirmation email when they register.',
  'If_disabled,_stores_will_not_get_a_registration_confirmation_email_when_a_store_registers.' => 'If disabled  stores will not get a registration confirmation email when a store registers.',
  'Send_Mail_on_Product_Approval’?' => 'Send Mail on Product Approval’ ',
  'If_Admin_approves_a_Store’s_product,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin approves a Store’s product  the store will get an automatic disapproval mail from the system.',
  'Want_to_enable_product_approval_mail?' => 'Want to enable product approval mail ',
  'Want_to_disable__product_approval_mai?' => 'Want to disable  product approval mai ',
  'If_enabled,_Store_will_receive_a_confirmation_email_when_the_Admin_approves_their_product.' => 'If enabled  Store will receive a confirmation email when the Admin approves their product.',
  'If_disabled,__Users_will_not_get_a_product_approval_mail.' => 'If disabled   Users will not get a product approval mail.',
  'Send_Mail_on_Product_Deny’?' => 'Send Mail on Product Deny’ ',
  'If_Admin_rejects_a_Store’s_product,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin rejects a Store’s product  the store will get an automatic disapproval mail from the system.',
  'Want_to_enable_Store_product_deny_mail?' => 'Want to enable Store product deny mail ',
  'Want_to_disable_Store_product_deny_mail?' => 'Want to disable Store product deny mail ',
  'If_enabled,_Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_product.' => 'If enabled  Users will receive a confirmation email when the Admin rejects their product.',
  'If_disabled,__Users_will_not_get_a_product_rejection_mail.' => 'If disabled   Users will not get a product rejection mail.',
  'Minimum_Store_App_Version_for_store' => 'Minimum Store App Version for store',
  'Download_URL_for_Store_App_for_store' => 'Download URL for Store App for store',
  'What_is_App_Version?' => 'What is App Version ',
  'This_app_version_defines_the_Store,_Deliveryman,_and_User_app_version_of_6amMart.' => 'This app version defines the Store  Deliveryman  and User app version of 6amMart.',
  'It_doesn’t_represent_the_Play_Store_or_App_Store_version.' => 'It doesn’t represent the Play Store or App Store version.',
  'خضروات fgx' => 'خضروات fgx',
  'flash_sales' => 'Flash sales',
  'item_campaigns' => 'Item campaigns',
  'other_banners' => 'Other banners',
  'push_notification' => 'Push notification',
  'food_list' => 'Food list',
  'New_Item_Request' => 'New Item Request',
  'store_section' => 'Store section',
  'store_management' => 'Store management',
  'add_store' => 'Add store',
  'stores_list' => 'Stores list',
  'Recommended_Store' => 'Recommended Store',
  'flash_sale_setup' => 'Flash sale setup',
  'ex_:_new_flash_sale' => 'Ex : new flash sale',
  'discount_Bearer' => 'Discount Bearer',
  'Define_the_cost_amount_you_want_to_bear_for_this_Flash_Sale.The_total_bear_amount_should_be_100.' => 'Define the cost amount you want to bear for this Flash Sale.The total bear amount should be 100.',
  'Ex_:_50' => 'Ex : 50',
  'store_owner' => 'Store owner',
  'validity' => 'Validity',
  'start_date' => 'Start date',
  'end_date' => 'End date',
  'flash_sale_list' => 'Flash sale list',
  'ex_:_flash_sale_title' => 'Ex : flash sale title',
  'active_products' => 'Active products',
  'publish' => 'Publish',
  'Want_to_publish_this_flash_sale?' => 'Want to publish this flash sale ',
  'Want_to_hide_this_flash_sale?' => 'Want to hide this flash sale ',
  'If_you_publish_this_flash_sale,_Customers_can_see_all_stores_&_products_available_under_this_flash_sale_from_the_Customer_App_&_Website._other_flash_sales_will_be_turned_off.' => 'If you publish this flash sale  Customers can see all stores & products available under this flash sale from the Customer App & Website. other flash sales will be turned off.',
  'If_you_hide_this_flash_sale,_Customers_Will_NOT_see_all_stores_&_products_available_under_this_flash_sale_from_the_Customer_App_&_Website.' => 'If you hide this flash sale  Customers Will NOT see all stores & products available under this flash sale from the Customer App & Website.',
  'add-product' => 'Add-product',
  'Want to delete this flash_sale ?' => 'Want to delete this flash sale  ',
  'flash_sale_product_setup' => 'Flash sale product setup',
  'flash_sale_product_list' => 'Flash sale product list',
  'ex_:_product_name' => 'Ex : product name',
  'stock_for_this_sale' => 'Stock for this sale',
  'Qty_Sold' => 'Qty Sold',
  'dispatch_section' => 'Dispatch section',
  'dispatch_management' => 'Dispatch management',
  'add_Offline_Payment_Method' => 'Add Offline Payment Method',
  'Add_Offline_Payment_Method' => 'Add Offline Payment Method',
  'Section_View' => 'Section View',
  'payment_information' => 'Payment information',
  'Add_New_Field' => 'Add New Field',
  'ex:_bkash' => 'Ex: bkash',
  'Required Information from Customer' => 'Required Information from Customer',
  'Payment_Note' => 'Payment Note',
  'Ex:ABC_Company' => 'Ex:ABC Company',
  'Offline Payment' => 'Offline Payment',
  'This view is from the user app.' => 'This view is from the user app.',
  'This is how customer will see in the app' => 'This is how customer will see in the app',
  'Pay on this account' => 'Pay on this account',
  'Amount' => 'Amount',
  'Payment Info' => 'Payment Info',
  'input_field_name' => 'Input field name',
  'payment_By' => 'Payment By',
  'placeholder' => 'Placeholder',
  'Enter Name' => 'Enter Name',
  'is_required_?' => 'Is required  ',
  'Reached maximum' => 'Reached maximum',
  'Bank_Name' => 'Bank Name',
  'Data' => 'Data',
  'ABC_bank' => 'ABC bank',
  'edit_Offline_Payment_Method' => 'Edit Offline Payment Method',
  'Edit_Offline_Payment_Method' => 'Edit Offline Payment Method',
  'payment_Information' => 'Payment Information',
  'required_Information_from_Customer' => 'Required Information from Customer',
  'input_field_Name' => 'Input field Name',
  'place_Holder' => 'Place Holder',
  'enter_name' => 'Enter name',
  'is_Required' => 'Is Required',
  'bank_Name' => 'Bank Name',
  'AVC_bank' => 'AVC bank',
  'print_invoice' => 'Print invoice',
  'reference_code' => 'Reference code',
  'items_price' => 'Items price',
  'Paid_by' => 'Paid by',
  'Paid_with_Cash' => 'Paid with Cash',
  'COD' => 'COD',
  'delivery_info' => 'Delivery info',
  'delivery_proof' => 'Delivery proof',
  'add_Order Rejection_Note' => 'Add Order Rejection Note',
  'Confirm_Order Rejection' => 'Confirm Order Rejection',
  'reference_code_add' => 'Reference code add',
  'add_delivery_proof' => 'Add delivery proof',
  'assign_deliveryman' => 'Assign deliveryman',
  'location_data' => 'Location data',
  'order_filter' => 'Order filter',
  'date_between' => 'Date between',
  'Payment_verification' => 'Payment verification',
  'verified' => 'Verified',
  'method_name' => 'Method name',
  'bkash_number' => 'Bkash number',
  'bKash' => 'BKash',
  'Payment_Verified' => 'Payment Verified',
  'Payment_Details' => 'Payment Details',
  'Payment_Verification' => 'Payment Verification',
  'Please_Check_&_Verify_the_payment_information_weather_it_is_correct_or_not_before_confirm_the_order.' => 'Please Check & Verify the payment information weather it is correct or not before confirm the order.',
  'Phone' => 'Phone',
  'Payment_Information' => 'Payment Information',
  'Customer_Note' => 'Customer Note',
  'Add_Offline_Payment_Rejection_Note' => 'Add Offline Payment Rejection Note',
  'transaction_id_mismatched' => 'Transaction id mismatched',
  'Confirm_Rejection' => 'Confirm Rejection',
  'food_campaigns' => 'Food campaigns',
  'food_management' => 'Food management',
  'addon_list' => 'Addon list',
  'Food_Gallery' => 'Food Gallery',
  'New_Food_Request' => 'New Food Request',
  'restaurant_section' => 'Restaurant section',
  'restaurant_management' => 'Restaurant management',
  'restaurants_list' => 'Restaurants list',
  'Recommended_Restaurants' => 'Recommended Restaurants',
  'select_Role' => 'Select Role',
  'choose_file' => 'Choose file',
  'Do_not_Logout' => 'Do not Logout',
  'order_section' => 'Order section',
  'item_management' => 'Item management',
  'pending_item_list' => 'Pending item list',
  'marketing_section' => 'Marketing section',
  'Advertisement Management' => 'Advertisement Management',
  'New_Advertisement' => 'New Advertisement',
  'Advertisement_List' => 'Advertisement List',
  'Pending' => 'Pending',
  'Ad_List' => 'Ad List',
  'business_section' => 'Business section',
  'storeConfig' => 'StoreConfig',
  'notification_setup' => 'Notification setup',
  'My_Subscription' => 'My Subscription',
  'My_Business_Plan' => 'My Business Plan',
  'disbursement_method' => 'Disbursement method',
  'Report_section' => 'Report section',
  'disbursement_report' => 'Disbursement report',
  'employee_section' => 'Employee section',
  'Want_to_get_highlighted?' => 'Want to get highlighted?',
  'Create_ads_to_get_highlighted_on_the_app_and_web_browser' => 'Create ads to get highlighted on the app and web browser',
  'Create_Ads' => 'Create Ads',
  'store_settings' => 'Store settings',
  'your_note_here' => 'Your note here',
  'Not_Now' => 'Not Now',
  'Okay' => 'Okay',
  'provider_details' => 'Provider details',
  'Provider_Request_List' => 'Provider Request List',
  'cashback' => 'Cashback',
  'advertisement' => 'Advertisement',
  'Ad_Requests' => 'Ad Requests',
  'Ads_list' => 'Ads list',
  'Enter_a_reason' => 'Enter a reason',
  'edit_details' => 'Edit details',
  'new_provider_request' => 'New provider request',
  'reject' => 'Reject',
  'update_&_approve' => 'Update & approve',
  'General_Info' => 'General Info',
  'Provider Logo & Covers' => 'Provider Logo & Covers',
  'Auto Focus Car Service' => 'Auto Focus Car Service',
  'House: 00, Road: 00, Test City' => 'House: 00, Road: 00, Test City',
  'Cover' => 'Cover',
  'provider_list' => 'Provider list',
  'Provider_List' => 'Provider List',
  'Total_Provider' => 'Total Provider',
  'Active_Provider' => 'Active Provider',
  'Inactive_Provider' => 'Inactive Provider',
  'Newly_joined' => 'Newly joined',
  'Total_Providers' => 'Total Providers',
  'Search by provider name, owner info...' => 'Search by provider name, owner info...',
  'new_provider' => 'New provider',
  'provider' => 'Provider',
  'owner_info' => 'Owner info',
  'total_vehicle' => 'Total vehicle',
  'total_driver' => 'Total driver',
  'total_trip' => 'Total trip',
  'business_featured' => 'Business featured',
  'Car_Rental_Service' => 'Car Rental Service',
  'Cameron_Williamson' => 'Cameron Williamson',
  'delete_store' => 'Delete store',
  'Subscription' => 'Subscription',
  'disbursements' => 'Disbursements',
  'business_plan' => 'Business plan',
  'Billing' => 'Billing',
  'Expire Date' => 'Expire Date',
  'Total_Bill' => 'Total Bill',
  'Number of Uses' => 'Number of Uses',
  'Package Overview' => 'Package Overview',
  'Expired' => 'Expired',
  'Days' => 'Days',
  'POS' => 'POS',
  'Mobile_App' => 'Mobile App',
  'product_Upload' => 'Product Upload',
  'Change / Renew Subscription Plan' => 'Change / Renew Subscription Plan',
  'Change Plan' => 'Change Plan',
  'Renew or shift your plan to get better experience!' => 'Renew or shift your plan to get better experience!',
  'Commission Base' => 'Commission Base',
  'Store will pay' => 'Store will pay',
  'commission to' => 'Commission to',
  'from each order. You will get access of all the features and options  in store panel , app and interaction with user.' => 'From each order. You will get access of all the features and options  in store panel , app and interaction with user.',
  'You_Want_To_Migrate_To_Commission.' => 'You Want To Migrate To Commission.',
  'Shift in this plan' => 'Shift in this plan',
  'mobile_app' => 'Mobile app',
  'chatting_options' => 'Chatting options',
  'review_section' => 'Review section',
  'Unlimited_Orders' => 'Unlimited Orders',
  'Unlimited_uploads' => 'Unlimited uploads',
  'Shift_in_this_plan' => 'Shift in this plan',
  'uploads' => 'Uploads',
  'Renew' => 'Renew',
  'Are_You_Sure_You_want_To_switch_to_this_plan?' => 'Are You Sure You want To switch to this plan?',
  'You_are_about_to_downgrade_your_plan.After_subscribing_to_this_plan_your_oldest_' => 'You are about to downgrade your plan.After subscribing to this plan your oldest ',
  'Items_will_be_inactivated.' => 'Items will be inactivated.',
  'Continue' => 'Continue',
  'Go_Back' => 'Go Back',
  'Are_you_sure?' => 'Are you sure?',
  'Successfully_canceled_the_subscription' => 'Successfully canceled the subscription',
  'Successfully_Switched_To_Commission' => 'Successfully Switched To Commission',
  'driver_list' => 'Driver list',
  'Auto_Focus_Car_Service' => 'Auto Focus Car Service',
  'trip_list' => 'Trip list',
  'vehicles' => 'Vehicles',
  'Total_Trips' => 'Total Trips',
  'new_driver' => 'New driver',
  'Trip ID' => 'Trip ID',
  'Trip_Date' => 'Trip Date',
  'Customer_Info' => 'Customer Info',
  'Driver_Info' => 'Driver Info',
  'Vehicle_Info' => 'Vehicle Info',
  'Trip_Type' => 'Trip Type',
  'Trip_Amount' => 'Trip Amount',
  'Trip_Status' => 'Trip Status',
  1234567 => '1234567',
  'Unassigned' => 'Unassigned',
  'F Premio 2006' => 'F Premio 2006',
  'Nator Kha 21-3214' => 'Nator Kha 21-3214',
  'Hourly' => 'Hourly',
  'Instant' => 'Instant',
  '$1,550.35' => '$1,550.35',
  'Paid' => 'Paid',
  'Canceled' => 'Canceled',
  'Brooklyn Simmons' => 'Brooklyn Simmons',
  '<EMAIL>' => '<EMAIL>',
  'Unpaid' => 'Unpaid',
  'Ongoing' => 'Ongoing',
  'priority_setup' => 'Priority setup',
  'disbursement' => 'Disbursement',
  'Automated_Message' => 'Automated Message',
  'By_turning_the_‘Maintenance_Mode’_ON,_all_your_apps_and_customer_website_will_be_disabled_temporarily._Only_the_Admin_Panel,_Admin_Landing_Page_&_Store_Panel_will_be_functional.' => 'By turning the ‘Maintenance Mode’ ON, all your apps and customer website will be disabled temporarily. Only the Admin Panel, Admin Landing Page & Store Panel will be functional.',
  'clicking_on_the_map_will_set_Latitude_and_Longitude_automatically' => 'Clicking on the map will set Latitude and Longitude automatically',
  'If_you_enable_it,_customers_will_see_the_product_Price_including_Tax,_during_checkout.' => 'If you enable it, customers will see the product Price including Tax, during checkout.',
  'country_picker' => 'Country picker',
  'If_you_enable_this_option,_in_all_phone_no_field_will_show_a_country_picker_list.' => 'If you enable this option, in all phone no field will show a country picker list.',
  'Want_to_enable_country_picker?' => 'Want to enable country picker?',
  'Want_to_disable_country_picker?' => 'Want to disable country picker?',
  'If_you_enable_this,_user_can_select_country_from_country_picker' => 'If you enable this, user can select country from country picker',
  'If_you_disable_this,_user_can_not_select_country_from_country_picker,_default_country_will_be_selected' => 'If you disable this, user can not select country from country picker, default country will be selected',
  'Additional Charge' => 'Additional Charge',
  'Payment' => 'Payment',
  'Shipping Charge' => 'Shipping Charge',
  'Business_Plan' => 'Business Plan',
  'If_enabled,_the_package_based_subscription_business_model_option_will_be_available_for_stores' => 'If enabled, the package based subscription business model option will be available for stores',
  'Subscription_Base' => 'Subscription Base',
  'Business_Model' => 'Business Model',
  'Want_to_disable_the' => 'Want to disable the',
  'If_enabled,_the_subscription_based_store_business_model_option_will_be_available_in_this_store' => 'If enabled, the subscription based store business model option will be available in this store',
  'If_disabled,_the_subscription_based_store_business_model_option_will_be_hidden_from_this_store_panel._The_existing_subscribed_stores’_subscriptions_will_be_valid_till_the_packages_expire' => 'If disabled, the subscription based store business model option will be hidden from this store panel. The existing subscribed stores’ subscriptions will be valid till the packages expire',
  'If_enabled,_the_commission_based_business_model_option_will_be_available_for_stores.' => 'If enabled, the commission based business model option will be available for stores.',
  'Commission_Base' => 'Commission Base',
  'If_enabled,_the_commission_based_store_business_model_option_will_be_available_for_this_store' => 'If enabled, the commission based store business model option will be available for this store',
  'If_disabled,_the_commission_based_store_business_model_option_will_be_hidden_from_this_store_panel._And_it_can_only_use_the_subscription_based_business_model' => 'If disabled, the commission based store business model option will be hidden from this store panel. And it can only use the subscription based business model',
  'Are_you_sure_to_change_the_currency_?' => 'Are you sure to change the currency ?',
  'If_you_enable_this_currency,_you_must_active_at_least_one_digital_payment_method_that_supports_this_currency._Otherwise_customers_cannot_pay_via_digital_payments_from_the_app_and_websites._And_Also_restaurants_cannot_pay_you_digitally' => 'If you enable this currency, you must active at least one digital payment method that supports this currency. Otherwise customers cannot pay via digital payments from the app and websites. And Also restaurants cannot pay you digitally',
  'Go_to_payment_method_settings.' => 'Go to payment method settings.',
  'OK' => 'OK',
  'subscription_management' => 'Subscription management',
  'subscription_Package' => 'Subscription Package',
  'Subscriber_List' => 'Subscriber List',
  'Notification_Channels' => 'Notification Channels',
  'Edit Provider - Business Plan Setup' => 'Edit Provider - Business Plan Setup',
  'Business Basic Setup' => 'Business Basic Setup',
  'Business Plan Setup' => 'Business Plan Setup',
  'Choose Business Plan' => 'Choose Business Plan',
  'You have to give a certain percentage of commission to admin for every Trip request.' => 'You have to give a certain percentage of commission to admin for every Trip request.',
  'Subscription Base' => 'Subscription Base',
  'You have to pay certain amount in every month/year to admin as subscription fee.' => 'You have to pay certain amount in every month/year to admin as subscription fee.',
  'Choose Subscription Package' => 'Choose Subscription Package',
  'BASIC' => 'BASIC',
  'Free Support 24/7' => 'Free Support 24/7',
  'Databases' => 'Databases',
  '00 Monthly Trip' => '00 Monthly Trip',
  '5 Vehicle Add' => '5 Vehicle Add',
  'STANDARED' => 'STANDARED',
  ' Free Support 24/7' => ' Free Support 24/7',
  '1000 Monthly Trip' => '1000 Monthly Trip',
  '15 Vehicle Add' => '15 Vehicle Add',
  'PREMIUM' => 'PREMIUM',
  'Unlimited Trip' => 'Unlimited Trip',
  'Unlimited Vehicle' => 'Unlimited Vehicle',
  'Total_Trip' => 'Total Trip',
  'Complete' => 'Complete',
  'Canceled_Trip' => 'Canceled Trip',
  'Driver_Status' => 'Driver Status',
  'Banners' => 'Banners',
  'Add_New_Banner' => 'Add New Banner',
  'Provider_Logo_&_Covers' => 'Provider Logo & Covers',
  'Select_Provider' => 'Select Provider',
  'Banner_Image' => 'Banner Image',
  'Click to upload' => 'Click to upload',
  'or drag and drop' => 'Or drag and drop',
  'Banner_Info' => 'Banner Info',
  'banner_type' => 'Banner type',
  'By_Turning_ON_As_Featured!' => 'By Turning ON As Featured!',
  'By_Turning_OFF_As_Featured!' => 'By Turning OFF As Featured!',
  'If_you_turn_on_this_featured,_then_promotional_banner_will_show_on_website_and_user_app_with_store_or_item.' => 'If you turn on this featured, then promotional banner will show on website and user app with store or item.',
  'If_you_turn_off_this_featured,_then_promotional_banner_won’t_show_on_website_and_user_app' => 'If you turn off this featured, then promotional banner won’t show on website and user app',
  'By_Turning_ON_Banner!' => 'By Turning ON Banner!',
  'By_Turning_OFF_Banner!' => 'By Turning OFF Banner!',
  'If_you_turn_on_this_status,_it_will_show_on_user_website_and_app.' => 'If you turn on this status, it will show on user website and app.',
  'If_you_turn_off_this_status,_it_won’t_show_on_user_website_and_app' => 'If you turn off this status, it won’t show on user website and app',
  'edit_banner' => 'Edit banner',
  'add_new_banner' => 'Add new banner',
  'banner_list' => 'Banner list',
  'if_you_turn/off_on_this_featured,_it_will_effect_on_website_&_user_app' => 'If you turn/off on this featured, it will effect on website & user app',
  'vehicle_details' => 'Vehicle details',
  'Fare_&_Discounts' => 'Fare & Discounts',
  'Other_Features' => 'Other Features',
  'Identity_Info' => 'Identity Info',
  'Documents_And_Images' => 'Documents And Images',
  'Here you can see all images & document for the provider' => 'Here you can see all images & document for the provider',
  'Documents' => 'Documents',
  'Review_ID' => 'Review ID',
  'Date' => 'Date',
  'Provider_Reply' => 'Provider Reply',
  'Jhone Doe III' => 'Jhone Doe III',
  'Gas Stove is very important in our daily life, most importantly it cooks food. So, when a gas stove breaks down it requires urgent servicing.' => 'Gas Stove is very important in our daily life, most importantly it cooks food. So, when a gas stove breaks down it requires urgent servicing.',
  'Provider Details - Add New Vehicale' => 'Provider Details - Add New Vehicale',
  'Add New Vehicle' => 'Add New Vehicle',
  'General_Information' => 'General Information',
  'vehicle_name' => 'Vehicle name',
  'type_vehicle_name' => 'Type vehicle name',
  'type_business_address' => 'Type business address',
  'Vehicle_Information' => 'Vehicle Information',
  'brand' => 'Brand',
  'select_vehicle_brand' => 'Select vehicle brand',
  'brand 1' => 'Brand 1',
  'brand 2' => 'Brand 2',
  'select_vehicle_category' => 'Select vehicle category',
  'category 1' => 'Category 1',
  'category 2' => 'Category 2',
  'select_vehicle_type' => 'Select vehicle type',
  'family' => 'Family',
  'Engine Capacity (cc)' => 'Engine Capacity (cc)',
  'Engine Power (hp)' => 'Engine Power (hp)',
  'Seating Capacity' => 'Seating Capacity',
  'Air Condition' => 'Air Condition',
  'fuel_type' => 'Fuel type',
  'select_fuel_type' => 'Select fuel type',
  'select_vehicle_fuel_type' => 'Select vehicle fuel type',
  'diesel' => 'Diesel',
  'transmission_type' => 'Transmission type',
  'select_vehicle_transmission' => 'Select vehicle transmission',
  'transmission 1' => 'Transmission 1',
  'transmission 2' => 'Transmission 2',
  'break_system' => 'Break system',
  'select_vehicle_break_system' => 'Select vehicle break system',
  'Disc Break' => 'Disc Break',
  'Business_Info' => 'Business Info',
  'business_zone' => 'Business zone',
  'select_business_zone_for_map' => 'Select business zone for map',
  'pickup_zone' => 'Pickup zone',
  'select_pickup_zone_for_map' => 'Select pickup zone for map',
  'New_York_State' => 'New York State',
  'Washington' => 'Washington',
  'Chicago_Municipal' => 'Chicago Municipal',
  'Approx. Pickup Time' => 'Approx. Pickup Time',
  'Pricing & Discounts' => 'Pricing & Discounts',
  'Distance Wise Price ($)' => 'Distance Wise Price ($)',
  'hourly' => 'Hourly',
  'Distance Wise' => 'Distance Wise',
  'select_discount' => 'Select discount',
  'Ex: 10' => 'Ex: 10',
  'Search_Tags' => 'Search Tags',
  'Vehicle_Thumbnail' => 'Vehicle Thumbnail',
  'Images' => 'Images',
  'JPG, JPEG, PNG Less Than 1MB' => 'JPG, JPEG, PNG Less Than 1MB',
  '(Ratio 2:1)' => '(Ratio 2:1)',
  'Vehicle Identity' => 'Vehicle Identity',
  'Same Model Multiple Vehicles' => 'Same Model Multiple Vehicles',
  'VIN Number' => 'VIN Number',
  'The VIN number has already been taken' => 'The VIN number has already been taken',
  'The license plate number has already been taken' => 'The license plate number has already been taken',
  'License Plate Number' => 'License Plate Number',
  'VIN number is required' => 'VIN number is required',
  'License plate number is required' => 'License plate number is required',
  'Vehicle_Documents' => 'Vehicle Documents',
  'image_added' => 'Image added',
  'General Info' => 'General Info',
  'Business Plan' => 'Business Plan',
  'Ex: ABC Company' => 'Ex: ABC Company',
  'business_module' => 'Business module',
  'Select_zone_first' => 'Select zone first',
  'select_module' => 'Select module',
  'Upload Cover Photo' => 'Upload Cover Photo',
  'login_info' => 'Login info',
  'Next' => 'Next',
  'Choose Your Business Plan' => 'Choose Your Business Plan',
  'Commision_Base' => 'Commision Base',
  'Run store by puchasing subsciption packages. You will have access the features of in store panel , app and interaction with user according to the subscription packages.' => 'Run store by puchasing subsciption packages. You will have access the features of in store panel , app and interaction with user according to the subscription packages.',
  'Back' => 'Back',
  'Password is valid' => 'Password is valid',
  'Password format is invalid' => 'Password format is invalid',
  'Store_logo_&_cover_photos_are_required' => 'Store logo & cover photos are required',
  'Store_name_is_required' => 'Store name is required',
  'Store_address_is_required' => 'Store address is required',
  'You_must_select_a_zone' => 'You must select a zone',
  'You_must_select_a_module' => 'You must select a module',
  'Must_click_on_the_map_for_lat/long' => 'Must click on the map for lat/long',
  'tax_is_required' => 'Tax is required',
  'minimum_delivery_time_is_required' => 'Minimum delivery time is required',
  'max_delivery_time_is_required' => 'Max delivery time is required',
  'last_name_is_required' => 'Last name is required',
  'valid_phone_number_is_required' => 'Valid phone number is required',
  'email_is_required' => 'Email is required',
  'password_is_required' => 'Password is required',
  'confirm_password_does_not_match' => 'Confirm password does not match',
  'You_must_select_a_package' => 'You must select a package',
  'Contact_Us' => 'Contact Us',
  'test module' => 'Test module',
  'test frewthrejktgr' => 'Test frewthrejktgr',
  'are_available.' => 'Are available.',
  'Seller App' => 'Seller App',
  'Deliveryman App' => 'Deliveryman App',
  'google_play' => 'Google play',
  'apple_store' => 'Apple store',
  'User App' => 'User App',
  'trips_details' => 'Trips details',
  'trip' => 'Trip',
  'Provider_Info' => 'Provider Info',
  'Trip_served' => 'Trip served',
  'Trip ID # 1000078' => 'Trip ID # 1000078',
  'trip_setup' => 'Trip setup',
  'Dashboard - Taxi Module' => 'Dashboard - Taxi Module',
  'Car_Rental_Module_Dashboard' => 'Car Rental Module Dashboard',
  'Monitor_your' => 'Monitor your',
  'car_rental_business' => 'Car rental business',
  'Delivery_Statistics' => 'Delivery Statistics',
  'All_Time' => 'All Time',
  'Confirmed' => 'Confirmed',
  'Ongoing_Trip' => 'Ongoing Trip',
  'Gross_Earnings' => 'Gross Earnings',
  'Earnings' => 'Earnings',
  'this_month' => 'This month',
  'this_week' => 'This week',
  'Top_Zones' => 'Top Zones',
  'Dhaka_Zone' => 'Dhaka Zone',
  '(Business_Zone)' => '(Business Zone)',
  'Trips' => 'Trips',
  'Mirpur_Zone' => 'Mirpur Zone',
  'Top_Vehicles' => 'Top Vehicles',
  'Toyota - F Premio 2006' => 'Toyota - F Premio 2006',
  'Trip Details' => 'Trip Details',
  'Additional_Documents' => 'Additional Documents',
  'You can upload a maximum of' => 'You can upload a maximum of',
  'files.' => 'Files.',
  'welcome_back_login_to_your_panel' => 'Welcome back login to your panel',
  'new_vehicle' => 'New vehicle',
  'Brand' => 'Brand',
  'Trip Fair' => 'Trip Fair',
  'Cancel_Trip' => 'Cancel Trip',
  'Cameron Williamson' => 'Cameron Williamson',
  'request_list' => 'Request list',
  'Pending_Request' => 'Pending Request',
  'Rejected_Request' => 'Rejected Request',
  'Total_Requested' => 'Total Requested',
  'business_address' => 'Business address',
  '4517 Washington Ave. Manchester, Kentucky 3949' => '4517 Washington Ave. Manchester, Kentucky 3949',
  'Standard' => 'Standard',
  'Are you sure, want to approve the request?' => 'Are you sure, want to approve the request?',
  'Approval Note' => 'Approval Note',
  'Type your Approval Note' => 'Type your Approval Note',
  'Are you sure, want to cancel the request?' => 'Are you sure, want to cancel the request?',
  'Cancellation Note' => 'Cancellation Note',
  'Type your Cancellation Note' => 'Type your Cancellation Note',
  'Add New Driver' => 'Add New Driver',
  'User_Info' => 'User Info',
  'Type your first name' => 'Type your first name',
  'Type your last name' => 'Type your last name',
  'Type your email address' => 'Type your email address',
  'Profile Image' => 'Profile Image',
  'Select Driver Identity type' => 'Select Driver Identity type',
  'Identity_Number' => 'Identity Number',
  'update_&_next' => 'Update & next',
  'Provider_Setup' => 'Provider Setup',
  'Want_to_enable_store_temporarily_closed?' => 'Want to enable store temporarily closed?',
  'Want_to_disable_store_temporarily_closed?' => 'Want to disable store temporarily closed?',
  'If_you_enable_this,_store_will_be_temporarily_closed.' => 'If you enable this, store will be temporarily closed.',
  'If_you_disable_this,_store_will__not_be_temporarily_closed.' => 'If you disable this, store will  not be temporarily closed.',
  'Basic_Settings' => 'Basic Settings',
  'manage_vehicle_setup' => 'Manage vehicle setup',
  'Want_to_enable_manage_vehicle_setup?' => 'Want to enable manage vehicle setup?',
  'Want_to_disable_manage_vehicle_setup?' => 'Want to disable manage vehicle setup?',
  'If_you_enable_this,_manage_vehicle_setup_will_be_enabled.' => 'If you enable this, manage vehicle setup will be enabled.',
  'If_you_disable_this,_manage_vehicle_setup_will_be_disabled.' => 'If you disable this, manage vehicle setup will be disabled.',
  'scheduled_trip' => 'Scheduled trip',
  'Want_to_enable_scheduled_trip?' => 'Want to enable scheduled trip?',
  'Want_to_disable_scheduled_trip?' => 'Want to disable scheduled trip?',
  'If_you_enable_this,_scheduled_trip_will_be_enabled.' => 'If you enable this, scheduled trip will be enabled.',
  'If_you_disable_this,_scheduled_trip_will_be_disabled.' => 'If you disable this, scheduled trip will be disabled.',
  'Minimum Trip Amount (hr)' => 'Minimum Trip Amount (hr)',
  'extra_service_charge' => 'Extra service charge',
  'Want_to_enable_extra_service_charge?' => 'Want to enable extra service charge?',
  'Want_to_disable_extra_service_charge?' => 'Want to disable extra service charge?',
  'If_you_enable_this,_extra_service_charge_will_be_enabled.' => 'If you enable this, extra service charge will be enabled.',
  'If_you_disable_this,_extra_service_charge_will_be_disabled.' => 'If you disable this, extra service charge will be disabled.',
  'Extra Service Charge Amount' => 'Extra Service Charge Amount',
  'When ON guest user can make trip' => 'When ON guest user can make trip',
  'Want_to_enable_gst?' => 'Want to enable gst?',
  'Want_to_disable_gst?' => 'Want to disable gst?',
  'If_you_enable_this,_gst_will_be_enabled.' => 'If you enable this, gst will be enabled.',
  'If_you_disable_this,_gst_will_be_disabled.' => 'If you disable this, gst will be disabled.',
  'provider_cancelation_rate' => 'Provider cancelation rate',
  'Want_to_enable_provider_cancelation_rate?' => 'Want to enable provider cancelation rate?',
  'Want_to_disable_provider_cancelation_rate?' => 'Want to disable provider cancelation rate?',
  'If_you_enable_this,_provider_cancelation_rate_will_be_enabled.' => 'If you enable this, provider cancelation rate will be enabled.',
  'If_you_disable_this,_provider_cancelation_rate_will_be_disabled.' => 'If you disable this, provider cancelation rate will be disabled.',
  'Cancelation Rate Limit' => 'Cancelation Rate Limit',
  'Cancelation Rate Warning' => 'Cancelation Rate Warning',
  'Hourly Wise Price ($)' => 'Hourly Wise Price ($)',
  'Provider_Active_Time' => 'Provider Active Time',
  'Minimum Trip Amount (Hr)' => 'Minimum Trip Amount (Hr)',
  'search_data' => 'Search data',
  'all_category' => 'All category',
  'select_sub_category' => 'Select sub category',
  'all_sub_category' => 'All sub category',
  'ex_:_search_item_by_name' => 'Ex : search item by name',
  'New_Product_Request' => 'New Product Request',
  'edit_item' => 'Edit item',
  'delete_item' => 'Delete item',
  'Lay\'s Classic Chips' => 'Lay s Classic Chips',
  'Total_Quantity' => 'Total Quantity',
  'Stock' => 'Stock',
  'red-small' => 'Red-small',
  'Total_Stock' => 'Total Stock',
  'update_stock' => 'Update stock',
  'Call_Us' => 'Call Us',
  'Send_Message' => 'Send Message',
  'Edit_Vechicle' => 'Edit Vechicle',
  'not_now' => 'Not now',
  'Driver_Details' => 'Driver Details',
  'Edit_Driver' => 'Edit Driver',
  'Search by trip ID, customer name...' => 'Search by trip ID, customer name...',
  'Columns' => 'Columns',
  'Booking_Date' => 'Booking Date',
  'Schedule_At' => 'Schedule At',
  'Provider Details - Settings' => 'Provider Details - Settings',
  'vendor_cancelation_rate' => 'Vendor cancelation rate',
  'vendor_cancelation_rate?' => 'Vendor cancelation rate?',
  'If_you_enable_this,vendor_cancelation_rate_will_be_enabled.' => 'If you enable this,vendor cancelation rate will be enabled.',
  'If_you_disable_this,vendor_cancelation_rate_will_be_disabled.' => 'If you disable this,vendor cancelation rate will be disabled.',
  'Vendor_Active_Time' => 'Vendor Active Time',
  'Vendor Logo & Covers' => 'Vendor Logo & Covers',
  'Low_Stock_List' => 'Low Stock List',
  'driver_Assigned' => 'Driver Assigned',
  'ongoing' => 'Ongoing',
  'Home_Page_Setup' => 'Home Page Setup',
  'Download App' => 'Download App',
  'Vendors Registration' => 'Vendors Registration',
  'Its much easier From Apps' => 'Its much easier From Apps',
  'type_title' => 'Type title',
  '26/30' => '26/30',
  'subtitle' => 'Subtitle',
  'type_subtitle' => 'Type subtitle',
  '52/110' => '52/110',
  'Banner_Type' => 'Banner Type',
  'Provider_Wise' => 'Provider Wise',
  'JPG, JPEG, PNG Less Than 2MB' => 'JPG, JPEG, PNG Less Than 2MB',
  'Ratio 3:1' => 'Ratio 3:1',
  'Banner_List' => 'Banner List',
  'Search by banner title...' => 'Search by banner title...',
  'provider_wise' => 'Provider wise',
  'Trip_management' => 'Trip management',
  'all_trips' => 'All trips',
  'scheduled_trips' => 'Scheduled trips',
  'pending_trips' => 'Pending trips',
  'confirmed_trips' => 'Confirmed trips',
  'Ongoing_trips' => 'Ongoing trips',
  'Completed_trips' => 'Completed trips',
  'canceled_trips' => 'Canceled trips',
  'payment_failed_trips' => 'Payment failed trips',
  'vehicle_section' => 'Vehicle section',
  'vehicle_management' => 'Vehicle management',
  'brands' => 'Brands',
  'Vehicle Setup' => 'Vehicle Setup',
  'create_new' => 'Create new',
  'vehicle_list' => 'Vehicle list',
  'provider_section' => 'Provider section',
  'provider_management' => 'Provider management',
  'new_providers_request' => 'New providers request',
  'add new provider' => 'Add new provider',
  'providers_list' => 'Providers list',
  'providers list' => 'Providers list',
  'Download_Apps' => 'Download Apps',
  'You have new trip, Check Please.' => 'You have new trip, Check Please.',
  'Add new brand' => 'Add new brand',
  'add_new_brand' => 'Add new brand',
  'Required.' => 'Required.',
  'new_brand' => 'New brand',
  'brand_list' => 'Brand list',
  'search_brands' => 'Search brands',
  'ex_:_brands' => 'Ex : brands',
  'edit_brand' => 'Edit brand',
  'Want to delete this brand' => 'Want to delete this brand',
  'delete_brand' => 'Delete brand',
  'edit_category' => 'Edit category',
  'delete_category' => 'Delete category',
  'Want to delete this driver' => 'Want to delete this driver',
  'delete_driver' => 'Delete driver',
  'Identity Information' => 'Identity Information',
  'Identity Type' => 'Identity Type',
  'Identity Number' => 'Identity Number',
  'Provider Info' => 'Provider Info',
  'Image Description' => 'Image Description',
  'Search by trip ID' => 'Search by trip ID',
  'Search by trip ID...' => 'Search by trip ID...',
  'car_Assigned' => 'Car Assigned',
  'distance_wise' => 'Distance wise',
  'Want to delete this vehicle' => 'Want to delete this vehicle',
  'new_tag' => 'New tag',
  'Excellent' => 'Excellent',
  'See more' => 'See more',
  'luxury' => 'Luxury',
  'Transmission' => 'Transmission',
  'dual_clutch' => 'Dual clutch',
  'Fuel Type' => 'Fuel Type',
  'jet_fuel' => 'Jet fuel',
  'Engine Capacity' => 'Engine Capacity',
  'Engine Power' => 'Engine Power',
  'Registration No.' => 'Registration No.',
  'Click to view the file' => 'Click to view the file',
  'Taxi Module' => 'Taxi Module',
  'Lets' => 'Lets',
  'Manage_your_business' => 'Manage your business',
  'Smartly_or_Earn' => 'Smartly or Earn',
  'vendor_registration' => 'Vendor registration',
  'Your Name' => 'Your Name',
  'Terms_And' => 'Terms And',
);