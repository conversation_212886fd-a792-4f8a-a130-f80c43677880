.bootstrap-tagsinput {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.09);
    border-radius: 3px;
    color: #4d627b;
    cursor: text;
    display: inline-block;
    line-height: 22px;
    margin-bottom: 10px;
    max-width: 100%;
    min-width: 100%;
    padding: 4px 6px 0;
    vertical-align: middle
}

.bootstrap-tagsinput input {
    background-color: transparent;
    border: 0;
    box-shadow: none;
    color: #4d627b;
    margin: 0;
    max-width: inherit;
    outline: 0;
    padding: 0;
    width: auto !important
}

.bootstrap-tagsinput input::placeholder {
    color: rgba(0, 0, 0, .4)
}

.bootstrap-tagsinput input:focus {
    border: 0;
    box-shadow: none
}

.bootstrap-tagsinput .tag {
    border-radius: 2px;
    background: #177bbb;
    color: white;
    display: inline-block;
    font-size: 12px;
    font-weight: normal;
    margin: 0 2px 5px 0;
    padding: 5px
}

.bootstrap-tagsinput .tag [data-role="remove"] {
    box-shadow: none !important;
    cursor: pointer;
    margin-inline-start: 8px
}

.bootstrap-tagsinput .tag [data-role="remove"]:after {
    content: "x";
    font-size: 12px;
    padding: 0 2px
}

.bootstrap-tagsinput .tag [data-role="remove"]:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05)
}

.bootstrap-tagsinput .tag [data-role="remove"]:hover:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125)
}
