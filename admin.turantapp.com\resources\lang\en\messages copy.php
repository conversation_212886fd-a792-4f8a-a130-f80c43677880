<?php return array (
  'alert_store_out_from_campaign' => 'You want your store out from this campaign?',
  'alert_store_join_campaign' => 'You want join this campaign?',
  'about_us' => 'About Us',
  'about_us_image' => 'About Us Image',
  'about_us_updated' => 'About us updated!',
  'about_the_campaign' => 'About the campaign...',
  'account_transaction' => 'Account transaction',
  'account_transaction_removed' => 'Account transaction removed!',
  'access_denied' => 'Access Denied !',
  'access_token' => 'Access Token',
  'add' => 'Add',
  'add_new_item' => 'Add new item',
  'add_delivery_man' => 'Add Delivery Man',
  'add_customer' => 'Register new customer',
  'update_discount' => 'Update discount',
  'update_tax' => 'Update tax',
  'addon' => 'Addon',
  'add_to_cart' => 'Add To Cart',
  'admin' => 'Admin',
  'admin_or_employee_signin' => 'Admin or Employee Sign in',
  'admin_updated_successfully' => 'Admin updated successfully!',
  'admin_password_updated_successfully' => 'Admin password updated successfully!',
  'all' => 'All',
  'all_stores' => 'All Stores',
  'all_categories' => 'All Categories',
  'android' => 'Android',
  'application_placed_successfully' => 'Application placed successfully',
  'application_status_updated_successfully' => 'Application status updted successfully',
  'approved' => 'approved',
  'approve' => 'Approve',
  'app_minimum_version' => 'App minimum version',
  'app_url' => 'App url',
  'app_settings' => 'App Settings',
  'app_settings_updated' => 'App Settings Updated',
  'app_store' => 'App Store',
  'as' => 'as',
  'at' => 'at',
  'attribute' => 'Attribute',
  'attribute_choice_option_value_can_not_be_null' => 'Attribute choice option values can not be null!',
  'attributes' => 'Attributes',
  'active_status_updated' => 'Active status updated successfully',
  'minimum_delivery_time' => 'Minimum delivery time',
  'maximum_delivery_time' => 'Maximum delivery time',
  'approx_delivery_time' => 'Approx delivery time',
  'availability' => 'availability',
  'banner' => 'Banner',
  'banner_status_updated' => 'Banner status updated',
  'banner_featured_status_updated' => 'Banner featured status updated',
  'banner_added_successfully' => 'Banner added successfully',
  'banner_updated_successfully' => 'Banner updated successfully',
  'banner_deleted_successfully' => 'Banner deleted successfully',
  'banners' => 'Banners',
  'balance' => 'Balance',
  'balance_before_transaction' => 'Balance before transaction',
  'bank' => 'bank',
  'bank_info_updated_successfully' => 'Bank Info updated',
  'basic' => 'Basic',
  'basic_campaign' => 'Basic Campaign',
  'basic_information' => 'Basic Information',
  'between' => 'Between',
  'branches' => 'branches',
  'branch' => 'Branch',
  'business' => 'Business',
  'bulk_import' => 'Bulk Import',
  'bulk_export' => 'Bulk Export',
  'button_links' => 'Button & Links',
  'by_admin' => 'by admin',
  'can_not_accept' => 'Can not accept!',
  'cancel' => 'Cancel',
  'canceled' => 'Canceled',
  'cash' => 'cash',
  'cash_in_hand' => 'Cash in hand',
  'earning_balance' => 'Earning balance',
  'card' => 'Card',
  'card_holder_name' => 'Card holder name',
  'card_holder_email' => 'Card holder E-mail',
  'card_number' => 'Card number',
  'card_expire_month' => 'Card expire month',
  'card_expire_year' => 'Card expire year',
  'cart_empty_warning' => 'Your cart is empty',
  'category' => 'Category',
  'categories' => 'Categories',
  'category_deleted' => 'Category deleted',
  'category_required' => 'Category  is required!',
  'category_status_updated' => 'Category status updated!',
  'category_updated_successfully' => 'Category updated successfully!',
  'category_priority_updated successfully' => 'Category priority updated successfully!',
  'category_imported_successfully' => ':count - Categories imported successfully!',
  'choice_title' => 'Choice Title',
  'change_your_password' => 'Change your password',
  'click_on_the_map_select_your_defaul_location' => 'Click on the map select your default location',
  'click_to_edit_this_item' => 'Click to edit this item',
  'collected_cash' => 'Collected Cash',
  'collect' => 'Collect',
  'collected_cash_by_store' => 'Collected cash by store',
  'collect_cash_from_store' => 'Collect cash from store',
  'coordinates_out_of_zone' => 'Coordinates out of zone!',
  'country' => 'country',
  'country_code_is_must' => 'Country code is must',
  'like_for_BD_880' => 'like for BD 880',
  'count' => 'count',
  'config' => 'Config',
  'config_data_updated' => 'Config data updated!',
  'config_your_account' => 'Config your :method account',
  'configuration_updated_successfully' => 'Configuration updated successfully!',
  'confirmed' => 'Confirmed',
  'confirm_password' => 'Confirm Password',
  'confirm_this_order' => 'Confirm this Order',
  'cancel_this_order' => 'Cancel this Order',
  'coupon' => 'Coupon',
  'coupons' => 'Coupons',
  'coupon_added_successfully' => 'Coupon added successfully!',
  'coupon_updated_successfully' => 'Coupon updated successfully!',
  'coupon_deleted_successfully' => 'Coupon deleted successfully!',
  'coupon_status_updated' => 'Coupon status updated',
  'coupon_usage_limit_over' => 'Coupon usage limit is over for you!',
  'coupon_expire' => 'coupon expire',
  'cover' => 'cover',
  'currency_symbol_positon' => 'Currency symbol position',
  'currency_added_successfully' => 'Currency added successfully!',
  'currency_updated_successfully' => 'Currency updated successfully!',
  'currency_deleted_successfully' => 'Currency deleted successfully!',
  'current' => 'Current',
  'customer_not_found' => 'Customer not found!',
  'dashboard' => 'Dashboard',
  'day_wise_report' => 'Per Day Report',
  'item_wise_report' => 'Item Wise Report',
  'deleted' => 'deleted',
  'delivered' => 'Delivered',
  'deliverymen_earning_provide' => 'Distribute DM Earning',
  'deliveryman_preview' => 'Delivery Man Preview',
  'delivery_zone' => 'Delivery Zone',
  'delivery_charge' => 'Delivery Fee',
  'delivery_address_updated' => 'Delivery address updated!',
  'deliveryman_earning_type' => 'Delivery man earning type',
  'deny' => 'Deny',
  'designation' => 'Designation',
  'dm_maximum_order' => 'Maximum assigned orders for delivery man',
  'dm_maximum_order_hint' => 'Number of maximum orders a delivery man can serve at a time',
  'dm_maximum_order_exceed_warning' => 'Delivery man maximum serving orders exceed!',
  'dispatchManagement' => 'Dispatch Management',
  'duration' => 'Duration',
  'duplicate_data_on_column' => 'Duplicate data on :field column',
  'dues' => 'Dues',
  'earned' => 'earned',
  'email' => 'Email',
  'email_is_ready_to_register' => 'Email is ready to register',
  'employees' => 'Employees',
  'employee' => 'Employee',
  'employee_added_successfully' => 'Employee added successfully',
  'employee_deleted_successfully' => 'Employee deleted successfully',
  'employee_updated_successfully' => 'Employee updated successfully',
  'end' => 'End',
  'enter_if_you_want_to_change' => 'enter if you want to change',
  'enter_new_email_address' => 'Enter new email address',
  'enter_new_password' => 'Enter new password',
  'enter_choice_values' => 'Enter choice values',
  'confirm_new_password' => 'Confirm your new password',
  'edit_store' => 'Edit store',
  'edited' => 'edited',
  'extra_discount' => 'Extra Discount',
  'failed' => 'Failed',
  'faield_to_create_order_transaction' => 'Failed to create order transaction!',
  'failed_to_import_data' => 'Failed to import data!',
  'failed_to_place_order' => 'Failed to place order! Please try again.',
  'faield_to_send_sms' => 'Failed to send SMS',
  'featured' => 'Featured',
  'filter' => 'filter',
  'first_name' => 'First name',
  'first_order' => 'First order',
  'flutterwave' => 'Flutterwave',
  'item_list' => 'Item List',
  'full_name' => 'Full Name',
  'product_already_added_in_cart' => 'Item already added in cart',
  'product_has_been_added_in_cart' => 'Item has been added in your cart!',
  'product_has_been_updated_in_cart' => 'Cart item has been updated!',
  'product_imported_successfully' => ':count - Items imported successfully!',
  'free_delivery' => 'Free delivery',
  'free_delivery_over' => 'Free delivery over',
  'freelancer' => 'Freelancer',
  'from' => 'From',
  'form' => 'form',
  'goto' => 'Go to ',
  'handover' => 'handover',
  'hash' => 'Hash',
  'high' => 'High',
  'item_wise' => 'Item wise',
  'store_wise' => 'Store wise',
  'information' => 'Information',
  'insufficient_balance' => 'Insufficient balance!',
  'installments' => 'Installments',
  'item_has_been_removed_from_cart' => 'Item has been removed from cart',
  'item_type' => 'Item Type',
  'is' => 'is',
  'ios' => 'IOS',
  'join_us' => 'Join Us',
  'last_name' => 'Last name',
  'landing_page' => 'Landing Page',
  'landing_page_settings' => 'Landing Page Settings',
  'landing_page_image_settings' => 'Landing Page Image Settings',
  'landing_page_image_updated' => 'Landing Page Image Updated',
  'landing_page_text_updated' => 'Landing Page Text Updated Successfully',
  'landing_page_links_updated' => 'Landing Page Links & Buttons Updated Successfully',
  'landing_page_speciality_updated' => 'Landing Page Speciality Updated Successfully',
  'landing_page_testimonial_updated' => 'Landing Page Testimonials Updated Successfully',
  'left' => 'Left',
  'list' => 'List',
  'location' => 'Location',
  'login' => 'login',
  'store_application' => 'Store Application',
  'store_featured_status_updated' => 'Store featured status updated',
  'store_required_warning' => 'Make sure you have selected a store first!',
  'store_temporarily_closed' => 'Store temporarily closed sucessfully',
  'store_temporarily_closed_title' => 'Store temporarily closed',
  'store_opened' => 'Store opened successfully',
  'store_settings_updated' => 'Store settings updated!',
  'store_registration' => 'Store registration',
  'deliveryman_registration' => 'Deliveryman Registration',
  'deliveryman_application' => 'Deliveryman Application',
  'category_required_warning' => 'Make sure you have selected a category first!',
  'select_zone_for_map' => ' Select a zone based on your store location. After selecting the zone you can see the zone on the map',
  'show_hide_item_menu' => 'By disable it, store will not be able to manage (edit, update, delete or change status) any item but can view all items.',
  'show_hide_reviews_menu' => 'Show or hide reviews menu from store panel.',
  'show_locations_on_map' => 'Show locations on map',
  'system' => 'System',
  'store_lat_lng_warning' => 'Select your store’s exact location from the map (Just click on the map for pick location). The location must be inside the selected zone.',
  'draw_your_zone_on_the_map' => '(draw your zone area on the map)',
  'customer_varification_toggle' => 'If this field is active, customer have to verify their phone number througn an OTP',
  'order_varification_toggle' => 'If this field is active then customer have to provide a OTP code to delivery man for delivered a order successfully',
  'order_confirmation_model' => 'Order confirmation model',
  'order_confirmation_model_hint' => 'Selected model will show the pending orders first and can confirm them',
  'store_data_updated' => 'Store data updated successfully!',
  'store_not_found' => 'Store not found!',
  'interest_updated_successfully' => 'Interest successfully updated!',
  'mail' => 'Mail',
  'maek_ready_for_delivery' => 'Make Ready for Delivery',
  'make_ready_for_handover' => 'Make Ready for Handover',
  'maek_delivered' => 'Make Delivered',
  'map_api_key' => 'Map api key',
  'map_api_hint' => 'NB: Client key should have enable map javascript api and you can restrict it with http refere. Server key should have enable place api key and you can restrict it with ip.',
  'map_api_hint_2' => ' You can use same api for both field without any restrictions.',
  'marketing' => 'Marketing',
  'maintenance_mode' => 'maintenance mode',
  'mercadopago' => 'MercadoPago',
  'me' => 'me',
  'merchant' => 'Merchant',
  'messages' => 'Messages',
  'minimum' => 'Minimum',
  'medium' => 'Medium',
  'minimum_shipping_charge' => 'Minimum delivery charge',
  'misconfiguration_or_data_missing' => 'Misconfiguration or data is missing!',
  'my' => 'My',
  'my_bank_info' => 'My Bank Information',
  'my_shop' => 'My Store',
  'new' => 'New',
  'new_password' => 'New Password',
  'new_order' => 'New order!',
  'new_order_push_description' => 'New order placed',
  'news_letter_signup' => 'Sign Up For Newsletter',
  'news_letter_signup_text' => 'Receive Latest News, Updates and Many Other News Every Week',
  'normal' => 'Normal',
  'optional' => 'Optional',
  'ongoingOrders' => 'Ongoing Orders',
  'on_going' => 'Ongoing',
  'online' => 'online',
  'offline' => 'offline',
  'weekly_off_day' => 'Weekly Offday',
  'owner' => 'Owner',
  'order' => 'Order',
  'order_already_assign_to_tis_deliveryman' => 'Already assign to tis delivery man',
  'order_push_title' => 'Order',
  'order_ammount_wit_delivery_carge' => 'order ammount wit delivery carge',
  'out_for_delivery' => 'Out for delivery',
  'out_of_coverage' => 'Out of coverage!',
  'our_features' => 'Our Features',
  'our_mobile_applications' => 'Our Mobile Apps',
  'our_features_section_paragrap' => 'Our Features Summary',
  'pages' => 'Pages',
  'password' => 'Password',
  'password_length_warning' => 'Password length must be :length caracters.',
  'password_length_placeholder' => ':length characters required',
  'password_not_matched' => 'Password Not Matched',
  'paystack' => 'Paystack',
  'paystack_callback_warning' => 'NB: Don`t forget to copy and past the callback url on your paystack dashboard',
  'per_km_shipping_charge' => 'Delivery charge per KM',
  'permission_denied' => 'Permission denied! please contact to admin',
  'pending' => 'Pending',
  'pending_take_away' => 'pending (take away)',
  'play_store' => 'Play Store',
  'please_select_store' => 'Please select a store!',
  'read_more' => 'Read More',
  'select_category' => 'Select Category',
  'select_date' => 'select date!',
  'select_store' => 'select Store',
  'select_zone' => 'select zone',
  'select_item' => 'Select Item',
  'select_off_day' => 'Select off day',
  'please_choose_all_options' => 'Please choose all the options',
  'please_fill_all_required_fields' => 'Please fill all the required fields',
  'processing' => 'Processing',
  'Proceed_for_cooking' => 'Proceed for processing',
  'privacy_policy' => 'Privacy Policy',
  'privacy_policy_updated' => 'Privacy policy updated!',
  'priority' => 'Priority',
  'product' => 'Product',
  'product_name_required' => 'Item name is required!',
  'product_added_successfully' => 'Item added successfully',
  'product_updated_successfully' => 'Item updated successfully',
  'product_deleted_successfully' => 'Item deleted successfully',
  'product_status_updated' => 'Item status updated',
  'discount_can_not_be_more_than_or_equal' => 'Discount can not be more or equal to the price!',
  'provide' => 'provide',
  'provided_dm_earnings_removed' => 'Provide deliveryman earnings removed!',
  'publicKey' => 'Public Key',
  'push' => 'Push',
  'received_from' => 'Received from',
  'received_at' => 'Received at',
  'received_by' => 'Received by',
  'remember' => 'Remember',
  'remove_sub_categories_first' => 'Remove subcategories first!',
  'request' => 'Request',
  'request_time' => 'Request Time',
  'required' => 'required',
  'store_commission' => 'Store commission',
  'store_view' => 'Store view',
  'store_is_closed_at_order_time' => 'Store is closed at orderd time',
  'store_owner_login_form' => 'Store Owner Login Form',
  'returned' => 'Returned',
  'reviews' => 'Reviews',
  'review_visibility_updated' => 'Review visibility updated!',
  'right' => 'Right',
  'role_added_successfully' => 'Role added successfully!',
  'role_deleted' => 'Role deleted',
  'scheduled_at' => 'Scheduled at',
  'scheduled' => 'Scheduled',
  'schedule_order_not_available' => 'Schedule order not available',
  'scheduled_date_is_store_offday' => 'Sorry! Order scheduled date is store offday. Please chose another date',
  'secret' => 'secret',
  'security_code' => 'Security code',
  'section' => 'Section',
  'search_addons' => 'Search addons',
  'search_categories' => 'Search categories',
  'search_sub_categories' => 'Search sub categories',
  'search_here' => 'Search here',
  'searchingDM' => 'Searching Delivery Man',
  'searching_for_deliverymen' => 'Searching For Delivery Man',
  'search_your_location_here' => 'Search your location here',
  'signin' => 'Signin',
  'sign_in_as_employee' => 'Sign in as Store Employee',
  'sign_in_as_owner' => 'Sign in as Store Owner',
  'speciality' => 'Speciality',
  'speciality_title' => 'Speciality Title',
  'speciality_img' => 'Speciality Image',
  'social_login' => 'Social Login',
  'social' => 'Social Pages',
  'Instagram' => 'Instagram',
  'Facebook' => 'Facebook',
  'Twitter' => 'Twitter',
  'LinkedIn' => 'LinkedIn',
  'Pinterest' => 'Pinterest',
  'social_media_link' => 'Social Media Url',
  'social_media_inserted' => 'Social Media Inserted',
  'social_media_exist' => 'Social Media Al Ready Exist',
  'social_media_updated' => 'Social Media Updated Successfully',
  'social_media_deleted' => 'Social media deleted Successfully',
  'social_media_required' => 'Social media is requeired',
  'are_u_sure_want_to_delete' => 'Are you sure delete this social media?',
  'unsuspend_this_delivery_man' => 'Unsuspend This Delivery Man',
  'suspend_this_delivery_man' => 'Suspend This Delivery Man',
  'suspended' => 'Suspended',
  'testimonial' => 'Testimonial',
  'feature' => 'Feature',
  'feature_title' => 'Feature Title',
  'feature_section_description' => 'Feature Section Description',
  'feature_description' => 'Feature Description',
  'feature_img' => 'Feature Image',
  'landing_page_feature_updated' => 'Landing page feature updated',
  'landing_page_feature_feature_section_paragraph' => 'Feature Section Text',
  'testimonial_title' => 'Testimonial Title',
  'paymob_supports_EGP_currency' => 'Paymob supports EGP currency',
  'country_permission_denied_or_misconfiguration' => 'Country permission denyed or misconfigured',
  'paymob_accept' => 'Paymob accept',
  'callback' => 'Callback',
  'transactions' => 'Transactions',
  'accepted' => 'Accepted',
  'acceptedbyDM' => 'Accepted By Delivery Man',
  'preparingInStores' => 'Item Preparing in Stores',
  'itemOnTheWay' => 'Item on the Way',
  'refundRequest' => 'Refund Request',
  'refunded' => 'Refunded',
  'refund_this_order' => 'Refund this order',
  'senang' => 'Senang',
  'pay' => 'Pay',
  'send' => 'Send',
  'settings' => 'Settings',
  'setup' => 'Setup',
  'sl#' => 'SL',
  'sign_in' => 'Sign in',
  'size' => 'size',
  'sub_category' => 'Sub Category',
  'subscription_successful' => 'Subscription successful',
  'subscription_exist' => 'Subscription email already exist',
  'subscribed_mail_list' => 'Subscribed Emails',
  'table' => 'Table',
  'to' => 'to',
  'total_earning' => 'Total earning',
  'transaction' => 'Transaction',
  'transaction_saved' => 'Transaction saved successfully!',
  'transaction_updated' => 'Transaction updated successfully!',
  'summary' => 'Summary',
  'url' => 'url',
  'upload' => 'Upload',
  'upload_zip_file' => 'upload zip file',
  'verification' => 'Verification',
  'veg' => 'Veg',
  'non_veg' => 'Non Veg',
  'walk_in_customer' => 'Walk-in Customer',
  'wallet' => 'wallet',
  'want' => 'Want',
  'wise' => 'Wise',
  'withdraw' => 'Withdraw',
  'withdraw_able_balance' => 'Withdrawable balance',
  'withdraws' => 'Withdraws',
  'withdraw_request_placed_successfully' => 'Withdraw request has been placed successfully!',
  'with' => 'with',
  'your' => 'your',
  'your_currency_is_not_supported' => 'Your currency is not supported by :method.',
  'order_can_not_cancle_after_confirm' => 'You can not cancle a order after confirmed!',
  'warning_add_bank_info' => 'Please add your Bank information first!',
  'warning_missing_bank_info' => 'Bank Information Missing!',
  'tergat' => 'Tergat',
  'not_found' => 'Not found!',
  'created_at' => 'Created at',
  'role_form' => 'Role Form',
  'role_name' => 'Role Name',
  'module_permission' => 'System Module Permission',
  'roles_table' => 'Role Table',
  'modules' => 'Modules',
  'employee_form' => 'Employee Form',
  'employee_image' => 'Employee Image',
  'Role' => 'Role',
  'employee_management' => 'Employee Management',
  'Employee' => 'Employee',
  'role_management' => 'Role Management',
  'custom_role' => 'Custom Role',
  'employee_handle' => 'Employee Handler',
  'notification' => 'Notification',
  'notification_status_updated' => 'Notification status updated!',
  'notification_deleted_successfully' => 'Notification deleted successfully!',
  'payment' => 'Payment',
  'payment_failed' => 'Payment failed',
  'payment_settings_updated' => 'Payment settings updated',
  'payment_reference_code_is_added' => 'Payment reference code is added!',
  'methods' => 'Methods',
  'terms_and_condition' => 'Terms & Conditions',
  'terms_and_condition_updated' => 'Terms & Conditions updated!',
  'register' => 'Register',
  'customer' => 'Customer',
  'report_and_analytics' => 'Report & Analytics',
  'deliveryman' => 'Delivery Man',
  'deliverymen' => 'Delivery Men',
  'deliveryman_type_updated' => 'Delivery-man type updated!',
  'deliveryman_status_updated' => 'Delivery-man status updated!',
  'deliveryman_added_successfully' => 'Delivery-man added successfully!',
  'deliveryman_updated_successfully' => 'Delivery-man updated successfully!',
  'deliveryman_deleted_successfully' => 'Delivery-man deleted successfully!',
  'earning' => 'Earning',
  'report' => 'Report',
  'reports' => 'Reports',
  'web_and_app' => 'Web & App',
  'web_app_url' => 'Web app url (Front-end)',
  'mobile_app_section_heading' => 'Mobile app section heading',
  'mobile_app_section_text' => 'Mobile App Section Paragraph',
  'welcome' => 'Welcome',
  'welcome_message' => 'Hello, here you can manage your orders by zone.',
  'employee_welcome_message' => 'Hello, here you can manage your stores.',
  'we_are_temporarily_unavailable_in_this_area' => 'We are temporarily unavailable in this area.',
  'none_of_your_sms_gateway_is_active_or_configured!' => 'None of your SMS gateway is active or configured!',
  'failed_to_send_sms' => 'Failed to send sms! Please contact to our customer care.',
  'otp_sent_successfull' => 'OTP sent successfully!',
  'products' => 'Products',
  'total' => 'Total',
  'sl' => 'SL',
  'orders' => 'Orders',
  'order_id' => 'Order ID',
  'order_placed' => 'Order placed successfully',
  'order_place_notification' => 'Notification mail for order placed',
  'order_place_text' => 'We have sent you this email in response to your order placed. You will be able to see your order status after login to your account',
  'order_place_fail_text' => 'If you need help, or you have any other questions, feel free to email us',
  'order_thanks' => 'Thanks for the order',
  'order_items' => 'Items',
  'order_unit_price' => 'Unit Price',
  'order_qty' => 'Quantity',
  'coupon_discount' => 'Coupon discount',
  'monthly' => 'Monthly',
  'overview' => 'Overview',
  'in' => 'in',
  'items' => 'Items',
  'people' => 'People',
  'like' => 'Like',
  'top' => 'Top',
  'here' => 'Here',
  'sign_out' => 'Sign Out',
  'profile' => 'Profile',
  'profile_settings' => 'Profile Settings',
  'profile_updated_successfully' => 'Profile updated successfully!',
  'select' => 'Select',
  'export' => 'Export',
  'copy' => 'Copy',
  'copy_callback' => 'Copy callback url',
  'print' => 'Print',
  'excel' => 'Excel',
  'csv' => 'Csv',
  'pdf' => 'Pdf',
  'columns' => 'Columns',
  'date' => 'Date',
  'daily' => 'daily',
  'status' => 'Status',
  '#' => 'SL',
  'action' => 'Action',
  'actions' => 'Actions',
  'download' => 'Download',
  'support' => 'Support',
  'download_our_apps' => 'Download our apps',
  'document_type' => 'Document type',
  'document_number' => 'Document number',
  'option' => 'Option',
  'options' => 'Options',
  'view' => 'View',
  'invoice' => 'Invoice',
  'issuing_bank' => 'Issuing bank',
  'details' => 'Details',
  'paid' => 'Paid',
  'unpaid' => 'Unpaid',
  'note' => 'Note',
  'method' => 'Method',
  'reference' => 'Reference',
  'code' => 'Code',
  'type' => 'Type',
  'contact' => 'Contact',
  'info' => 'Information',
  'variation' => 'Variation',
  'addons' => 'Addons',
  'addon_added_successfully' => 'Addon added successfully!',
  'addon_updated_successfully' => 'Addon updated successfully!',
  'addon_deleted_successfully' => 'Addon deleted successfully!',
  'addon_status_updated' => 'Addon status updated!',
  'addon_imported_successfully' => ':count - Addons imported successfully!',
  'attribute_added_successfully' => 'Attribute added successfully!',
  'attribute_updated_successfully' => 'Attribute updated successfully!',
  'attribute_deleted_successfully' => 'Attribute deleted successfully!',
  'attribute_status_updated' => 'Attribute status updated!',
  'attribute_imported_successfully' => ':count - attributes imported successfully!',
  'price' => 'Price',
  'vat' => 'VAT',
  'tax' => 'TAX',
  'vat/tax' => 'VAT/TAX',
  'cost' => 'Cost',
  'subtotal' => 'Sub Total',
  'discount' => 'Discount',
  'delivery' => 'Delivery',
  'fee' => 'Fee',
  'title' => 'Title',
  'item' => 'Item',
  'image' => 'Image',
  'image_uploaded_successfully' => 'Images uploaded successfully',
  'image_deleted_successfully' => 'Images deleted successfully',
  'choose' => 'Choose',
  'file' => 'File',
  'file_manager' => 'file manager',
  'submit' => 'Submit',
  'close' => 'Close',
  'latitude' => 'Latitude',
  'longitude' => 'Longitude',
  'address' => 'Address',
  'invalid' => 'Invalid',
  'invalid_otp' => 'Invalid OTP!',
  'data' => 'Data',
  'edit' => 'Edit',
  'name' => 'Name',
  'save' => 'Save',
  'salary_based' => 'Salary based',
  'changes' => 'Changes',
  'change' => 'Change',
  'ratio' => 'Ratio',
  'any' => 'Any',
  'active' => 'Active',
  'disabled' => 'Disabled',
  'delete' => 'Delete',
  'update' => 'Update',
  'update_option_is_disable_for_demo' => 'Update option is disabled for demo!',
  'main' => 'Main',
  'sub' => 'Sub',
  'main_category' => 'Main Category',
  'percent' => 'Percent',
  'amount' => 'Amount',
  'amount_to_be_paid' => 'Amount to be paid',
  'set_menu' => 'Set Menu',
  'available' => 'Available',
  'time' => 'time',
  'starts' => 'starts',
  'ends' => 'ends',
  'short' => 'Short',
  'description' => 'Description',
  'back' => 'Back',
  'of' => 'of',
  'variations' => 'Variations',
  'reviewer' => 'Reviewer',
  'review' => 'Review',
  'review_submited_successfully' => 'Review submited successfully!',
  'coverage' => 'Coverage',
  'km' => 'KM',
  'conversation' => 'Conversation',
  'customers' => 'Customers',
  'reply' => 'Reply',
  'rating' => 'Rating',
  'id' => 'ID',
  'joined_at' => 'Joined at',
  'addresses' => 'Addresses',
  'limit' => 'Limit',
  'for' => 'for',
  'same' => 'same',
  'user' => 'User',
  'default' => 'Default',
  'first' => 'First',
  'start' => 'Start',
  'expire' => 'Expire',
  'min' => 'Min',
  'max' => 'Max',
  'purchase' => 'Purchase',
  'search' => 'Ex : Search here',
  'opening' => 'Opening',
  'closing' => 'Closing',
  'currency' => 'Currency',
  'on' => 'on',
  'off' => 'off',
  'order_canceled_successfully' => 'Order canceled successfully!',
  'order_placed_successfully' => 'Order placed successfully!',
  'order_updated_successfully' => 'Order updated successfully',
  'order_varification_code_not_matched' => 'Order varification code not matched',
  'order_varification_code_is_required' => 'Order varification code is required',
  'order_data_not_found' => 'Order data not found',
  'order_payment_details' => 'Payment details',
  'phone' => 'Phone',
  'phone_number_is_already_varified' => 'Phone number is already verified',
  'phone_number_varified_successfully' => 'Phone number is successfully verified!',
  'phone_number_and_otp_not_matched' => 'Phone number and otp not matched!',
  'photo' => 'photo',
  'value' => 'Value',
  'footer' => 'Footer',
  'text' => 'Text',
  'text_copied' => 'Text copied',
  'logo' => 'Logo',
  'smtp' => 'SMTP',
  'mailer' => 'Mailer',
  'host' => 'Host',
  'port' => 'Port',
  'pos' => 'POS',
  'pos_system' => 'Pos System',
  'progress' => 'progress',
  'driver' => 'Driver',
  'username' => 'Username',
  'encryption' => 'Encryption',
  'gateway' => 'Gateway',
  'gallery' => 'Gallery',
  'gst' => 'GST',
  'gst_can_not_be_empty' => 'GST can not be empty!',
  'gst_status_warning' => 'If GST is enable, GST number will be shown in invoice',
  'cash_on_delivery' => 'Cash on delivery',
  'digital' => 'digital',
  'inactive' => 'Inactive',
  'inactive_vendor_warning' => 'Inactive vendor! Please contact to admin.',
  'configure' => 'Configure',
  'sslcommerz' => 'SSLCOMMERZ',
  'razorpay' => 'Razor pay',
  'store' => 'Store',
  'razorkey' => 'Razor key',
  'razorsecret' => 'Razor secret',
  'paypal' => 'Paypal',
  'stripe' => 'Stripe',
  'client' => 'Client',
  'published' => 'Published',
  'key' => 'Key',
  'paypalsecret' => 'Paypal secret',
  'api' => 'API',
  'firebase' => 'Firebase',
  'server' => 'Server',
  'service_not_available_in_this_area' => 'Service not available in this area',
  'confirmation' => 'Confirmation',
  'message' => 'Message',
  'messaging_service_id' => 'Messaging Service Sid',
  'message_updated' => 'Push notification settings updated!',
  'assign' => 'Assign',
  'last' => 'Last',
  'identity' => 'Identity',
  'identification_number' => 'Identification number',
  'issuer' => 'Issuer',
  'number' => 'Number',
  'passport' => 'Passport',
  'driving' => 'Driving',
  'license' => 'License',
  'nid' => 'NID',
  'images' => 'Images',
  'attachment' => 'Attachment',
  'show' => 'Show',
  'range' => 'Range',
  'sold' => 'Sold',
  'software_version' => 'Software Version',
  'default_tax' => 'Default tax',
  'default_admin_commission' => 'Default admin commission',
  'this' => 'This',
  'third_party_apis' => 'Third Party APIs',
  'week' => 'Week',
  'weekly' => 'Weekly',
  'column' => 'Column',
  'vendor' => 'Store',
  'zone' => 'Zone',
  'zone_added_successfully' => 'Zone added successfully!',
  'zone_status_updated' => 'Zone status updated!',
  'zone_updated_successfully' => 'Zone updated successfully!',
  'zone_deleted_successfully' => 'Zone deleted successfully!',
  'zone_id_required' => 'Zone id is required!',
  'zone_wise' => 'Zone wise',
  'credentials' => 'Credentials',
  'item_item' => 'Item item',
  'item_name_required' => 'Item name is required!',
  'item_type_is_required' => 'Item type is required',
  'item_section' => 'Manage Item',
  'item_status_updated' => 'Item status updated!',
  'item_not_found' => 'Item not found',
  'charge' => 'Charge',
  'campaign' => 'Campaign',
  'campaigns' => 'Campaigns',
  'campaign_order' => 'Campaign Order',
  'campaign_status_updated' => 'Campaign status updated!',
  'campaign_deleted_successfully' => 'Campaign deleted successfully!',
  'capmaign_participation_updated' => 'Campaign participation updated!',
  'stores' => 'Stores',
  'store_added_to_campaign' => 'Store added to Campaign!',
  'store_imported_successfully' => ':count - Stores imported successfully!',
  'store_remove_from_campaign' => 'Store removed from Campaign!',
  'vendor_view' => 'Store View',
  'bank_info' => 'Bank Information',
  'bank_name' => 'Bank Name',
  'holder_name' => 'Holder Name',
  'account_no' => 'Account No',
  'withdrawn' => 'Withdrawn',
  'comission' => 'Comission',
  'disable' => 'Disable',
  'enable' => 'Enable',
  'enable_earning' => 'Enable earning',
  'disable_earning' => 'Disable earning',
  'panel' => 'Panel',
  'no' => 'No',
  'uncategorize' => 'uncategorize',
  'unknown_tab' => 'Unknown Tab!',
  'removed' => 'removed',
  'replace' => 'Replace',
  'role_updated_successfully' => 'Role updated successfully!',
  'role_deleted_successfully' => 'Role deleted successfully!',
  'sine_in' => 'Sign In',
  'take_away' => 'take away',
  'added_successfully' => ' Added successfully!',
  'updated_successfully' => ' updated successfully!',
  'successfully_removed' => 'Successfully removed!',
  'successfully_added' => 'Successfully added!',
  'successfully_updated' => 'Successfully updated!',
  'successfully_updated_to_changes_restart_app' => 'Successfully updated. To see the changes in app restart the app.',
  'status_updated' => 'Status updated!',
  'settings_updated' => 'Settings updated!',
  'discount_cleared' => 'Discount cleared!',
  'seller_payment_approved' => 'Seller Payment has been approved successfully',
  'seller_payment_denied' => 'Seller Payment request has been denied successfully',
  'self_delivery_system' => 'Self delivery system',
  'not_fund' => 'Not found!',
  'no_more_orders' => 'No more orders!',
  'please_assign_deliveryman_first' => 'Please assign delivery man first!',
  'add_your_paymen_ref_first' => 'Add your payment reference code first!',
  'push_notification_faild' => 'Push notification failed!',
  'you_can_not_cancel_a_completed_order' => 'You can not cancel a completed or already shipped order',
  'you_can_not_change_the_status_of_a_completed_order' => 'You can not change the status of a completed order',
  'you_can_not_cancel_a_order' => 'You can not cancel a order',
  'you_can_not_cancel_after_confirm' => 'You can not cancle after confirmed!',
  'you_can_not_delivered_delivery_order' => 'You can not delivered a order!',
  'you_can_not_edit_this_zone_please_add_a_new_zone_to_edit' => 'Sorry!You can not edit this zone in demo. Please add a new zone to edit',
  'you_can_not_edit_this_module_please_add_a_new_module_to_edit' => 'Sorry!You can not edit this module in demo. Please add a new module to edit',
  'you_can_not_edit_this_store_please_add_a_new_store_to_edit' => 'Sorry!You can not edit this store in demo. Please add a new store to edit',
  'you_can_not_delete_this_store_please_add_a_new_store_to_delete' => 'Sorry!You can not delete this store in demo. Please add a new store to delete',
  'you_can_not_delete_this_zone_please_add_a_new_zone_to_delete' => 'Sorry!You can not delete this zone in demo. Please add a new zone to delete',
  'veg_non_veg_disable_warning' => 'You can not disable both veg and non-veg',
  'you_are_successfully_joined_to_the_campaign' => 'You are successfully joined to the campaign',
  'you_are_successfully_removed_from_the_campaign' => 'You are successfully removed form the campaign',
  'you_have_uploaded_a_wrong_format_file' => 'You have uploaded a wrong format file, please upload the right file.',
  'you_want_to_refund_this_order' => 'Refundable amount - :amount. This amount will be added to customer`s wallet if customer wallet is enable otherwise please manage the amount transfer manually.',
  'you_want_to_temporarily_close_this_store' => 'You want to temporarily close this store?',
  'you_want_to_open_this_store' => 'You want to change the active status to open for this store?',
  'you_want_to_update_this_order_item' => 'You want to update this order item?',
  'you_want_to_remove_this_order_item' => 'You want to remove this order item?',
  'you_want_to_edit_this_order' => 'You want to edit this order?',
  'you_want_to_cancel_editing' => 'You want to cancel editing this order?',
  'you_want_to_submit_all_changes_for_this_order' => 'You want to submit all changes for this order?',
  'you_want_to_suspend_this_deliveryman' => 'Want to suspend this deliveryman ?',
  'you_want_to_unsuspend_this_deliveryman' => 'Want to unsuspend this deliveryman ?',
  'you_want_to_approve_this_application' => 'You want to approve this application?',
  'you_want_to_deny_this_application' => 'You want to deny this application?',
  'are_you_sure_want_to_refund' => 'Are you sure want to refund?',
  'are_you_sure' => 'Are you sure?',
  'you_can_not_refund_a_cod_order' => 'Sorry cash on delivery or unpaid order can not be refunded!',
  'already_confirmed' => 'This order is already confirmed!',
  'already_submitted' => 'Already submitted!',
  'cannot_change_status_after_delivered' => 'Sorry! You can not change the status after delivered a order.',
  'assign_delivery_mam_manually' => 'Assign delivery man manually',
  'total_served_order' => 'Total served orders',
  'orders_served' => 'orders served',
  'orders_delivered' => 'orders delivered',
  'order_confirmation_warning' => 'You are not allowed to confirm this order',
  'top_selling_items' => 'Top selling items',
  'top_content_image' => 'Top Content Image',
  'popular_stores' => 'Popular stores',
  'most_reviewed_items' => 'Most Reviewed Items',
  'new_business' => 'New Business',
  'new_campaign' => 'New Campaign',
  'new_category' => 'New Category',
  'new_coupon' => 'New coupon',
  'new_item' => 'New item',
  'new_stores' => 'New stores',
  'new_delivery_man' => 'New delivery man',
  'new_addon' => 'New addon',
  'new_banner' => 'New banner',
  'new_attribute' => 'New attribute',
  'new_notification' => 'New notification',
  'new_zone' => 'New Zone',
  'popular_items' => 'Popular items',
  'total_sell' => 'Total sell',
  'total_order_amount' => 'Total order amount',
  'total_uses' => 'Uses Count',
  'top_rated_items' => 'Most rated items',
  'top_deliveryman' => 'Top delivery man by order count',
  'top_customers' => 'Top customers',
  'top_stores' => 'Top stores by order delivered',
  'commission_earned' => 'Commission Earned',
  'admin_commission' => 'Admin commission',
  'refund_requested' => 'Refund Requested',
  'can_not_add_both_item_and_store_at_same_time' => 'Can not add both item and store at same time!',
  'already_in_wishlist' => 'Already in your wishlist!',
  'can_not_disable_both_take_away_and_delivery' => 'You can not disable both take away and delivery!',
  'if_sales_commission_disabled_system_default_will_be_applicable' => 'If sales commission is disabled here, the system default commission will be applied.',
  'if_sales_tax_disabled_system_default_will_be_applicable' => 'If sales tax is disabled here, the system default tax will be applied.',
  'dashboard_order_statistics' => 'Dashboard order statistics',
  'cooking' => 'Processing',
  'ready_for_delivery' => 'Ready for delivery',
  'item_on_the_way' => 'Item on the way',
  'yearly_statistics' => 'Yearly Statistics',
  'commission_given' => 'Commission given',
  'followup' => 'Followup your store\'s activities',
  'Want_to_delete_this_role' => 'Want to delete this role ?',
  'Want_to_delete_this_item' => 'Want to delete this item ?',
  'you_are_unassigned_from_a_order' => 'You are unassigned from a order',
  'you_are_assigned_to_a_order' => 'You are assigned to a order',
  'you_can_not_schedule_a_order_in_past' => 'You can not schedule a order in past time',
  'you_can_schedule_a_order_only_in_future_time' => 'You can schedule a order only in future time',
  'You_can_not_change_status_after_picked_up_by_delivery_man' => 'You can not change status after picked up by delivery man!',
  'you_can_not_deliver_a_delivery_order' => 'You can not deliver a order which is not self pick up!',
  'you_need_to_order_at_least' => 'You need to order at least :amount.',
  'you_want_to_change_this_store_status' => 'You want to change status for this store status?',
  'you_want_to_block_this_customer' => 'You want to block this customer ?',
  'you_want_to_unblock_this_customer' => 'You want to unblock this customer ?',
  'you_want_to_hide_this_review_for_customer' => 'You want to hide this review to customers?',
  'you_want_to_show_this_review_for_customer' => 'You want to visible this review to customers?',
  'your_account_is_blocked' => 'Sorry! Your account is blocked. Please contact to our customer care.',
  'your_account_has_been_blocked' => 'Your account has been blocked! Please contact to our customer care.',
  'your_account_has_been_suspended' => 'Sorry! Your account has been suspended.',
  'your_first_name' => 'Your first name',
  'your_last_name' => 'Your last name',
  'want_to_disable_earnings' => 'Want to change the delivery man type to salary based?',
  'want_to_enable_earnings' => 'Want to change the delivery man type to freelancer?',
  'want_to_update_admin_password' => 'Want to update admin password ?',
  'want_to_update_password' => 'Want to update user password ?',
  'proceed' => 'Proceed',
  'denied' => 'Denied',
  'sms' => 'SMS',
  'twilio_sms' => 'Twilio',
  'sid' => 'SID',
  'token' => 'Token',
  'token_varified' => 'Token verified!',
  'token_not_found' => 'Token is not found!',
  'otp_template' => 'OTP template',
  'nexmo_sms' => 'Nexmo',
  'api_key' => 'API key',
  'iframe_id' => 'Iframe id',
  'integration_id' => 'Integration id',
  'HMAC' => 'HMAC',
  'EGP_currency_is_required' => 'EGP currency is required',
  'api_secret' => 'API Secret',
  '2factor_sms' => '2factor',
  'msg91_sms' => 'Msg91',
  'template_id' => 'Template ID',
  'authkey' => 'Auth Key',
  'module' => 'System Module',
  'management' => 'Management',
  'header_title_1' => 'Header Title 1',
  'header_title_2' => 'Header Title 2',
  'header_title_3' => 'Header Title 3',
  'about_title' => 'About Title',
  'why_choose_us' => 'Why Choose Us?',
  'why_choose_us_title' => 'Why Choose Us Title',
  'trusted_by_customer' => 'Trusted by Customer',
  'trusted_by_store' => 'Store Owner',
  'contact_us' => 'Contact Us',
  'footer_article' => 'Footer Article',
  'home' => 'Home',
  'quick_links' => 'Quick Links',
  'browse_web' => 'Browse Web',
  'yes' => 'Yes',
  'saturday' => 'Saturday',
  'sunday' => 'Sunday',
  'monday' => 'Monday',
  'tuesday' => 'Tuesday',
  'wednesday' => 'Wednesday',
  'thirsday' => 'Thursday',
  'friday' => 'Friday',
  'store_cancellation_toggle' => 'Store can cancel a order',
  'deliveryman_cancellation_toggle' => 'Deliveryman can cancel a order',
  'time_zone' => 'Time Zone',
  'time_format' => 'Time Format',
  '12_hour' => '12H',
  '24_hour' => '24H',
  'language' => 'Language',
  'show_earning_for_each_order' => 'Show earning for each order (Deliveryman app)',
  'order_canceled_confirmation' => 'You want to cancel this order?',
  'first_name_is_required' => 'First name is required!',
  'select_a_zone' => 'Please select a zone!',
  'select_a_module' => 'Please select a module!',
  'select_dm_type' => 'Please select deliveryman type!',
  'credential_updated' => ':service credential updated',
  'credentials_setup' => 'Credentials SetUp',
  'callback_uri' => 'Callback URI',
  'copy_uri' => 'Copy URI',
  'store_client_id' => 'Store Client ID',
  'store_client_secret' => 'Store Client Secret',
  'google_api_setup_instructions' => 'Google API Set up Instructions',
  'go_to_the_credentials_page' => 'Go to the Credentials page',
  'click' => 'Click',
  'create_credentials' => 'Create credentials',
  'auth_client_id' => 'OAuth client ID',
  'select_the' => 'Select the',
  'web_application' => 'Web application',
  'name_your_auth_client' => 'Name your OAuth 2.0 client',
  'add_uri' => 'ADD URI',
  'authorized_redirect_uris' => 'Authorized redirect URIs',
  'provide_the' => 'provide the',
  'from_below_and_click' => 'from below and click',
  'create' => 'create',
  'client_id' => 'Client ID',
  'and' => 'and',
  'client_secret' => 'Client Secret',
  'past_in_the_input_field_below_and' => 'paste in the input field below and',
  'facebook_api_set_instruction' => 'Facebook API Set up Instructions',
  'goto_the_facebook_developer_page' => 'Go to the facebook developer page',
  'click_here' => 'Click here',
  'get_started' => 'Get Started',
  'from_navbar' => 'from Navbar',
  'from_register_tab_press' => 'From Register tab press',
  'continue' => 'Continue',
  'if_needed' => 'If needed',
  'provide_primary_email_and_press' => 'Provide Primary Email and press',
  'confirm_email' => 'Confirm Email',
  'in_about_section_select' => 'In about section select',
  'other' => 'Other',
  'and_press' => 'and press',
  'complete_registration' => 'Complete Registration',
  'create_app' => 'Create App',
  'select_an_app_type_and_press' => 'Select an app type and press',
  'next' => 'Next',
  'complete_the_details_form_and_press' => 'Complete the add details form and press',
  'facebook_login' => 'Facebook Login',
  'press' => 'press',
  'set_up' => 'Set Up',
  'web' => 'Web',
  'site_url' => 'Site URL',
  'base_url_of_the_site' => 'Base URL of the site ex',
  'now_go_to' => 'Now go to',
  'setting' => 'Setting',
  'left_sidebar' => 'left sidebar',
  'make_sure_to_check' => 'Make sure to check',
  'client_auth_login' => 'Client OAuth Login',
  'must_on' => 'must on',
  'valid_auth_redirect_uris' => 'Valid OAuth Redirect URIs',
  'save_changes' => 'Save Changes',
  'from_left_sidebar' => 'from left sidebar',
  'fill_the_form_and_press' => 'Fill the form and press',
  'now_copy' => 'Now, copy',
  'twitter_api_set_up_instructions' => 'Twitter API Set up Instructions',
  'instruction_will_be_available_very_soon' => 'Instruction will be available very soon',
  'facebook' => 'Facebook',
  'google' => 'Google',
  'Customer_not_found_or_Account_has_been_suspended' => 'Customer not found or Account has been suspended',
  'email_does_not_match' => 'Email does not match',
  'user_not_found' => 'User not found',
  'email_already_exists' => 'Email already exists!',
  'want_to_login_your_admin_account' => 'Want to login your admin account?',
  'admin_login' => 'Admin Login',
  'your_email' => 'Your Email',
  'remember_me' => 'Remember me',
  'store_employee_login_form' => 'Store Employee Login Form',
  'add_new_store' => 'Add new store',
  'edit_bank_info' => 'Edit Bank Information',
  'please_only_input_png_or_jpg_type_file' => 'Please only input png or jpg type file',
  'file_size_too_big' => 'File size too big!',
  'credentials_does_not_match' => 'Credentials does not match.',
  'product_unavailable_warning' => 'One or more items in your cart is not available. Please clear your cart and try again.',
  'product_out_of_stock_warning' => ':item out of stock!',
  'refund_request_placed_successfully' => 'Refund request placed successfully!',
  'you_can_not_request_for_refund_after_delivery' => 'You can not request for refund before delivery!',
  'invalid_password_warning' => 'Your password is invalid. Please try again.',
  'veg_non_veg' => 'Veg/non-veg toggle',
  'store_self_registration' => 'Store self registration',
  'dm_self_registration' => 'Deliveryman self registration',
  '404_warning_message' => 'Sorry, the page you\'re looking for cannot be found',
  '500_warning_message' => 'The server encountered an internal error or misconfiguration and was unable to complete your request.',
  'reload_page' => 'Reload page',
  'error' => 'Error',
  'your_application_is_not_approved_yet' => 'Your application is not approved yet!',
  'add_new_addon' => 'Add new addon',
  'nav_menu' => 'Nav menu',
  'you_want_to_update_user_info' => 'Want to update user Information ?',
  'schedule_order_disabled_warning' => 'Schedule order is disabled by admin',
  'vendor_pasword_updated_successfully' => 'Vendor password updated successfully!',
  'languages' => 'Languages',
  'reCaptcha' => 'ReCaptcha',
  'reCaptcha Setup' => 'ReCaptcha Setup',
  'Credentials SetUp' => 'Credentials SetUp',
  'Site Key' => 'Site Key',
  'Secret Key' => 'Secret Key',
  'reCaptcha credential Set up Instructions' => 'ReCaptcha credential Set up Instructions',
  'Go to the Credentials page' => 'Go to the Credentials page',
  'Click' => 'Click',
  'Add a ' => 'Add a ',
  'label' => 'Label',
  '(Ex: Test Label)' => '(Ex: Test Label)',
  'Select reCAPTCHA v2 as ' => 'Select reCAPTCHA v2 as ',
  'reCAPTCHA Type' => 'ReCAPTCHA Type',
  'Sub type: I\'m not a robot Checkbox' => 'Sub type: I m not a robot Checkbox',
  'Add' => 'Add',
  'domain' => 'Domain',
  '(For ex: demo.6amtech.com)' => '(For ex: demo.6amtech.com)',
  'Check in ' => 'Check in ',
  'Accept the reCAPTCHA Terms of Service' => 'Accept the reCAPTCHA Terms of Service',
  'Press' => 'Press',
  'Submit' => 'Submit',
  'Copy' => 'Copy',
  'paste in the input filed below and' => 'Paste in the input filed below and',
  'Close' => 'Close',
  'Please check the recaptcha' => 'Please check the recaptcha',
  'Enter recaptcha value' => 'Enter recaptcha value',
  'ReCAPTCHA Failed' => 'ReCAPTCHA Failed',
  'Users Overview' => 'Users Overview',
  'This Month' => 'This Month',
  'Overall' => 'Overall',
  'Business Overview' => 'Business Overview',
  'Customer' => 'Customer',
  'Delivery Man' => 'Delivery Man',
  'Overall Statistics' => 'Overall Statistics',
  'Today\'s Statistics' => 'Today\'s Statistics',
  'Name is required!' => 'Name is required!',
  'Store is required when banner type is store wise' => 'Store is required when banner type is store wise',
  'Item is required when banner type is item wise' => 'Item is required when banner type is item wise',
  'Role name is required!' => 'Role name is required!',
  'Role name already taken!' => 'Role name already taken!',
  'Last name is required!' => 'Last name is required!',
  'No Data found' => 'No Data found',
  'Bank Info View' => 'Bank Info View',
  'complete' => 'Complete',
  'Please select atleast one module' => 'Please select atleast one system module',
  'Item Bulk Import' => 'Item Bulk Import',
  'Import Item`s File' => 'Import Item`s File',
  'Download Format' => 'Download Format',
  'Add new category' => 'Add new category',
  'Update category' => 'Update category',
  'Total Price' => 'Total Price',
  'you want to sent notification to' => 'You want to sent notification to ',
  'Variant' => 'Variant',
  'Variant Price' => 'Variant Price',
  'quantity' => 'Quantity',
  'Category Bulk Export' => 'Category Bulk Export',
  'Import Categories File' => 'Import Categories File',
  'qty' => 'QTY',
  'You have new order, Check Please.' => 'You have new order, Check Please.',
  'Ok, let me check' => 'Ok, let me check',
  'store deleted!' => 'Store deleted!',
  'Item deleted!' => 'Item deleted!',
  'reset' => 'Reset',
  'Unauthorized' => 'Unauthorized.',
  'Export Stores' => 'Export Stores',
  'star' => 'star',
  'Order List' => 'Order List',
  'This Month\'s Statistics' => 'This Month\'s Statistics',
  'Daily time schedule' => 'Daily time schedule',
  'Create Schedule For ' => 'Create Schedule For ',
  'Start time' => 'Start time',
  'End time' => 'End time',
  'Store settings updated!' => 'Store settings updated!',
  'Schedule added successfully' => 'Schedule added successfully',
  'Schedule removed successfully' => 'Schedule removed successfully',
  'Offday' => 'Offday',
  'You want to remove this schedule' => 'You want to remove this schedule?',
  'End time must be after the start time' => 'End time must be after the start time',
  'Schedule not found' => 'Schedule not found or already deleted!',
  'schedule_overlapping_warning' => 'This schedule is overlaping another shcedule!',
  'Update addon' => 'Update addon',
  'Update campaign' => 'Update campaign',
  'description_length_warning' => 'Description must not be greater than 1000 characters',
  'Add new sub category' => 'Add new sub category',
  'Name and description in english is required' => 'Name and description in english is required',
  'Schedule order slot duration' => 'Schedule order slot duration',
  'Digit after decimal point' => 'Digit after decimal point',
  'minute' => 'Minute',
  'Update Attribute' => 'Update Attribute',
  'add_language_warrning' => 'If you add a new language you have to add it in app also',
  'paytm' => 'Paytm',
  'paytm_merchant_key' => 'Paytm merchant key',
  'paytm_merchant_mid' => 'Paytm merchant mid',
  'paytm_merchant_website' => 'Paytm merchant website',
  'liqpay' => 'Liqpay',
  'privateKey' => 'PrivateKey',
  'base_url_by_region' => 'Base url by region',
  'profile_id' => 'Profile id',
  'server_key' => 'Server key',
  'paytabs' => 'Paytabs',
  'bkash' => 'Bkash',
  'Cash on delivery order not available at this time' => 'Cash on delivery order not available at this time',
  'category_added_successfully' => 'Category added successfully!',
  'Fav Icon' => 'Fav Icon',
  'store_count' => 'Store count',
  'module_type' => 'System module type',
  'module_added_successfully' => 'System module added successfully!',
  'module_updated_successfully' => 'System module updated successfully!',
  'module_deleted_successfully' => 'System module deleted successfully!',
  'update_module' => 'Update system module',
  'module_status_updated' => 'System module status updated!',
  'minutes' => 'minutes',
  'hours' => 'hours',
  'days' => 'days',
  'unit' => 'Unit',
  'units' => 'Units',
  'unit_type' => 'Unit type',
  'new_unit' => 'New unit',
  'unit_added_successfully' => 'Unit added successfully',
  'unit_updated_successfully' => 'Unit updated successfully',
  'unit_deleted_successfully' => 'Unit deleted successfully',
  'Update Unit' => 'Update Unit',
  'stock' => 'stock',
  'total_stock' => 'Total stock',
  'thumbnail' => 'Thumbnail',
  'remove' => 'Remove',
  'minimum_processing_time' => 'Minimum processing time (minutes)',
  'minimum_processing_time_warning' => 'Delivery slot will be start after order place time plus this time',
  'module_id_required' => 'System module id is required',
  'module_change_warning' => 'NB: You will not be able to change system module in future',
  'module_type_change_warning' => 'NB: You will not be able to change system module type in future',
  'parcel' => 'Parcel',
  'parcel_category' => 'Parcel Category',
  'parcel_category_not_found' => 'Parcel Category Not Found',
  'update_parcel_category' => 'Update parcel category',
  'Add_new_parcel_category' => 'Add new parcel category',
  'parcel_category_status_updated' => 'Parcel category status updated',
  'parcel_category_added_successfully' => 'Parcel category added successfully',
  'parcel_category_updated_successfully' => 'Parcel category updated successfully',
  'parcel_category_deleted_successfully' => 'Parcel category deleted successfully',
  'parcel_settings' => 'Parcel Settings',
  'parcel_settings_updated' => 'Parcel settings updated',
  'receiver' => 'Receiver',
  'current_page_only' => 'This will export the current page only',
  'sender' => 'Sender',
  'distance' => 'Distance',
  'deliveryman_commission' => 'Deliveryman commission',
  'prescription' => 'Prescription',
  'dear' => 'Dear',
  'thanks' => 'Thank you for joinning with',
  'congratulations' => 'Congratulations',
  'account_created' => 'Account Created',
  'buy_now' => 'Buy Now',
  'feedback' => 'If you require any assistance or have feedback or suggestions about our site, you can email us at',
  'copyright' => 'All copy right reserved',
  'welcome_text' => 'We are thrilled to have you with us. Explore more items and stores and enjoy',
  'no_data_found' => 'No data found',
  'feature_section_image' => 'Feature section image',
  'feature_section_title' => 'Feature Section Title',
  'mobile_app_section_image' => 'Mobile section image',
  'change_header_bg' => 'Header background',
  'change_footer_bg' => 'Footer background',
  'landing_page_bg' => 'Landing page background',
  'background_updated' => 'Background color updated',
  'background_color' => 'Background Change',
  'reviewer_designation' => 'Reviewer designation',
  'reviewer_name' => 'Reviewer name',
  'thanks_for_the_order' => 'Thanks for the order',
  'Your_order_ID' => 'Your order ID',
  'Ordered_Items' => 'Ordered Items',
  'to_view_your_order_sign_into_your' => 'To view your order sign into your :company_name',
  'to_view_your_order_sign_into_your account_and_please_click_below_button' => 'To view your order sign into your :company_name account and please click below button',
  'Unit_price' => 'Unit price',
  'payment_details' => 'Payment details',
  'website' => 'Website',
  'store_info' => 'Store Information',
  'web_app_landing_page_settings' => 'Web app landing page settings',
  'web_app' => 'Web app',
  'web_app_settings_updated' => 'Web app landing page settings updated',
  'icon' => 'Icon',
  'select_theme' => 'Select theme',
  'Social Media' => 'Social Media',
  'You have new order, Check Please' => 'You have new order, Check Please.',
  'order_place' => 'Order place',
  'test_your_email_integration' => 'Test your email integration',
  'inActive' => 'In Active',
  'smtp_mail_config' => 'SMTP mail config',
  'a_test_mail_will_be_sent_to_your_email' => 'A test mail will be sent to your email',
  'email_configuration_error' => 'Email configuration error',
  'email_configured_perfectly' => 'Email configured perfectly',
  'email_status_is_not_active' => 'Emailstatus is not active',
  'invalid_email_address' => 'Invalid email address',
  'send_mail' => 'Send mail',
  'Active' => 'Active',
  'Inactive' => 'Inactive',
  'Are you sure?' => 'Are you sure ',
  'Yes' => 'Yes',
  'email_configured_perfectly!' => 'Email configured perfectly!',
  'clean_database' => 'Clean Database',
  'DB_clean' => 'DB clean',
  'Clean database' => 'Clean database',
  'This_page_contains_sensitive_information.Make_sure_before_changing.' => 'This page contains sensitive information.Make sure before changing.',
  'Clear' => 'Clear',
  'Sensitive_data! Make_sure_before_changing.' => 'Sensitive data! Make sure before changing.',
  'Cancelled' => 'Cancelled',
  'models' => 'Models',
  'preparingInRestaurants' => 'PreparingInRestaurants',
  'bukl_export' => 'Bukl export',
  'environment_setup' => 'Environment setup',
  'This_page_is_having_sensitive_data.Make_sure_before_changing.' => 'This page is having sensitive data.Make sure before changing.',
  'APP_NAME' => 'APP NAME',
  'APP_DEBUG' => 'APP DEBUG',
  'True' => 'True',
  'False' => 'False',
  'APP_MODE' => 'APP MODE',
  'Live' => 'Live',
  'Dev' => 'Dev',
  'APP_URL' => 'APP URL',
  'DB_CONNECTION' => 'DB CONNECTION',
  'DB_HOST' => 'DB HOST',
  'DB_PORT' => 'DB PORT',
  'DB_DATABASE' => 'DB DATABASE',
  'DB_USERNAME' => 'DB USERNAME',
  'DB_PASSWORD' => 'DB PASSWORD',
  'BUYER_USERNAME' => 'BUYER USERNAME',
  'PURCHASE_CODE' => 'PURCHASE CODE',
  'Environment variables updated successfully!' => 'Environment variables updated successfully!',
  'Floor' => 'Floor',
  'Road' => 'Road',
  'House' => 'House',
  'This month' => 'This month',
  'selected' => 'Selected',
  'stock_report' => 'Stock report',
  'Current stock' => 'Current stock',
  'new_food' => 'New food',
  'All Zones' => 'All Zones',
  'module_all_zone_hint' => 'If "all zone" selected, store will be availabe from all zone otherwise only the zone stores belongs to.',
  'Store can serve in' => 'Store can serve in',
  'One Zone' => 'One Zone',
  'Delivery address' => 'Delivery address',
  'add_new_customer' => 'Add new customer',
  'Ex_:<EMAIL>' => 'Ex : <EMAIL>',
  'The Geolocation service failed' => 'The Geolocation service failed',
  'Your browser doesn\'t support geolocation' => 'Your browser doesn t support geolocation',
  'Sorry, product out of stock' => 'Sorry  product out of stock',
  'Sorry, you can not add multiple stores data in same cart' => 'Sorry  you can not add multiple stores data in same cart',
  'customer_added_successfully' => 'Customer added successfully',
  'order ' => 'Order ',
  'contact_person_name' => 'Contact person name',
  'contact_person_number' => 'Contact person number',
  'with_country_code' => 'With country code',
  'you_want_to_delete' => 'You want to delete this file?',
  'You want to remove this store' => 'You want to remove this store',
  'Redirecting_to_the_payment_page' => 'Redirecting to the payment page',
  'items_on_the_way' => 'Items on the way',
  'role' => 'Role',
  'DESC' => 'DESC',
  'item_price' => 'Item price',
  'addon_cost' => 'Addon cost',
  'THANK YOU' => 'THANK YOU',
  'show_hide_food_menu' => 'Show hide food menu',
  'Create Schedule' => 'Create Schedule',
  'Campaign List' => 'Campaign List',
  'edit_coupon' => 'Edit coupon',
  'role_name_example' => 'Ex : Manager',
  'Too Many Requests' => 'Too Many Requests',
  'logout_warning_message' => 'Do you want to logout?',
  'business_setup' => 'Business setup',
  'all_image_delete_warning' => 'You cannot delete all images!',
  'item_image_removed_successfully' => 'Item image removed successfully!',
  'Page Expired' => 'Page Expired',
  'customer_name' => 'Customer Name',
  'store is required when banner type is store wise' => 'Store is required when banner type is store wise',
  'Order Details' => 'Order Details',
  'Edit item' => 'Edit item',
  'table_unchecked_warning' => 'Uncheck ":table" first',
  'Paid by' => 'Paid by',
  'password_mismatch' => 'Password did,t match!',
  'want_to_remove_store' => 'Want to remove this store ?',
  'user_account_delete_warning' => 'You can\'t delete your account without complete your orders.',
  'user_account_wallet_delete_warning' => 'You can\'t delete your account without clear your dues.',
  'Item Preview' => 'Item Preview',
  'add_fund' => 'Add Fund',
  'you_want_to_add_fund' => 'You want to add fund',
  'to_wallet' => 'To wallet',
  'fund_added_successfully' => 'Fund added successfully',
  'customer_settings' => 'Customer settings',
  'customer_wallet' => 'Customer Wallet',
  'customer_loyalty_point' => 'Customer Loyalty Point',
  'c_referrer_earning' => 'C referrer earning',
  'refund_to_wallet' => 'Refund to wallet',
  'refund_to_wallet_hint' => 'When a order will refund, order amount will add to customer wallet',
  'point_to_currency_exchange_rate' => '1 :currency equal to how much loyalty points?',
  'item_purchase_point' => 'Percentage of loyalty point on order amount',
  'item_purchase_point_hint' => 'On every purchase this percent of amount will be added as loyalty point on his account',
  'minimum_point_to_transfer' => 'Minimum loyalty points to transfer into wallet',
  'customer_referrer' => 'Customer referrer',
  'referrer_to_currency' => 'One referrer equal to how much :currency?',
  'stock_limit_list' => 'Stock limit list',
  'dm_tips_status' => 'Dm tips status',
  'customer_referrer_earning' => 'Customer referrer earning',
  'customer_settings_updated_successfully' => 'Customer settings updated successfully',
  'loyalty_point_balance' => 'Loyalty point balance',
  'debit' => 'Debit',
  'credit' => 'Credit',
  'transaction_type' => 'Transaction type',
  'point_to_wallet' => 'Point to wallet',
  'select_customer' => 'Select customer',
  'order_status_updated' => 'Order status updated',
  'referrer' => 'Referrer',
  'loyalty_point' => 'Loyalty point',
  'add_fund_by_admin' => 'Add fund by admin',
  'Rewared by company admin' => 'Rewared by company admin',
  'loyalty_point_to_wallet' => 'Loyalty point to wallet',
  'earned_from_referrer' => 'Earned from referrer',
  'add_fund_to_wallet' => 'Add fund to wallet',
  'delivery_man_tips' => 'DM Tips',
  'free_delivery_toggle_hint' => 'Enable self delivery system if free delivery is disabled',
  'store_logo' => 'Store logo',
  'store settings updated!' => 'Store settings updated!',
  'Delivery Man Not Found' => 'Delivery Man Not Found',
  'export_stores' => 'Export Stores',
  'dispatch' => 'Dispatch',
  'food' => 'Food',
  'link' => 'Link',
  'mail_config' => 'Mail config',
  'Add Parcel' => 'Add Parcel',
  'homeDelivery' => 'HomeDelivery',
  'home Delivery' => 'Home Delivery',
  'ex' => 'Ex',
  'by' => 'By',
  'resturant_information' => 'Resturant information',
  'store_information' => 'Store information',
  'account' => 'Account',
  'ex_search_module' => 'Ex : Search Module',
  'ex_:_search_module' => 'Ex : Search Module',
  'ex_:_Search_module' => 'Ex : Search Module',
  'ex_:_Search_Module' => 'Ex : Search Module',
  'STEP 1' => 'STEP 1',
  'Download Excel File' => 'Download Excel File',
  'STEP 2' => 'STEP 2',
  'Match Spread sheet data according to instruction' => 'Match Spread sheet data according to instruction',
  'Validate data and complete import' => 'Validate data and complete import',
  'STEP 3' => 'STEP 3',
  'Select Data Type' => 'Select Data Type',
  'Select Data Range and Export' => 'Select Data Range and Export',
  'clear' => 'Clear',
  'add_new_category' => 'Add new category',
  'SL' => 'SL',
  'ex_search_name' => 'Ex : Search Name',
  'ex_:_search_name' => 'Ex : Search Name',
  'ex_:_search_attribute_name' => 'Ex : search attribute name',
  'ex_:_attribute_name' => 'Ex : attribute name',
  'ex_:_categories' => 'Ex : categories',
  'ex_:_addons_name' => 'Ex : addons name',
  'Select Data Range by Date or ID and Export' => 'Select Data Range by Date or ID and Export',
  'ex_:_sub_categories' => 'Ex : sub categories',
  'item_details' => 'Item details',
  'time_schedule' => 'Time schedule',
  'ex_:_search_item_name' => 'Ex : Search Item Name',
  'download_spreadsheet_template' => 'Download spreadsheet template',
  'template_with_existing_data' => 'Template with existing data',
  'template_without_data' => 'Template without data',
  'import_categories_file' => 'Import categories file',
  'excellent' => 'Excellent',
  'good' => 'Good',
  'average' => 'Average',
  'below_average' => 'Below average',
  'poor' => 'Poor',
  'excellent_' => 'Excellent ',
  'short_description' => 'Short description',
  'general_information' => 'General information',
  'login_information' => 'Login information',
  'ex_:_search_delivery_man' => 'Ex : Search Delivery Man',
  'show_data' => 'Show data',
  'download_options' => 'Download options',
  'ex_:_search_by_' => 'Ex : search by ',
  'wallet_settings' => 'Wallet settings',
  'ex_:_search_' => 'Ex : search ',
  'ex_:_search_name_,_email_or_phone' => 'Ex : search name, email or phone',
  'stock_report_table' => 'Stock report table',
  'ex_:_search_email' => 'Ex : search email',
  'ex_:_email' => 'Ex : email',
  'order_list' => 'Order list',
  'ex_:_search_ID' => 'Ex : search ID',
  'total_amount' => 'Total amount',
  'genaral_information' => 'Genaral information',
  'business_information' => 'Business information',
  'business_setting' => 'Business setting',
  'save_information' => 'Save information',
  'dm_tips_model_hint' => 'Dm tips model hint',
  'social_media' => 'Social Media',
  'mail_configuration_status' => 'Mail configuration status',
  'change image' => 'Change image',
  'note_:' => 'Note :',
  'ex_:_search_role_name' => 'Ex : search role name',
  'add_new_employee' => 'Add new employee',
  'account_information' => 'Account information',
  'ex_:_search_sub_categories' => 'Ex : search sub categories',
  'ex_:_search' => 'Ex : search',
  'home_delivery' => 'Home delivery',
  'payment_method' => 'Payment Method',
  'product_section' => 'Product section',
  'paid_amount_:' => 'Paid amount :',
  'paid_by' => 'Paid by',
  'billing_section' => 'Billing section',
  'delivery_options' => 'Delivery options',
  'order_details' => 'Order details',
  'payment_status' => 'Payment status',
  'order_setup' => 'Order setup',
  'customer_information' => 'Customer information',
  'order_already_assign_to_this_deliveryman' => 'Order already assign to this deliveryman',
  'unassigned_orders' => 'Unassigned orders',
  'ongoing_orders' => 'Ongoing orders',
  '* This discount is applied on all the foods in your restaurant' => '* This discount is applied on all the foods in your restaurant',
  'purchase_conditions' => 'Purchase conditions',
  '' => '',
  'no_discount_created_yet' => 'No discount created yet',
  'opening_time' => 'Opening time',
  'closing_time' => 'Closing time',
  'order_transactions' => 'Order transactions',
  'withdraw_transactions' => 'Withdraw transactions',
  'cash_transaction' => 'Cash transaction',
  'ex_:_search_order_id' => 'Ex : Search Order ID',
  'item_update' => 'Item update',
  'export_items' => 'Export items',
  'items_bulk_import' => 'Items bulk import',
  'import' => 'Import',
  'category_list' => 'Category list',
  'update_bank_info' => 'Update Bank Information',
  'add_bank_account' => 'Add bank account',
  'available_time_schedule' => 'Available time schedule',
  'account_info' => 'Account Information',
  'campaign_list' => 'Campaign list',
  'delivery_man_preview' => 'Delivery Man Preview',
  'my_store_info' => 'My Store Information',
  'employee_list' => 'Employee list',
  'search_role' => 'Ex : Search by Role Name',
  'edit_role' => 'Edit role',
  'store_wallet' => 'Store wallet',
  'proceed_for_processing' => 'Proceed for processing',
  'make_delivered' => 'Make delivered',
  'order_status' => 'Order status',
  'ex_:_search_here' => 'Ex : Search here...',
  'all_parcel_module' => 'All parcel module',
  'new_sub_category' => 'New sub category',
  'FCM Project ID' => 'FCM Project ID',
  'auth_domain' => 'Auth domain',
  'storage_bucket' => 'Storage bucket',
  'messaging_sender_id' => 'Messaging sender id',
  'app_id' => 'App id',
  'measurement_id' => 'Measurement id',
  'Chat' => 'Chat',
  'Conversations' => 'Conversations',
  'conversations' => 'Conversations',
  'delivery_man' => 'Delivery man',
  'ex_:_new_attribute' => 'Ex : New Attribute Name...',
  'updated_attribute' => 'Updated Attribute...',
  'search_unit' => 'Ex : Search Unit Name',
  'unit_name' => 'Ex : Unit Name',
  'ex_:_order_id' => 'Ex : order id',
  'discount_amount' => 'Discount Amount',
  'ex_:_search_store_name' => 'Ex : search store name',
  'collect_cash' => 'Collect Cash',
  'ex_:_search_sender_name' => 'Ex : Search Sender Name',
  'no_products_on_pos_search' => 'To get the required search result First select store to search category wise products or search manually to find products under that store.',
  'payment_amount' => 'Payment amount',
  'ex_:confirm_password' => 'Ex :confirm password',
  'ex_:_' => 'Ex : ',
  'ex_:' => 'Ex :',
  'deliveryman_list' => 'Deliveryman List',
  'contact_info' => 'Contact info',
  'total_orders' => 'Total orders',
  'active_status' => 'Active status',
  'currently_assigned_orders' => 'Currently assigned orders',
  'delivery_fee' => 'Delivery fee',
  'contact_information' => 'Contact information',
  'active/inactive' => 'Active/inactive',
  'active/Inactive' => 'Active/Inactive',
  100 => '100',
  'ex_100' => 'Ex : 100',
  'ex_cash' => 'Ex : Cash',
  'ex_collect_cash' => 'Ex : Collect Cash',
  'Ex_:_Footer_Text' => 'Ex : Footer Text',
  'maintenance_txt' => '*By turning the ‘Maintenance Mode’ ON, all your apps and customer website will be disabled temporarily. Only the Admin Panel, Admin Landing Page & Store Panel will be functional. ',
  'edit_info' => 'Edit info',
  'total_stores' => 'Total stores',
  'active_stores' => 'Active stores',
  'inactive_stores' => 'Inactive stores',
  'newly_joined_stores' => 'Newly joined stores',
  'total_transactions' => 'Total transactions',
  'total_store_withdraws' => 'Total store withdraws',
  'ex_:_Search_Store' => 'Ex : Search Store',
  'owner_information' => 'Owner information',
  'discounts' => 'Discounts',
  'manage_item_setup' => 'Item Management',
  'Show_Reviews_In_Store_Panel' => 'Show Reviews In Store Panel',
  'order_option' => 'Order option',
  'self_delivery' => 'Self delivery',
  'scheduled_order_hint' => ' If this status is turned on  the customer is able to place a scheduled order for this restaurant.',
  'self_delivery_hint' => 'When this option is enabled  restaurants need to deliver orders by themselves or by their own delivery man. Restaurants will also get an option for adding their own delivery man from the restaurant panel.',
  'home_delivery_hint' => 'If this option is active  customers can place orders for home delivery.',
  'take_away_hint' => 'By disabling this option  customers can t place self-pickup / take-away orders.',
  'pos_system_hint' => 'If this option is turned on  the restaurant panel will get the Point of Sale (POS) option.',
  'include_pos_in_store_panel' => 'POS in Store Panel',
  'basic_campaigns' => 'Basic Campaigns',
  'bannerd' => 'Bannerd',
  'scheduled_orders' => 'Scheduled Orders',
  'accepted_orders' => 'Accepted Orders',
  'processing_orders' => 'Processing Orders',
  'delivered_orders' => 'Delivered Orders',
  'canceled_orders' => 'Canceled Orders',
  'payment_failed_orders' => 'Payment Failed Orders',
  'refunded_orders' => 'Refunded Orders',
  'subscribed_emails' => 'Subscribed Emails',
  'customer_chat' => 'Customer Chat',
  'sms_system_module' => 'Sms System Module',
  'notification_settings' => 'Notification Settings',
  'limited_stock_item' => 'Limited Stock Item',
  'limited_stock' => 'Limited stock',
  'module_management' => 'Module management',
  'system_module_setup' => 'System Module Setup',
  'zone_setup' => 'Zone Setup',
  'no_products_on_store_pos_search' => 'dfaklsdmf asdfalsdfskda fk asldfakld flasdfklads flasdfklsdfkladflad ',
  'update_delivery_address' => 'Update delivery address',
  'clear_cart' => 'Clear Cart',
  'place_order' => 'Place Order',
  'change_amount_:' => 'Change Amount :',
  'confirmed_orders' => 'Confirmed Orders',
  'cooking_orders' => 'Cooking Orders',
  'items_list' => 'Items List',
  'campaigns_list' => 'Campaigns List',
  'my_wallet' => 'My Wallet',
  'chat' => 'Chat',
  'order_date' => 'Order date',
  'reviewer_table_list' => 'Reviewer table list',
  'Select Data Range by Date and Export' => 'Select Data Range by Date and Export',
  'category_id' => 'Category id',
  'category_name' => 'Category name',
  'ex_:_search_by_category_name' => 'Ex : Search by Category Name...',
  'date_duration' => 'Date duration',
  'time_duration' => 'Time duration',
  'join' => 'Join',
  'leave' => 'Leave',
  'edit_store_info' => 'Edit store info',
  'edit_store_information' => 'Edit store information',
  'Ratio (1:1)' => 'Ratio (1:1)',
  'Employee image size max 2 MB' => 'Employee image size max 2 MB',
  'Update Bank Info' => 'Update Bank Info',
  'Add Bank Info' => 'Add Bank Info',
  'Identity Image' => 'Identity Image',
  '( Ratio 190x120 )' => '( Ratio 190x120 )',
  'delivery_man_image' => 'Delivery man image',
  'deliverymen_list' => 'Deliverymen list',
  'active_orders' => 'Active orders',
  'delivery_man_details' => 'Delivery man details',
  'reviewer_list' => 'Reviewer list',
  'ex_search_order_id' => 'Ex Search Order ID',
  'ex_search_order_id ' => 'Ex search order id ',
  'total_users' => 'Total users',
  'Add new zone' => 'Add new zone',
  'Instructions' => 'Instructions',
  'Create zone by click on map and connect the dots together' => 'Create zone by click on map and connect the dots together',
  'Use this to drag map to find proper area' => 'Use this to drag map to find proper area',
  'Click this icon to start pin points in the map and connect them to draw a zone . Minimum 3  points required' => 'Click this icon to start pin points in the map and connect them to draw a zone . Minimum 3  points required',
  'Coordinates' => 'Coordinates',
  'Minimum delivery charge' => 'Minimum delivery charge',
  'Ex:' => 'Ex:',
  'Delivery charge per KM' => 'Delivery charge per KM',
  'Want to change status for this zone ?' => 'Want to change status for this zone  ',
  'Want to delete this zone ?' => 'Want to delete this zone  ',
  'Update Zone' => 'Update Zone',
  'Add New System Module' => 'Add New System Module',
  'Module Name' => 'Module Name',
  'Add new campaign' => 'Add new campaign',
  'Search Title ...' => 'Search Title ...',
  'Campaign created successfully!' => 'Campaign created successfully!',
  'Campaign view' => 'Campaign view',
  'Item Info' => 'Item Info',
  'Item Image' => 'Item Image',
  'Item Details' => 'Item Details',
  'Add Attribute' => 'Add Attribute',
  'Want to delete this item ?' => 'Want to delete this item  ',
  'Want to delete this banner ?' => 'Want to delete this banner  ',
  'Update Banner' => 'Update Banner',
  'Add new coupon' => 'Add new coupon',
  'Want to delete this coupon ?' => 'Want to delete this coupon  ',
  'Select Restaurant' => 'Select Restaurant',
  'Notification list' => 'Notification list',
  'Want to delete this notification ?' => 'Want to delete this notification  ',
  'No Image' => 'No Image',
  'send_again' => 'Send again',
  'all_zones' => 'All zones',
  'picked_up' => 'Picked up',
  'Previous order' => 'Previous order',
  'Next order' => 'Next order',
  'Change status to pending ?' => 'Change status to pending  ',
  'Change status to confirmed ?' => 'Change status to confirmed  ',
  'Change status to processing ?' => 'Change status to processing  ',
  'Change status to handover ?' => 'Change status to handover  ',
  'Change status to out for delivery ?' => 'Change status to out for delivery  ',
  'Change status to delivered (payment status will be paid if not)?' => 'Change status to delivered (payment status will be paid if not) ',
  'Change status to canceled ?' => 'Change status to canceled  ',
  'Want to delete this category' => 'Want to delete this category',
  'Select Main Category' => 'Select Main Category',
  '1. Download the format file and fill it with proper data.' => '1. Download the format file and fill it with proper data.',
  '2. You can download the example file to understand how the data must be filled.' => '2. You can download the example file to understand how the data must be filled.',
  '3. Once you have downloaded and filled the format file, upload it in the form below and
                        submit.' => '3. Once you have downloaded and filled the format file  upload it in the form below and
                        submit.',
  '4. After uploading categories you need to edit them and set category`s images.' => '4. After uploading categories you need to edit them and set category`s images.',
  '5. For parent category "position" will 0 and for sub category it will be 1.' => '5. For parent category  position  will 0 and for sub category it will be 1.',
  '6. By default status will be 1, please input the right ids.' => '6. By default status will be 1  please input the right ids.',
  '7. For a category parent_id will be empty, for sub category it will be the category id.' => '7. For a category parent id will be empty  for sub category it will be the category id.',
  '8. For a sub category module id will it`s parents module id.' => '8. For a sub category module id will it`s parents module id.',
  'Download Spreadsheet Template' => 'Download Spreadsheet Template',
  'Template with Existing Data' => 'Template with Existing Data',
  'Template without Data' => 'Template without Data',
  'Choose File' => 'Choose File',
  'Addon Bulk Export' => 'Addon Bulk Export',
  'AddOn Bulk Import' => 'AddOn Bulk Import',
  'Item List' => 'Item List',
  'Review List' => 'Review List',
  '4. You can get store id, module id and unit id from their list, please input the right ids.' => '4. You can get store id  module id and unit id from their list  please input the right ids.',
  '5. For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59' => '5. For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59',
  '6. You can upload your product images in product folder from gallery, and copy image`s path.' => '6. You can upload your product images in product folder from gallery  and copy image`s path.',
  'Food Bulk Export' => 'Food Bulk Export',
  'Store List' => 'Store List',
  'Employee Add' => 'Employee Add',
  'Employee Edit' => 'Employee Edit',
  'order_refund' => 'Order refund',
  'Customer Details' => 'Customer Details',
  'Subscribed Emails' => 'Subscribed Emails',
  'Customer List' => 'Customer List',
  'Search by name...' => 'Search by name...',
  'Want to remove this deliveryman ?' => 'Want to remove this deliveryman  ',
  'Delivery Man Preview' => 'Delivery Man Preview',
  'ownerFirstName' => 'OwnerFirstName',
  'ownerLastName' => 'OwnerLastName',
  'storeName' => 'StoreName',
  'delivery_time' => 'Delivery time',
  'zone_id' => 'Zone id',
  'module_id' => 'Module id',
  'Withdraw request process' => 'Withdraw request process',
  'Note about transaction or
                                    request' => 'Note about transaction or
                                    request',
  'Store Bulk Import' => 'Store Bulk Import',
  '3. Once you have downloaded and filled the format file, upload it in the form below and
                        submit.Make sure the phone numbers and email addresses are unique.' => '3. Once you have downloaded and filled the format file  upload it in the form below and
                        submit.Make sure the phone numbers and email addresses are unique.',
  '4. After uploading stores you need to edit them and set stores`s logo and cover.' => '4. After uploading stores you need to edit them and set stores`s logo and cover.',
  '5. You can get module id and  zone id from their list, please input the right ids.' => '5. You can get module id and  zone id from their list  please input the right ids.',
  '6. For delivery time the format is "from-to type" for example: "30-40 min". Also you can use days or hours as type. Please be carefull about this format or leave this field empty.' => '6. For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.',
  '7. You can upload your store images in store folder from gallery, and copy image`s path.' => '7. You can upload your store images in store folder from gallery  and copy image`s path.',
  '8. Default password for store is 12345678.' => '8. Default password for store is 12345678.',
  'Import Stores File' => 'Import Stores File',
  'Withdraw Request' => 'Withdraw Request',
  'Withdraw information View' => 'Withdraw information View',
  'Search By Referance' => 'Search By Referance',
  'Employee List' => 'Employee List',
  'Search by Order ID' => 'Search by Order ID',
  'Search by name or email..' => 'Search by name or email..',
  'Add new delivery-man' => 'Add new delivery-man',
  'Update delivery-man' => 'Update delivery-man',
  'Your browser doesn`t support geolocation' => 'Your browser doesn`t support geolocation',
  'Ex :' => 'Ex :',
  'Contact Number' => 'Contact Number',
  '* pin the address in the map to calculate delivery fee' => '* pin the address in the map to calculate delivery fee',
  'Delivery fee' => 'Delivery fee',
  'Update' => 'Update',
  'Delivery Infomation' => 'Delivery Infomation',
  'Home Delivery' => 'Home Delivery',
  'Name' => 'Name',
  'Payment Method' => 'Payment Method',
  'Cash On Delivery' => 'Cash On Delivery',
  'Wallet' => 'Wallet',
  'Clear Cart' => 'Clear Cart',
  'no_customer_selected' => 'No customer selected',
  'please_select_a_valid_delivery_location_on_the_map' => 'Please select a valid delivery location on the map',
  'insufficient_wallet_balance' => 'Insufficient wallet balance',
  'Add new customer' => 'Add new customer',
  'Billing Section' => 'Billing Section',
  'Add New Customer' => 'Add New Customer',
  'Total' => 'Total',
  'Delivery Information' => 'Delivery Information',
  'Ex: +3264124565' => 'Ex: +3264124565',
  'Ex: 4th' => 'Ex: 4th',
  'Ex: 45/C' => 'Ex: 45/C',
  'Ex: 1A' => 'Ex: 1A',
  'Ex: address' => 'Ex: address',
  'Sorry, the minimum value was reached' => 'Sorry  the minimum value was reached',
  'Sorry, stock limit exceeded.' => 'Sorry  stock limit exceeded.',
  'Please choose all the options' => 'Please choose all the options',
  'Cash' => 'Cash',
  'Card' => 'Card',
  'Paid Amount' => 'Paid Amount',
  'Change Amount' => 'Change Amount',
  'All Items' => 'All Items',
  'Active Items' => 'Active Items',
  'Inactive Items' => 'Inactive Items',
  'food_campaign' => 'Food campaign',
  'Search by title' => 'Search by title',
  'Item Campaigns' => 'Item Campaigns',
  'Select Store' => 'Select Store',
  'Item wise report table' => 'Item wise report table',
  'If this option is on, customers will get free delivery' => 'If this option is on  customers will get free delivery',
  'total_withdrawal_amount' => 'Total withdrawal amount',
  'confirm_order' => 'Confirm order',
  'Update Item' => 'Update Item',
  'item_info' => 'Item info',
  'item_image' => 'Item image',
  'ex_:_search_sub_category' => 'Ex : Search Sub Category',
  'search_by_title' => 'Search by title',
  'ex_:_Search_Store_Name' => 'Ex : Search Store Name',
  'store_type' => 'Store type',
  '* This discount is applied on all the items in your store' => '* This discount is applied on all the items in your store',
  'include_POS_in_store_panel' => 'Include POS in store panel',
  'collect_from' => 'Collect from',
  'Ex_:_Card' => 'Ex : Card',
  'Ex_:_1000' => 'Ex : 1000',
  'history' => 'History',
  'Search By Referance  or Name' => 'Search By Referance  or Name',
  'Ex : 5+ Characters' => 'Ex : 5+ Characters',
  'Ex_:_mail.6am.one' => 'Ex : mail.6am.one',
  'Ex : smtp' => 'Ex : smtp',
  'Ex : 587' => 'Ex : 587',
  'message_description' => 'Message Description',
  'restaurant_bulk_export' => 'Store Bulk Export',
  'Search Menu...' => 'Search Menu...',
  'Update option is disabled for demo!' => 'Update option is disabled for demo!',
  'cash on delivery' => 'Cash on delivery',
  'POS Orders' => 'POS Orders',
  'Or' => 'Or',
  'Accoutn transaction information' => 'Accoutn transaction information',
  'preparing_in_restaurants' => 'Preparing in restaurants',
  'Proceed, If thermal printer is ready.' => 'Proceed  If thermal printer is ready.',
  'The chosen model will confirm the order first. For example, if you choose the delivery confirmation model then the delivery men will get the orders before the restaurants and confirm for delivery and after confirmation by the delivery men, the restaurants will get the order for processing.' => 'The chosen model will confirm the order first. For example  if you choose the delivery confirmation model then the delivery men will get the orders before the restaurants and confirm for delivery and after confirmation by the delivery men  the restaurants will get the order for processing.',
  'restaurant' => 'Restaurant',
  'Delivery Man can Cancel Order' => 'Delivery Man can Cancel Order',
  'Order cancellation is possible by the delivery person if "Yes" is chosen .' => 'Order cancellation is possible by the delivery person if  Yes  is chosen .',
  'restaurant_can_cancel_order' => 'Restaurant can cancel order',
  'Order cancellation is possible by the restaurant if "Yes" is chosen .' => 'Order cancellation is possible by the restaurant if  Yes  is chosen .',
  'store_can_cancel_order' => 'Store can cancel order',
  'Order cancellation is possible by the store if "Yes" is chosen .' => 'Order cancellation is possible by the store if  Yes  is chosen .',
  'preparing_in_stores' => 'Preparing in stores',
  'Campaign title...' => 'Campaign title...',
  'Coupon Title' => 'Coupon Title',
  'item on the way' => 'Item on the way',
  'Clear all filters' => 'Clear all filters',
  'ssl commerz payment' => 'Ssl commerz payment',
  'Want to delete this attribute ?' => 'Want to delete this attribute  ',
  'Want to delete this unit ?' => 'Want to delete this unit  ',
  'Want to delete this addon ?' => 'Want to delete this addon  ',
  'Want to remove discount?' => 'Want to remove discount ',
  'Be careful before you turn on/off maintenance mode' => 'Be careful before you turn on/off maintenance mode',
  'Maintenance is on.' => 'Maintenance is on.',
  'Maintenance is off.' => 'Maintenance is off.',
  'NB : #OTP# will be replace with otp' => 'NB : #OTP# will be replace with otp',
  'EX of SMS provider`s template : your OTP is XXXX here, please check.' => 'EX of SMS provider`s template : your OTP is XXXX here  please check.',
  'NB : Keep an OTP variable in your SMS providers OTP Template.' => 'NB : Keep an OTP variable in your SMS providers OTP Template.',
  'FCM Settings' => 'FCM Settings',
  'Feature title' => 'Feature title',
  'Feature description' => 'Feature description',
  'Promotions' => 'Promotions',
  'Your' => 'Your',
  'All Service' => 'All Service',
  'in one field' => 'In one field',
  'welcome_back' => 'Welcome back',
  'order_on_the_way' => 'Order on the way',
  'cash_receipt' => 'Cash receipt',
  'contact_name' => 'Contact name',
  'desc' => 'Desc',
  'Ex: Jhone' => 'Ex: Jhone',
  'Login as Store Employee' => 'Login as Store Employee',
  'login here' => 'Login here',
  'Login as Store Owner' => 'Login as Store Owner',
  'Do you want to logout?' => 'Do you want to logout ',
  'Enter order verification code' => 'Enter order verification code',
  'ex_:_search_by_order_ID' => 'Ex : search by order ID',
  'take away' => 'Take away',
  'Customer Not found!' => 'Customer Not found!',
  'paid_amount_is_less_than_total_amount' => 'Paid amount is less than total amount',
  'Both' => 'Both',
  'both' => 'Both',
  'digital payment' => 'Digital payment',
  'Messages' => 'Messages',
  'After activating this field, customers are able to place scheduled orders.' => 'After activating this field  customers are able to place scheduled orders.',
  'If this option is enabled, the Delivery men Tip option will show on the user app & web app during order placement.' => 'If this option is enabled  the Delivery men Tip option will show on the user app & web app during order placement.',
  'If this field is enabled, the delivery man is able to see the earnings when accepting the order on the order request page.' => 'If this field is enabled  the delivery man is able to see the earnings when accepting the order on the order request page.',
  'Turning on this, admin will get a popup notification with sound for all orders.' => 'Turning on this  admin will get a popup notification with sound for all orders.',
  'If this field is active, customers have to provide a 4-digit code to the delivery man to deliver an order successfully. Customers will get this code in order details.' => 'If this field is active  customers have to provide a 4-digit code to the delivery man to deliver an order successfully. Customers will get this code in order details.',
  'When this field is active, the restaurants and the customers both can see the veg/non-veg tag.' => 'When this field is active  the restaurants and the customers both can see the veg/non-veg tag.',
  'If this field is active, stores can register themself using the restaurant app, user app, or website.' => 'If this field is active  stores can register themself using the restaurant app  user app  or website.',
  'When this field is active, delivery men can register themself using the delivery man app, user app, or website.' => 'When this field is active  delivery men can register themself using the delivery man app  user app  or website.',
  'When this field is active, the stores and the customers both can see the veg/non-veg tag.' => 'When this field is active  the stores and the customers both can see the veg/non-veg tag.',
  'If this field is active, stores can register themself using the store app, user app, or website.' => 'If this field is active  stores can register themself using the store app  user app  or website.',
  'The chosen model will confirm the order first. For example, if you choose the delivery confirmation model then the delivery men will get the orders before the stores and confirm for delivery and after confirmation by the delivery men, the stores will get the order for processing.' => 'The chosen model will confirm the order first. For example  if you choose the delivery confirmation model then the delivery men will get the orders before the stores and confirm for delivery and after confirmation by the delivery men  the stores will get the order for processing.',
  'By disabling this field, the store can`t manage items, which means the store web panel app won`t get the access for managing items' => 'By disabling this field  the store can`t manage items  which means the store web panel app won`t get the access for managing items',
  'If this field is active, the store panel & store app can see the customer`s review' => 'If this field is active  the store panel & store app can see the customer`s review',
  'If this option is turned on, the restaurant panel will get the
                                    Point of Sale (POS) option' => 'If this option is turned on  the restaurant panel will get the
                                    Point of Sale (POS) option',
  'If this option is turned on, the store panel will get the
                                    Point of Sale (POS) option' => 'If this option is turned on  the store panel will get the
                                    Point of Sale (POS) option',
  'If this status is turned on, the customer is able to place a scheduled
                                    order for this store' => 'If this status is turned on  the customer is able to place a scheduled
                                    order for this store',
  'When this option is enabled, stores need to deliver orders by themselves or
                                    by their own delivery man. Stores will also get an option for adding their own delivery man
                                    from the restaurant panel' => 'When this option is enabled  stores need to deliver orders by themselves or
                                    by their own delivery man. Stores will also get an option for adding their own delivery man
                                    from the restaurant panel',
  'When this option is enabled, stores need to deliver orders by themselves or
                                    by their own delivery man. Stores will also get an option for adding their own delivery man
                                    from the store panel' => 'When this option is enabled  stores need to deliver orders by themselves or
                                    by their own delivery man. Stores will also get an option for adding their own delivery man
                                    from the store panel',
  'customer_wallet_disable_warning_admin' => 'Customer wallet in disabled, please enable it from customer settings.',
  'Order Refunds' => 'Order Refunds',
  'Refund Requests' => 'Refund Requests',
  'refund_settings' => 'Refund settings',
  'requested' => 'Requested',
  'Refund' => 'Refund',
  'refund_rejected' => 'Refund rejected',
  'Rejected' => 'Rejected',
  'rejected' => 'Rejected',
  'Refund Settings' => 'Refund Settings',
  'Refund Request' => 'Refund Request',
  'Mode' => 'Mode',
  '*By Turning ON Refund Mode, Customers Can Sent Refund Requests' => '*By Turning ON Refund Mode  Customers Can Sent Refund Requests',
  'Add a Refund Reason' => 'Add a Refund Reason',
  'New Refund Reason' => 'New Refund Reason',
  'Add Now' => 'Add Now',
  'Refund Reason List' => 'Refund Reason List',
  'Refund Reason Added Successfully' => 'Refund Reason Added Successfully',
  'Want to delete this refund reason ?' => 'Want to delete this refund reason  ',
  'Reason' => 'Reason',
  'Refund Reason Deleted Successfully' => 'Refund Reason Deleted Successfully',
  'Fatoorah' => 'Fatoorah',
  'Refund Reason Updated Successfully' => 'Refund Reason Updated Successfully',
  'refund requested' => 'Refund requested',
  'Order Rejection' => 'Order Rejection',
  'Note' => 'Note',
  'Confirm' => 'Confirm',
  'requested refunds' => 'Requested refunds',
  'Refund Reason' => 'Refund Reason',
  'Customer Note' => 'Customer Note',
  'Admin Note' => 'Admin Note',
  'Refund Amount' => 'Refund Amount',
  'Refund Status' => 'Refund Status',
  'Refund Method' => 'Refund Method',
  'Refund Requested on' => 'Refund Requested on',
  'Refund Rejection Successfully' => 'Refund Rejection Successfully',
  'Your Refund Request has been Rejected.' => 'Your Refund Request has been Rejected.',
  'refund request canceled' => 'Refund request canceled',
  'Refund Rejected' => 'Refund Rejected',
  'pay later' => 'Pay later',
  'requested refund' => 'Requested refund',
  'requested_on' => 'Requested on',
  'Method' => 'Method',
  'Status' => 'Status',
  'refund' => 'Refund',
  'Social Login Setup' => 'Social Login Setup',
  'created' => 'Created',
  'Copied to the clipboard' => 'Copied to the clipboard',
  'default_link' => 'Default link',
  'Module Setup' => 'Module Setup',
  'per_km_delivery_charge' => 'Per km delivery charge',
  'zone_module_updated_successfully' => 'Zone module updated successfully',
  'Zone Wise Module Setup' => 'Zone Wise Module Setup',
  'Admin Comission in Delivery Charge' => 'Admin Comission in Delivery Charge',
  'Store Wise Report' => 'Store Wise Report',
  'Summary Report' => 'Summary Report',
  'Sales Report' => 'Sales Report',
  'Order Report' => 'Order Report',
  'Transactions Report' => 'Transactions Report',
  'Store Report' => 'Store Report',
  'order_statistics' => 'Order statistics',
  'Total Orders' => 'Total Orders',
  'Average Order Value :' => 'Average Order Value :',
  'Total Stores' => 'Total Stores',
  'Store' => 'Store',
  'Total Order' => 'Total Order',
  'Total Amount' => 'Total Amount',
  'Completion Rate' => 'Completion Rate',
  'Cancelation Rate' => 'Cancelation Rate',
  'Action' => 'Action',
  'Search by ID, customer or payment status' => 'Search by ID  customer or payment status',
  'Filter Data' => 'Filter Data',
  'Total Sales' => 'Total Sales',
  'Product' => 'Product',
  'QTY Sold' => 'QTY Sold',
  'Gross Sale' => 'Gross Sale',
  'Tax' => 'Tax',
  'Commission' => 'Commission',
  'Discount Given' => 'Discount Given',
  'Net Sale' => 'Net Sale',
  'payment statistics' => 'Payment statistics',
  'Order ID' => 'Order ID',
  'Order Date' => 'Order Date',
  'Customer Info' => 'Customer Info',
  'Discount' => 'Discount',
  'Delivery Charge' => 'Delivery Charge',
  'XID' => 'XID',
  'Created At' => 'Created At',
  'Transaction Amount' => 'Transaction Amount',
  'Reference' => 'Reference',
  'Search by product..' => 'Search by product..',
  'store_wise_report' => 'Store Wise Report',
  'Search by ID..' => 'Search by ID..',
  'coupon_discount_amount' => 'Coupon discount amount',
  'total_tax_amount' => 'Total tax amount',
  'All Time' => 'All Time',
  'This Year' => 'This Year',
  'Previous Year' => 'Previous Year',
  'This Week' => 'This Week',
  'Incomplete' => 'Incomplete',
  'Completed' => 'Completed',
  'Total Items' => 'Total Items',
  'Cash Payments' => 'Cash Payments',
  'Digital Payments' => 'Digital Payments',
  'delivery_charge_per_km' => 'Delivery charge per km',
  'Comission on delivery fee' => 'Comission on delivery fee',
  'Ongoing Rate' => 'Ongoing Rate',
  'order statistics' => 'Order statistics',
  'Custom' => 'Custom',
  'Total Discount Given' => 'Total Discount Given',
  'Product Discount' => 'Product Discount',
  'View All Orders' => 'View All Orders',
  'Completed payment statistics' => 'Completed payment statistics',
  'Total Delivered Order' => 'Total Delivered Order',
  'Not applicable' => 'Not applicable',
  'Average Value of completed orders.' => 'This value is the average of total order amount and total completed order count.',
  'Average Value of all type of orders.' => 'This value is the sum of the total completed order and amount.',
  'ecommerce' => 'Ecommerce',
  'venture_starts_here_!' => 'Venture starts here !',
  'Enjoy all services in one platform' => 'Enjoy all services in one platform',
  'grocery' => 'Grocery',
  'pharmacy' => 'Pharmacy',
  'Earn point by' => 'Earn point by',
  'Refer' => 'Refer',
  'Friend' => 'Friend',
  'Refer a friend' => 'Refer a friend',
  'Earn' => 'Earn',
  'Money' => 'Money',
  'Earn money by using different platform' => 'Earn money by using different platform',
  'Download Seller' => 'Download Seller',
  'App' => 'App',
  'Become a best' => 'Become a best',
  'Seller' => 'Seller',
  'Download Deliveryman' => 'Download Deliveryman',
  'Become a smart' => 'Become a smart',
  'Deliveryman' => 'Deliveryman',
  'What\'s so' => 'What s so',
  'Special' => 'Special',
  'About' => 'About',
  '6am' => '6am',
  'Mart?' => 'Mart ',
  'Download' => 'Download',
  'Car provider' => 'Car provider',
  'Still increasing' => 'Still increasing',
  'Let’s' => 'Let’s',
  'Manage your business' => 'Manage your business',
  'Smartly or Earn.' => 'Smartly or Earn.',
  'Download the Seller App' => 'Download the Seller App',
  'Download the deliveryman App' => 'Download the deliveryman App',
  'Suppport' => 'Suppport',
  'Contact' => 'Contact',
  'Application' => 'Application',
  'join as' => 'Join as',
  'download App Section' => 'Download App Section',
  'promotion Banner' => 'Promotion Banner',
  'module Section' => 'Module Section',
  'seller_banner_bg' => 'Seller banner bg',
  'deliveryman_banner_bg' => 'Deliveryman banner bg',
  'landing_page_download_app_section_updated' => 'Landing page download app section updated',
  'sub_title' => 'Sub title',
  'banner_image' => 'Banner image',
  'two' => 'Two',
  'image two' => 'Image two',
  'Privacy' => 'Privacy',
  'Policy' => 'Policy',
  'Get' => 'Get',
  'in touch' => 'In touch',
  'Any question or remarks?' => 'Any question or remarks ',
  'Just write us a message!' => 'Just write us a message!',
  'Call' => 'Call',
  'Us' => 'Us',
  'Email' => 'Email',
  'Address' => 'Address',
  'Time' => 'Time',
  'Monday' => 'Monday',
  'Friday' => 'Friday',
  'Send' => 'Send',
  'Message' => 'Message',
  'Terms' => 'Terms',
  'And' => 'And',
  'Conditions' => 'Conditions',
  'Refunded amount added to customer wallet' => 'Refunded amount added to customer wallet',
  'What' => 'What',
  's so' => 'S so',
  'Download Seller App' => 'Download Seller App',
  'Download Deliveryman App' => 'Download Deliveryman App',
  'module_section_title' => 'Module Section Title',
  'module_section_sub_title' => 'Module Section Sub-title',
  'refer_section_title' => 'Refer Section Title',
  'refer_section_sub_title' => 'Refer Section Sub Title',
  'refer_section_description' => 'Refer Section Description',
  'joinus_section_title' => 'Joinus Section Title',
  'joinus_section_sub_title' => 'Joinus Section Sub Title',
  'download_app_section_title' => 'Download App Section Title',
  'download_app_section_sub_title' => 'Download App Section Sub Title',
  'newsletter_title' => 'Newsletter Title',
  'newsletter_sub_title' => 'Newsletter Sub Title',
  'contact_us_title' => 'Contact Us Title',
  'contact_us_sub_title' => 'Contact Us Sub Title',
  'landing_page_promotion_banner_updated' => 'Landing Page Promotion Banner Updated',
  'landing_page_module_section_updated' => 'Landing Page Module Section Updated',
  'Reviewer Brand' => 'Reviewer Brand',
  'Image' => 'Image',
  'seller_banner_background' => 'Seller Banner Background',
  'deliveryman_banner_background' => 'Deliveryman Banner Background',
  'app_download_count_numbers' => 'User Count Numbers',
  'seller_count_numbers' => 'Seller Count Numbers',
  'deliveryman_count_numbers' => 'Deliveryman Count Numbers',
  'Opening Time' => 'Opening Time',
  'Closing Time' => 'Closing Time',
  'opening_day' => 'Opening day',
  'thrusday' => 'Thrusday',
  'closing_day' => 'Closing day',
  'application' => 'Application',
  'Contact Messages' => 'Contact Messages',
  'all_message_lists' => 'All message lists',
  'message_lists' => 'Message lists',
  'ex_:_message_name' => 'Ex : message name',
  'subject' => 'Subject',
  'Seen/Unseen' => 'Seen/Unseen',
  'Not_Seen_Yet' => 'Not Seen Yet',
  'Message View' => 'Message View',
  'Message_View' => 'Message View',
  'User_details' => 'User details',
  'Feedback' => 'Feedback',
  'Please_send_a_Feedback' => 'Please send a Feedback',
  'check' => 'Check',
  'Message_Log' => 'Message Log',
  'Subject' => 'Subject',
  'No_reply' => 'No Reply',
  'Send_Mail' => 'Send Mail',
  'Configure_your_mail_setup_first' => 'Configure your mail setup first',
  'Mail_Body' => 'Mail Body',
  'Reply_form_6amMart' => 'Reply form 6amMart',
  'Dear' => 'Dear',
  'All copy right reserved' => 'All copy right reserved',
  'Seen' => 'Seen',
  'contact_deleted_successfully' => 'Contact deleted successfully',
  'already_check' => 'Already check',
  'Cancel Refund' => 'Cancel Refund',
  'colors' => 'Colors',
  'Primary Color 1' => 'Primary Color 1',
  'Primary Color 2' => 'Primary Color 2',
  'Add Parcel Category' => 'Add Parcel Category',
  'You have to set category wise charge from parcel category' => 'You have to set category wise charge from parcel category',
  'Set charge from parcel category' => 'Set charge from parcel category',
  'New Registered Stores' => 'New Registered Stores',
  'New Items' => 'New Items',
  'Currently you need to manage discount with store.' => 'Currently you need to manage discount with store.',
  'expense_report' => 'Expense Report',
  'Expense Report' => 'Expense Report',
  'Search by type or description' => 'Search by type or description',
  'expense_date' => 'Expense date',
  'delivery_commission' => 'Delivery commission',
  'Total Tax' => 'Total Tax',
  'Total Commission' => 'Total Commission',
  'Total Store Earnings' => 'Total Store Earnings',
  'contact_messages' => 'Contact messages',
  'Search by order_id' => 'Search by order id',
  'landing_page_counter_section_updated' => 'Landing page counter section updated',
  'contact_us_image' => 'Contact us image',
  'cancelation' => 'Cancelation',
  'shipping_policy' => 'Shipping policy',
  'shipping_policy_updated' => 'Shipping policy updated',
  'Want to delete this message?' => 'Want to delete this message ',
  'Reply_form_6amMart 123' => 'Reply form 6amMart 123',
  'Something went wrong!' => 'Something went wrong!',
  'refund_page' => 'Refund page',
  'refund_updated' => 'Refund updated',
  'cancelation_updated' => 'Cancelation updated',
  'If the order amount exceeds this amount the delivery fee will be free and the delivery fee will be deduced from the admins commission.' => NULL,
  'Want to delete this message ?' => 'Want to delete this message  ',
  'Refund Policy' => 'Refund Policy',
  'Cancelation Policy' => 'Cancelation Policy',
  'Shipping Policy' => 'Shipping Policy',
  'seller_App_url' => 'Seller App url',
  'deliveryman_App_url' => 'Deliveryman App url',
  'Grocery' => 'Grocery',
  'Pharmacy' => 'Pharmacy',
  'Shop' => 'Shop',
  'Food' => 'Food',
  'Parcel' => 'Parcel',
  'Test A Module' => 'Test A Module',
  'parcel123' => 'Parcel123',
  'Super Shop' => 'Super Shop',
  'parcel144' => 'Parcel144',
  'Module_test' => 'Module test',
  'Food43454' => 'Food43454',
  'Grocery2222' => 'Grocery2222',
  'Module for all zone' => 'Module for all zone',
  'Grocery All Zone' => 'Grocery All Zone',
  'PRO Super Shop' => 'PRO Super Shop',
  'New  Grocery  System Module' => 'New  Grocery  System Module',
  'New  Parcel System Module' => 'New  Parcel System Module',
  'cvb' => 'Cvb',
  'test mo' => 'Test mo',
  'Tech Mart' => 'Tech Mart',
  'Tech parcel' => 'Tech parcel',
  'Mart GM test' => 'Mart GM test',
  'Mart  New Parcel For' => 'Mart  New Parcel For',
  'Agora x' => 'Agora x',
  'Mart FM test' => 'Mart FM test',
  'ValAgen Gro' => 'ValAgen Gro',
  'ValAgen Food' => 'ValAgen Food',
  'ValAgen Phar' => 'ValAgen Phar',
  'ValAgen Ecom' => 'ValAgen Ecom',
  'ValAgen Parcel' => 'ValAgen Parcel',
  'Tech Mart Mart' => 'Tech Mart Mart',
  'test' => 'Test',
  'ghmnh' => 'Ghmnh',
  'zsxscds' => 'Zsxscds',
  'Test Parcel' => 'Test Parcel',
  '6parcel' => '6parcel',
  'What is so special about' => 'What is so special about',
  'Header Title' => 'Header Title',
  'Header Sub Title' => 'Header Sub Title',
  'asdf' => 'Asdf',
  'sadf' => 'Sadf',
  'Store_front' => 'Store front',
  'free_over_delivery_message' => 'If the order amount exceeds this amount the delivery fee will be free and the delivery fee will be deduced from the admins commission.',
  'footer_text' => 'Footer text',
  'minimum_order' => 'Minimum order',
  'schedule_order' => 'Schedule order',
  'vendor_id' => 'Vendor id',
  'cover_photo' => 'Cover photo',
  'reviews_section' => 'Reviews section',
  'off_day' => 'Off day',
  'order_count' => 'Order count',
  'total_order' => 'Total order',
  'order_place_to_schedule_interval' => 'Order place to schedule interval',
  'category_ids' => 'Category ids',
  'add_ons' => 'Add ons',
  'choice_options' => 'Choice options',
  'tax_type' => 'Tax type',
  'discount_type' => 'Discount type',
  'available_time_starts' => 'Available time starts',
  'available_time_ends' => 'Available time ends',
  'store_id' => 'Store id',
  'avg_rating' => 'Avg rating',
  'rating_count' => 'Rating count',
  'unit_id' => 'Unit id',
  'parent_id' => 'Parent id',
  'position' => 'Position',
  'total_delivered_orders' => 'Total delivered orders',
  'ssl_commerz_payment' => 'Ssl commerz payment',
  'Something went wrong' => 'Something went wrong',
  'Currently you need to manage discount with the Restaurant.' => 'Currently you need to manage discount with the Restaurant.',
  'refund_request_canceled' => 'Refund request canceled',
  'Refund Image' => 'Refund Image',
  'New  GroceryÂ Â System Module' => 'New  GroceryÂ Â System Module',
  'insufficient_point' => 'Insufficient Point',
  'machine' => 'Machine',
  'store_name' => 'Store Name',
  'store_phone' => 'Store Phone',
  'owner_name' => 'Owner Name',
  'owner_phone' => 'Owner Phone',
  'invalid_latitude_or_longtitude' => 'Invalid latitude or longtitude',
  'amount_received_by' => 'Amount received by',
  '9. Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error' => '9. Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error.',
  'food_variations' => 'Food variations',
  'add_new_variation' => 'Add new variation',
  'add_new' => 'Add new',
  'selcetion_type' => 'Selcetion type',
  'Multiple' => 'Multiple',
  'Single' => 'Single',
  'Min' => 'Min',
  'Max' => 'Max',
  'Required' => 'Required',
  'Delete' => 'Delete',
  'Option_name' => 'Option name',
  'Additional_price' => 'Additional price',
  'Add_New_Option' => 'Add New Option',
  'please_add_more_options_or_change_the_max_value_for' => 'Please add more options or change the max value for',
  'add new variation' => 'Add new variation',
  'You_need_to_select_minimum_ ' => 'You need to select minimum  ',
  'to_maximum_ ' => 'To maximum  ',
  'Please select items from' => 'Please select items from',
  'Variation' => 'Variation',
  'multiple_select' => 'Multiple select',
  'Min_select' => 'Min select',
  'Max_select' => 'Max select',
  'delivery_tips' => 'Delivery tips',
  'Search Data' => 'Search Data',
  'All Category' => 'All Category',
  'Today' => 'Today',
  'Custom Date Range' => 'Custom Date Range',
  'Filter' => 'Filter',
  'All Categories' => 'All Categories',
  'Start Date' => 'Start Date',
  'End Date' => 'End Date',
  'single_select' => 'Single select',
  'Please select minimum ' => 'Please select minimum ',
  ' For ' => ' For ',
  'average_ratings' => 'Average ratings',
  'total_amount_sold' => 'Total amount sold',
  'total_discount_given' => 'Total discount given',
  'average_sale_value' => 'Average sale value',
  'Admin Earning' => 'Admin Earning',
  'Store Earning' => 'Store Earning',
  'Deliveryman Earning' => 'Deliveryman Earning',
  'Completed Transaction' => 'Completed Transaction',
  'On-Hold Transactions' => 'On-Hold Transactions',
  'Canceled Transactions' => 'Canceled Transactions',
  'order_report' => 'Order Report',
  'in_progress_orders' => 'In Progress Orders',
  'failed_orders' => 'Failed Orders',
  'on_the_way' => 'On the  Way',
  'Total Item Amount' => 'Total Item Amount',
  'Item Discount' => 'Item Discount',
  'Coupon Discount' => 'Coupon Discount',
  'Discounted Amount' => 'Discounted Amount',
  'Order Amount' => 'Order Amount',
  'Customer Name' => 'Customer Name',
  'Order Status' => 'Order Status',
  'order_amount' => 'Order amount',
  'custom' => 'Custom',
  'lists' => 'Lists',
  'expense' => 'Expense',
  'requests' => 'Requests',
  'Pending Requests' => 'Pending Requests',
  'Store Pending Requests' => 'Store Pending Requests',
  'pending_requests' => 'Pending requests',
  'deny_requests' => 'Deny requests',
  'Store Deny Requests' => 'Store Deny Requests',
  'deny_delivery_man' => 'Deny delivery man',
  'denied_stores' => 'Denied stores',
  'total_item_amount' => 'Total item amount',
  'item_discount' => 'Item discount',
  'discounted_amount' => 'Discounted amount',
  'digital_payment' => 'Digital payment',
  'ssl_commerz' => 'Ssl commerz',
  'vendor_type_required' => 'Vendor type required',
  'admin_discount' => 'Admin discount',
  'store_discount' => 'Store discount',
  'current_language_key_required' => 'User current language is required!',
  'transection_report' => 'Transection Report',
  'item_report' => 'Item Report',
  'Item report table' => 'Item report table',
  'transaction_report' => 'Transaction report',
  'parcel_order' => 'Parcel order',
  'Accepted by Delivery Man' => 'Accepted by Delivery Man',
  'Packaging' => 'Packaging',
  'Out for Delivery' => 'Out for Delivery',
  'User Statistics' => 'User Statistics',
  'view_all' => 'View all',
  'Orders' => 'Orders',
  'Delivery man' => 'Delivery man',
  'Settings' => 'Settings',
  'Monitor your business general settings from here' => 'Monitor your business general settings from here',
  'Zone Setup' => 'Zone Setup',
  'System Module Setup' => 'System Module Setup',
  'Business Settings' => 'Business Settings',
  '3rd Party' => '3rd Party',
  'Social Media and Page Setup' => 'Social Media and Page Setup',
  'View All' => 'View All',
  'Modules Section' => 'Modules Section',
  'Select Module & Monitor your business monitor wise' => 'Select Module & Monitor your business monitor wise',
  'e-Pharma' => 'E-Pharma',
  'e-Medi Care' => 'E-Medi Care',
  'Vaccine Center' => 'Vaccine Center',
  'GroFresh' => 'GroFresh',
  'e-Cab' => 'E-Cab',
  'e-Shop' => 'E-Shop',
  'top selling stores' => 'Top selling stores',
  'order : ' => 'Order : ',
  'most rated stores' => 'Most rated stores',
  'top selling products' => 'Top selling products',
  'top rated products' => 'Top rated products',
  'Failed Orders' => 'Failed Orders',
  'category_setup' => 'Category setup',
  'delivery_fee_setup' => 'Delivery fee setup',
  'tags' => 'Tags',
  'foods' => 'Foods',
  'restaurants' => 'Restaurants',
  'U' => 'U',
  'Users' => 'Users',
  'Transactions & Reports' => 'Transactions & Reports',
  'total_customer' => 'Total customer',
  'active_customer' => 'Active customer',
  'blocked_customers' => 'Blocked customers',
  'blocked_customer' => 'Blocked customer',
  'newly_joined' => 'Newly joined',
  'total_delivery_man' => 'Total delivery man',
  'active_delivery_man' => 'Active delivery man',
  'newly_joined_delivery_man' => 'Newly joined delivery man',
  'inactive_deliveryman' => 'Inactive deliveryman',
  'assing_order_count' => 'Assing order count',
  'total_employee' => 'Total employee',
  'customer_satisfaction' => 'Customer satisfaction',
  'review_received' => 'Review received',
  'customer_growth' => 'customer growth',
  'this_year' => 'This Year',
  'man' => 'Man',
  'delivery_man_settings' => 'Delivery Man Settings',
  'Date & Time' => 'Date & Time',
  'Expense Type' => 'Expense Type',
  'expense amount' => 'Expense amount',
  'Delivery Man Request' => 'Delivery Man Request',
  'store_report' => 'Store report',
  'store request' => 'Store request',
  'Including accepted and processing Orders' => 'Including accepted and processing Orders',
  'Including accepted and processing orders' => 'Including accepted and processing orders',
  'cache_clear' => 'Cache clear',
  'cache_clear_successfully' => 'Cache clear successfully',
  'delivery_man_request' => 'Delivery man request',
  'prescription_order' => 'Prescription order',
  'update_order_amount' => 'Update order amount',
  'update_discount_amount' => 'Update discount amount',
  'store_request' => 'Store request',
  'Refunded' => 'Refunded',
  'Refunded Transactions' => 'Refunded Transactions',
  'Refunded Transaction' => 'Refunded Transaction',
  'commision_on_delivery_charge' => 'Commision on delivery charge',
  'admin_net_income' => 'Admin net income',
  'store_net_income' => 'Store net income',
  'order_transaction_statement' => 'Order Transaction Statement',
  'statement' => 'Statement',
  'If_you_require_any_assistance_or_have_feedback_or_suggestions_about_our_site,_you' => 'If you require any assistance or have feedback or suggestions about our site  you',
  'can_email_us_at' => 'Can email us at',
  'All_copy_right_reserved_©_2023_' => 'All copy right reserved © 2023 ',
  'total_ordered_product_price' => 'Total ordered product price',
  'total_product_discount' => 'Total product discount',
  'total_coupon_discount' => 'Total coupon discount',
  'total_discounted_amount' => 'Total discounted amount',
  'total_vat/_tax' => 'Total vat/ tax',
  'total_delivery_charge' => 'Total delivery charge',
  'additional_information' => 'Additional information',
  'totals' => 'Totals',
  'the_start_day_and_end_day_is_same' => 'The start day and end day is same',
  'completed' => 'Completed',
  'delivered_by' => 'Delivered by',
  'freelance' => 'Freelance',
  'email_not_found' => 'Email not found',
  'prescription_order_hint' => 'By enabling this option users will be able to upload prescriptions and place orders instead of adding the medicine to the cart',
  'not_received_yet' => 'Not Received Yet',
  'Registered Stores' => 'Registered Stores',
  'search_by_name' => 'Search by name',
  'new_joining_requests' => 'New joining requests',
  'pending_stores' => 'Pending stores',
  'maximum_cod_order_amount' => 'Maximum COD order amount',
  'set_maximum_cod_order_amount' => 'Maximum order amount for Cash On Delivery',
  'search_tags' => 'Search tags',
  'discount_given' => 'Discount given',
  'pending_delivery_man' => 'Pending delivery man',
  'denied_deliveryman' => 'Denied deliveryman',
  'ex_: search_delivery_man' => 'Ex : search delivery man',
  'denied_delivery_man' => 'Denied delivery man',
  'Want to change digital payment for this zone ?' => 'Want to change digital payment for this zone  ',
  'Want to change cash on delivery for this zone ?' => 'Want to change cash on delivery for this zone  ',
  'zone_cash_on_delivery_status_updated' => 'Zone cash on delivery status updated',
  'zone_digital_payment_status_updated' => 'Zone digital payment status updated',
  'Employees' => 'Employees',
  'prescription_order_status' => 'Prescription order status',
  'When this field is active, store will get prescription order option.' => 'When this field is active  store will get prescription order option.',
  'Handover' => 'Handover',
  'gross_sale' => 'Gross sale',
  'Freelance' => 'Freelance',
  'When the order is successfully delivered full order amount goes to this section.' => 'When the order is successfully delivered full order amount goes to this section.',
  'If the order is successfully refunded, the full order amount goes to this section without the delivery fee and delivery tips.' => 'If the order is successfully refunded  the full order amount goes to this section without the delivery fee and delivery tips.',
  'Deducting the admin discount from the admin net income amount goes to this section.' => 'Deducting the admin discount from the admin net income amount goes to this section.',
  'If self-delivery is off, deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.' => 'If self-delivery is off  deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.',
  'Deducting the admin commission on the delivery fee, the delivery fee & tips amount goes to
                                        earning section.' => 'Deducting the admin commission on the delivery fee  the delivery fee & tips amount goes to
                                        earning section.',
  'Include_TAX_Amount' => 'Include TAX Amount',
  'When this field is active, Tax amount will not be added with the total product price' => 'When this field is active  Tax amount will not be added with the total product price',
  'TAX_Included' => 'TAX Included',
  'Site Direction' => 'Site Direction',
  'Left_to_Right' => 'Left to Right',
  'Right_to_Left' => 'Right to Left',
  'Fully Booked Delivery Man' => 'Fully Booked Delivery Man',
  'Dispatch Management' => 'Dispatch Management',
  'Languages' => 'Languages',
  'System_Setup' => 'System Setup',
  'changing_some_settings_will_take_time_to_show_effect_please_clear_session_or_wait_for_60_minutes_else_browse_from_incognito_mode' => 'Changing some settings will take time to show effect please clear session or wait for 60 minutes else browse from incognito mode',
  'language_table' => 'Language table',
  'add_new_language' => 'Add new language',
  'Id' => 'Id',
  'Code' => 'Code',
  'new_language' => 'New language',
  'country_code' => 'Country code',
  'direction' => 'Direction',
  'status_updated_successfully' => 'Status updated successfully',
  'Are you sure to delete this' => 'Are you sure to delete this',
  'You will not be able to revert this' => 'You will not be able to revert this',
  'delete it' => 'Delete it',
  'default language can not be deleted! to delete change the default language first!' => 'Default language can not be deleted! to delete change the default language first!',
  'Translate' => 'Translate',
  'Dashboard' => 'Dashboard',
  'Language' => 'Language',
  'language_content_table' => 'Language content table',
  'SL#' => 'SL#',
  'auto_translate' => 'Auto translate',
  'text_updated_successfully' => 'Text updated successfully',
  'Key removed successfully' => 'Key removed successfully',
  'Key translated successfully' => 'Key translated successfully',
  'searching for deliverymen' => 'Searching for deliverymen',
  'This week' => 'This week',
  'This year' => 'This year',
  'Cooking' => 'Cooking',
  'most rated' => 'Most rated',
  'top rated' => 'Top rated',
  'top selling' => 'Top selling',
  'Grocery Stores' => 'Grocery Stores',
  'newly added' => 'Newly added',
  'available_delivery_man' => 'Available delivery man',
  'Available to assign more order' => 'Available to assign more order',
  'sale' => 'Sale',
  'new_restaurants' => 'New restaurants',
  'add new restaurant' => 'Add new restaurant',
  'Search Delivery Man ...' => 'Search Delivery Man ...',
  'Withdraw Requests' => 'Withdraw Requests',
  'Delivery Man Payments' => 'Delivery Man Payments',
  'Currently Active Delivery Men' => 'Currently Active Delivery Men',
  'View All Delivery Men' => 'View All Delivery Men',
  'User Overview' => 'User Overview',
  'Hello, here you can manage your users by zone.' => 'Hello  here you can manage your users by zone.',
  'Dispatch Overview' => 'Dispatch Overview',
  'Hello, here you can manage your dispatch management.' => 'Hello  here you can manage your dispatch management.',
  'Hello, here you can manage your dispatch orders.' => 'Hello  here you can manage your dispatch orders.',
  'Hello, here you can manage your' => 'Hello  here you can manage your',
  'orders by zone.' => 'Orders by zone.',
  'Hello, Here You Can Manage Your' => 'Hello  Here You Can Manage Your',
  'orders by Zone.' => 'Orders by Zone.',
  'Select Module & Monitor your business module wise' => 'Select Module & Monitor your business module wise',
  'Please, Enable or Create Module First' => 'Please  Enable or Create Module First',
  'Customer Statistics' => 'Customer Statistics',
  'Positive' => 'Positive',
  'Good' => 'Good',
  'Neutral' => 'Neutral',
  'Negetive' => 'Negetive',
  'Deliveryman Statistics' => 'Deliveryman Statistics',
  'pos section' => 'Pos section',
  'New Sale' => 'New Sale',
  'Promotion Management' => 'Promotion Management',
  'Product Setup' => 'Product Setup',
  'Company Information' => 'Company Information',
  'Currency Symbol' => 'Currency Symbol',
  'Currency Position' => 'Currency Position',
  'Default Commission on Order' => 'Default Commission on Order',
  'Commission on Delivery Charge' => 'Commission on Delivery Charge',
  'Company Location' => 'Company Location',
  'Logo & Icon' => 'Logo & Icon',
  'Office Opening & Closing' => 'Office Opening & Closing',
  'add_module' => 'Add module',
  'add_new_module' => 'Add new module',
  'default language can not be updated! to update change the default language first!' => 'Default language can not be updated! to update change the default language first!',
  'Vehicle_List' => 'Vehicle List',
  'vehicles_category_list' => 'Vehicles category list',
  'add_vehicle_category' => 'Add vehicle category',
  'Ex: Search by type...' => 'Ex: Search by type...',
  'Type' => 'Type',
  'Starting_coverage_area' => 'Starting coverage area',
  'Maximum_coverage_area' => 'Maximum coverage area',
  'Extra_charges' => 'Extra charges',
  'vehicles_category' => 'Vehicles category',
  'Add new Vehicle' => 'Add new Vehicle',
  'Vehicle' => 'Vehicle',
  'extra_charges' => 'Extra charges',
  'This amount will be added with delivery charge' => 'This amount will be added with delivery charge',
  'starting_coverage_area' => 'Starting coverage area',
  'minimum_coverage_area_hint' => 'Minimum coverage area hint',
  'maximum_coverage_area' => 'Maximum coverage area',
  'maximum_coverage_area_hint' => 'Maximum coverage area hint',
  'Vehicle_created' => 'Vehicle created',
  'vehicle' => 'Vehicle',
  'vehicle view' => 'Vehicle view',
  'vehicle_type' => 'Vehicle type',
  'Vehicle Information' => 'Vehicle Information',
  'No_vehicle_data_found' => 'No vehicle data found',
  'select_a_vehicle' => 'Select a vehicle',
  'Vehicle_Type' => 'Vehicle Type',
  'Vehicle_Extra_Charges' => 'Vehicle Extra Charges',
  'Vehicle_minimum_coverage_area' => 'Vehicle minimum coverage area',
  'Vehicle_maximum_coverage_area' => 'Vehicle maximum coverage area',
  'Maximum delivery charge' => 'Maximum delivery charge',
  'maximum delivery charge' => 'Maximum delivery charge',
  'Max_Number_of_Otp_Hits_in_a_Row' => 'Max Number of Otp Hits in a Row',
  'otp_resend_interval_time' => 'Otp resend interval time',
  'seconds' => 'Seconds',
  'you_want_to_confirm_this_store' => 'You want to confirm this store',
  'Approve' => 'Approve',
  'you_want_to_reject_this_store' => 'You want to reject this store',
  'Deny' => 'Deny',
  'not_approved' => 'Not approved',
  'New message arrived' => 'New message arrived',
  '--' => '--',
  'Food Setup' => 'Food Setup',
  'Recommended' => 'Recommended',
  'Recommend_to_customers' => 'Recommend to customers',
  'Item recommendation updated!' => 'Item recommendation updated!',
  'order_cancellation_reasons' => 'Order cancellation reasons',
  'add_an_order_cancellation_reason' => 'Add an order cancellation reason',
  'select_user_type' => 'Select user type',
  'add_reason' => 'Add reason',
  'order_cancellation_reason_list' => 'Order cancellation reason list',
  'order_cancellation_reason' => 'Order cancellation reason',
  'Save_changes' => 'Save changes',
  'Order Type' => 'Order Type',
  'select_cancellation_reason' => 'Select cancellation reason',
  'order_cancellation_reason_added_successfully' => 'Order cancellation reason added successfully',
  'want_to_delete_this_order_cancellation_reason' => 'Want to delete this order cancellation reason',
  'Cancelled_By' => 'Cancelled By',
  'This report shows the delivery fee for all orders whose delivery fee is free for using "free delivery over" or "free delivery coupon".' => 'This report shows the delivery fee for all orders whose delivery fee is free for using "free delivery over" or  "free delivery coupon" .',
  'Report' => 'Report',
  'vendor_expense_report_discription' => 'Vendor expense report discription',
  'Search by title or code' => 'Search by title or code',
  'No data to show' => 'No data to show',
  'Taxi' => 'Taxi',
  'Taxi Rent' => 'Taxi Rent',
  'Coverage_area_overlapped' => 'Coverage area overlapped',
  'update_vehicle_category' => 'Update vehicle category',
  'Vehicle_updated' => 'Vehicle updated',
  'add_fund_by_customer' => 'Add fund by customer',
  'refund_order' => 'Refund order',
  'admin_bonus' => 'Admin bonus',
  'maximum_shipping_charge' => 'Maximum shipping charge',
  'app_min_version_hint' => 'This version will compare with the APP_VERSION variable available in app_constants.dart file (File path: lib/util/app_constants.dart)',
  'Download_Url' => 'Download Url',
  'deliveryman_app_section' => 'Deliveryman app section',
  'for_deliveryman' => 'For deliveryman',
  'store_app_section' => 'Store app section',
  'for_store' => 'For store',
  'discount_on_product' => 'Discount on product',
  'Vehicle_category_updated' => 'Vehicle category updated',
  'Vehicle_category_created' => 'Vehicle category created',
  'minimum_coverage_area' => 'Minimum coverage area',
  'campaign_is_expired' => 'Campaign is expired',
  'It will add a limite on total delivery charge.' => 'It will add a limite on total delivery charge.',
  'Maximum delivery charge must be greater than minimum delivery charge.' => 'Maximum delivery charge must be greater than minimum delivery charge.',
  'This value is the miximum distance for a vehicle in this category to serve an order.
                                    ' => 'This value is the miximum distance for a vehicle in this category to serve an order.
                                    ',
  'This value is the miximum distance for a vehicle in this category to serve an order.
                                        ' => 'This value is the miximum distance for a vehicle in this category to serve an order.
                                        ',
  'maximum_coverage' => 'Maximum coverage',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over, store discount, Coupon discount & food discounts(partial according to order commission).' => 'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & food discounts(partial according to order commission).',
  'Update Coupon' => 'Update Coupon',
  'This report will show all the orders in which the store discount has been used. The restaurant discounts are: Free delivery, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the store discount has been used. The restaurant discounts are: Free delivery  Coupon discount & item discounts(partial according to order commission).',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over, store discount, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & item discounts(partial according to order commission).',
  'low_stock_report' => 'Low stock report',
  'react_landing_page' => 'React landing page',
  'button_text' => 'Button text',
  'Ex: Button text' => 'Ex: Button text',
  'Ex: Link' => 'Ex: Link',
  'React Landing Page' => 'React Landing Page',
  'React Landing Page Features' => 'React Landing Page Features',
  'Header Section Banner' => 'Header Section Banner',
  'Change Image' => 'Change Image',
  'App Section Image' => 'App Section Image',
  'Footer Logo' => 'Footer Logo',
  'Hero Section' => 'Hero Section',
  'heading' => 'Heading',
  'Ex: Heading' => 'Ex: Heading',
  'slogan' => 'Slogan',
  'Ex: Slogan' => 'Ex: Slogan',
  'Short Description' => 'Short Description',
  'Ex: Short Description' => 'Ex: Short Description',
  'Delivery Service Section' => 'Delivery Service Section',
  'Title' => 'Title',
  'Ex: Service title' => 'Ex: Service title',
  'Description' => 'Description',
  'Ex: Description' => 'Ex: Description',
  '1352 X 250 px' => '1352 X 250 px',
  'React Landing Page Main Banner Image' => 'React Landing Page Main Banner Image',
  'Main Banner' => 'Main Banner',
  'Banner Title' => 'Banner Title',
  'Ex: Banner title' => 'Ex: Banner title',
  'Banner Sub Title' => 'Banner Sub Title',
  'Ex: Banner sub title' => 'Ex: Banner sub title',
  'Full Banner Image' => 'Full Banner Image',
  'React Landing Page Discount Banner Image' => 'React Landing Page Discount Banner Image',
  'Discount Banner' => 'Discount Banner',
  'Discount Banner Title' => 'Discount Banner Title',
  'Ex: Discount banner title' => 'Ex: Discount banner title',
  'Discount Banner Sub Title' => 'Discount Banner Sub Title',
  'Ex: Discount banner sub title' => 'Ex: Discount banner sub title',
  'Discount Banner Image' => 'Discount Banner Image',
  'Half Banner Images' => 'Half Banner Images',
  'Banner' => 'Banner',
  'Banner Image' => 'Banner Image',
  '668 X 250 px' => '668 X 250 px',
  'Ex: Feature title' => 'Ex: Feature title',
  'Ex: Feature description' => 'Ex: Feature description',
  '140 X 140 px' => '140 X 140 px',
  'Max_Number_of_Otp_Verification_in_a_Row' => 'Max Number of Otp Verification in a Row',
  'otp_verification_interval_time' => 'Otp verification interval time',
  'Gst No' => 'Gst No',
  'apple' => 'Apple',
  'team_id' => 'Team id',
  'key_id' => 'Key id',
  'apple_api_set_instruction' => 'Apple api set instruction',
  'goto_the_apple_developer_page' => 'Goto the apple developer page',
  'Service Id file' => 'Service Id file',
  'Check Service Id file' => 'Check Service Id file',
  'File Exists' => 'File Exists',
  'File not found' => 'File not found',
  'redirect_url' => 'Redirect url',
  'service_file' => 'Service file',
  '(Already Exists)' => '(Already Exists)',
  'Go to Apple Developer page' => 'Go to Apple Developer page',
  'Here in top left corner you can see the' => 'Here in top left corner you can see the',
  'Team ID' => 'Team ID',
  '[Apple_Deveveloper_Account_Name - Team_ID]' => '[Apple Deveveloper Account Name - Team ID]',
  'Click Plus icon -> select App IDs -> click on Continue' => 'Click Plus icon -  select App IDs -  click on Continue',
  'Put a description and also identifier (identifier that used for app) and this is the' => 'Put a description and also identifier (identifier that used for app) and this is the',
  'Client ID' => 'Client ID',
  'Click Continue and Download the file in device named AuthKey_ID.p8 (Store it safely and it is used for push notification)' => 'Click Continue and Download the file in device named AuthKey ID.p8 (Store it safely and it is used for push notification)',
  'Again click Plus icon -> select Service IDs -> click on Continue' => 'Again click Plus icon -  select Service IDs -  click on Continue',
  'Push a description and also identifier and Continue' => 'Push a description and also identifier and Continue',
  'Download the file in device named' => 'Download the file in device named',
  'AuthKey_KeyID.p8' => 'AuthKey KeyID.p8',
  '[This is the Service Key ID file and also after AuthKey_ that is the Key ID]' => '[This is the Service Key ID file and also after AuthKey  that is the Key ID]',
  'This value is the minimum distance for a vehicle in this category to serve an order.' => 'This value is the minimum distance for a vehicle in this category to serve an order.',
  'This value is the miximum distance for a vehicle in this category to serve an order.
                                            ' => 'This value is the miximum distance for a vehicle in this category to serve an order.
                                            ',
  'Identity Documents' => 'Identity Documents',
  'Identity_Image' => 'Identity Image',
  'Registration request successfull' => 'Registration request successfull',
  'Thank you for the joinning request on' => 'Thank you for the joinning request on',
  'Your registration request is now pending' => 'Your registration request is now pending',
  'Please wait untill admin aprroave your request' => 'Please wait untill admin aprroave your request',
  'If you require any assistance or have feedback or suggestions about our site, you can email us at' => 'If you require any assistance or have feedback or suggestions about our site  you can email us at',
  'can_not_add_both_food_and_restaurant_at_same_time' => 'Can not add both food and restaurant at same time',
  'login_to_your_panel' => 'Login to your panel',
  'select_your_role_&_login' => 'Select your role & login',
  'Verify' => 'Verify',
  'Change Password' => 'Change Password',
  'New Password' => 'New Password',
  'Confirm Password' => 'Confirm Password',
  'password_changed_successfully' => 'Password changed successfully',
  'select_role' => 'Select role',
  'admin_employee' => 'Admin employee',
  'store_employee' => 'Store employee',
  'Please_enter_a_valid_email_address.' => 'Please enter a valid email address.',
  'Forget Password' => 'Forget Password',
  'Send_Mail_to_Your_Email' => 'Send Mail to Your Email',
  'A mail will be send to your registered email with a  link to change passowrd' => 'A mail will be send to your registered email with a  link to change passowrd',
  'Send Mail' => 'Send Mail',
  'A mail has been sent to your registered email' => 'A mail has been sent to your registered email',
  'Click the link in the mail description to change password' => 'Click the link in the mail description to change password',
  'dm_cancel_order_hint' => 'Dm cancel order hint',
  'You_must_select_your_role_before_login' => 'You must select your role before login',
  'Password_must_be_at_least_8_character_long' => 'Password must be at least 8 character long',
  'You_password_must_contain_at_least_one_uppercase_and_one_lowercase_letter' => 'You password must contain at least one uppercase and one lowercase letter',
  'Password_must_contain_at_least_one_letter' => 'Password must contain at least one letter',
  'Password_must_contain_at_least_one_number' => 'Password must contain at least one number',
  'Password_must_contain_at_least_one_symbols' => 'Password must contain at least one symbols',
  'Your_password_has_appeared_in_a_data_leak._Please_choose_a_different_password' => 'Your password has appeared in a data leak. Please choose a different password',
  'The password must contain at least one uppercase and one lowercase letter.' => 'The password must contain at least one uppercase and one lowercase letter.',
  'The password must contain at least one letter.' => 'The password must contain at least one letter.',
  'The password must contain at least one symbol.' => 'The password must contain at least one symbol.',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter,_and_at_least_8_or_more_characters' => 'Must contain at least one number and one uppercase and lowercase letter  and at least 8 or more characters',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter_and_symbol,_and_at_least_8_or_more_characters' => 'Must contain at least one number and one uppercase and lowercase letter and symbol  and at least 8 or more characters',
  'Ex: 8+ Character' => 'Ex: 8+ Character',
  'The password and confirm password must match.' => 'The password and confirm password must match.',
  'The given password has appeared in a data leak. Please choose a different password.' => 'The given password has appeared in a data leak. Please choose a different password.',
  'The confirm password field is required.' => 'The confirm password field is required.',
  'The phone must be at least 10 characters.' => 'The phone must be at least 10 characters.',
  'Save' => 'Save',
  'link_expired' => 'Link expired',
  'maximum_delivery_charge' => 'Maximum delivery charge',
  'Credentials does not match.' => 'Credentials does not match.',
  'language_list' => 'Language list',
  'english_value' => 'English value',
  'translated_value' => 'Translated value',
  'minimum_delivery_time_should_be_more_than_10_min' => 'Minimum delivery time should be more than 10 min',
  'Password_Reset' => 'Password Reset',
  'Change_password_request' => 'Change password request',
  'hi' => 'Hi',
  'Please_click' => 'Please click',
  'here
                    ' => 'Here
                    ',
  'or_click_the_link_below_to_change_your_password' => 'Or click the link below to change your password',
  'Please_contact_us_for_any_queries_,_we’re_always_happy_to_help.' => 'Please contact us for any queries   we’re always happy to help.',
  'Thanks_&_Regards_,' => 'Thanks & Regards  ',
  '* When this discount is available, is applied on all the items in this stores.' => '* When this discount is available  is applied on all the items in this stores.',
  'duplicate_email_or_phone_exists_at_the_database' => 'Duplicate email or phone exists at the database',
  'food_variations_generator' => 'Food variations generator',
  'generate' => 'Generate',
  'Import' => 'Import',
  'You_want_to_' => 'You want to ',
  'Please click save information button below to save all the changes' => 'Please click save information button below to save all the changes',
  'Customer Can Earn & Buy From Wallet' => 'Customer Can Earn & Buy From Wallet',
  'lorem_ipsum_hint' => 'Lorem ipsum hint',
  'Referral Earning' => 'Referral Earning',
  'Customer Can Earn & Buy From Referral' => 'Customer Can Earn & Buy From Referral',
  'Earning Per Referral ($)' => 'Earning Per Referral ($)',
  'Customer Verification' => 'Customer Verification',
  'Loyalty Point' => 'Loyalty Point',
  'Customer Can Earn Loyalty Point' => 'Customer Can Earn Loyalty Point',
  'Loyalty Point Earn Per Order (%)' => 'Loyalty Point Earn Per Order (%)',
  'Minimum Loyalty Point Required to Transfer' => 'Minimum Loyalty Point Required to Transfer',
  'Save Information' => 'Save Information',
  'By Turning OFF ' => 'By Turning OFF ',
  'Refund to Wallet' => 'Refund to Wallet',
  'Option' => 'Option',
  'Customer will no see wallet option from his profile settings' => 'Customer will no see wallet option from his profile settings',
  'By Turning ON ' => 'By Turning ON ',
  'Customer will see wallet option in his profile settings & can earn & send money from his wallet money' => 'Customer will see wallet option in his profile settings & can earn & send money from his wallet money',
  'Ok' => 'Ok',
  'Cancel' => 'Cancel',
  'Wallet Earning' => 'Wallet Earning',
  'Customer will not get any option to share code and earn' => 'Customer will not get any option to share code and earn',
  'Customer can share his refer code to other & when other people will login while using this code & place their first order delivered. customer will earn wallet money.' => 'Customer can share his refer code to other & when other people will login while using this code & place their first order delivered. customer will earn wallet money.',
  'Customer will no see loyalty point option from his profile settings' => 'Customer will no see loyalty point option from his profile settings',
  'Customer will see loyalty point option in his profile settings & can earn & convert this point to wallet money' => 'Customer will see loyalty point option in his profile settings & can earn & convert this point to wallet money',
  'Customers do not need to verify their number through OTP while sign up. They can directly create account.' => 'Customers do not need to verify their number through OTP while sign up. They can directly create account.',
  'Customers have verify their number through OTP while sign up' => 'Customers have verify their number through OTP while sign up',
  'Show Earnings in App' => 'Show Earnings in App',
  'Maximum Assigned Order Limit' => 'Maximum Assigned Order Limit',
  'Delivery Man Tips' => 'Delivery Man Tips',
  'Customer will not be able give tips from checkout page to the delivery man while placing order' => 'Customer will not be able give tips from checkout page to the delivery man while placing order',
  'Customer will be able give tips from checkout page to the delivery man while placing order' => 'Customer will be able give tips from checkout page to the delivery man while placing order',
  'Show Earnings in Apps' => 'Show Earnings in Apps',
  'If this feature is off, delivery man can’t see his earning from each order in order details from his app' => 'If this feature is off  delivery man can’t see his earning from each order in order details from his app',
  'If this feature is on, delivery man can see his earning from each order in order details from his app' => 'If this feature is on  delivery man can see his earning from each order in order details from his app',
  'Delivery Man Self Registration' => 'Delivery Man Self Registration',
  'User can’t register  him self as a delivery man from user website  or Delivery Man App. He has to contact admin & request create an account for him' => 'User can’t register  him self as a delivery man from user website  or Delivery Man App. He has to contact admin & request create an account for him',
  'User can register  him self as a delivery man from user website  or Delivery Man App' => 'User can register  him self as a delivery man from user website  or Delivery Man App',
  'Store Self Registration' => 'Store Self Registration',
  'User can’t register to open a store all by him self from user website or Store App . He has to contact admin & request to open a store' => 'User can’t register to open a store all by him self from user website or Store App . He has to contact admin & request to open a store',
  'User can register to open a store all by him self from user website or Store App ' => 'User can register to open a store all by him self from user website or Store App ',
  'lorem ipsum' => 'Lorem ipsum',
  'Takeaway' => 'Takeaway',
  'Schedule Order Time Interval' => 'Schedule Order Time Interval',
  'Order Delivery Verification' => 'Order Delivery Verification',
  'Customer will not get any code and delivery man does not need the code to complete the order process' => 'Customer will not get any code and delivery man does not need the code to complete the order process',
  'While delivery man handover the order to customer, a OTP will be send to customer website / app. The delivery man have to use the code to complete delivery process.' => 'While delivery man handover the order to customer  a OTP will be send to customer website / app. The delivery man have to use the code to complete delivery process.',
  'Order Using Prescription' => 'Order Using Prescription',
  'place order by uploading prescription will be off in customer app / website' => 'Place order by uploading prescription will be off in customer app / website',
  'While order from pharmacy module , customer can just upload prescription and place order' => 'While order from pharmacy module   customer can just upload prescription and place order',
  'Customer will not able to use home delivery option' => 'Customer will not able to use home delivery option',
  'Customer will able to use home delivery option' => 'Customer will able to use home delivery option',
  'Customer will not able to use takeaway option.' => 'Customer will not able to use takeaway option.',
  'Customer will able to use takeaway option.' => 'Customer will able to use takeaway option.',
  'Schedule Order' => 'Schedule Order',
  'After turning off this field customers won’t able to use schedule order.' => 'After turning off this field customers won’t able to use schedule order.',
  'After turning on this field customers are able to use schedule order.' => 'After turning on this field customers are able to use schedule order.',
  'Order Cancelation Messages' => 'Order Cancelation Messages',
  'Order Cancellation Reason' => 'Order Cancellation Reason',
  'User Type' => 'User Type',
  '3:1' => '3:1',
  'Favicon' => 'Favicon',
  '1:1' => '1:1',
  'General Settings' => 'General Settings',
  'Left' => 'Left',
  'Right' => 'Right',
  'Copyright Text' => 'Copyright Text',
  'business settings' => 'Business settings',
  'Veg/Non Veg' => 'Veg/Non Veg',
  'Store & Customer both will not see veg / non-veg tag with food and can’t search according to the tags from website & apps' => 'Store & Customer both will not see veg / non-veg tag with food and can’t search according to the tags from website & apps',
  'Store & Customer both can see veg / non-veg tag with food and can search according to the tags from website & apps' => 'Store & Customer both can see veg / non-veg tag with food and can search according to the tags from website & apps',
  'Include Tax Amount' => 'Include Tax Amount',
  'Tax will be shown separately from product / item prices' => 'Tax will be shown separately from product / item prices',
  'Product / item price will be show as the summation of product price & tax' => 'Product / item price will be show as the summation of product price & tax',
  'Free Delivery Over' => 'Free Delivery Over',
  'Free delivery over a certain order amount feature will not work' => 'Free delivery over a certain order amount feature will not work',
  'After a certain order amount decided by admin , the delivery charge will be consider free for customer. & The delivery fee will be deduct from admin wallet' => 'After a certain order amount decided by admin   the delivery charge will be consider free for customer. & The delivery fee will be deduct from admin wallet',
  'Confirmation' => 'Confirmation',
  'Are you sure you want to turn on self-registration for stores?' => 'Are you sure you want to turn on self-registration for stores ',
  'store_setup' => 'Store setup',
  'order_cancellation_reason_updated_successfully' => 'Order cancellation reason updated successfully',
  'Payment Methods' => 'Payment Methods',
  'SMS Module' => 'SMS Module',
  'Mail Config' => 'Mail Config',
  'Map APIs' => 'Map APIs',
  'Social Logins' => 'Social Logins',
  'Recaptcha' => 'Recaptcha',
  'Without configuring this section map functionality will not work properly. Thus the whole system will not work as it planned' => 'Without configuring this section map functionality will not work properly. Thus the whole system will not work as it planned',
  'By Turning OFF Cash On Delivery Option' => 'By Turning OFF Cash On Delivery Option',
  'Customers will not be able to select COD as a payment method during checkout. Please review your settings and enable COD if you wish to offer this payment option to customers.' => 'Customers will not be able to select COD as a payment method during checkout. Please review your settings and enable COD if you wish to offer this payment option to customers.',
  'Send Test Mail' => 'Send Test Mail',
  'How it Works' => 'How it Works',
  'Turn OFF' => 'Turn OFF',
  '*By Turning OFF mail configuration, all your mailing services will be off.' => '*By Turning OFF mail configuration  all your mailing services will be off.',
  'Important!' => 'Important!',
  'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.' => 'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.',
  'Warning!' => 'Warning!',
  'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.' => 'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.',
  'Congratulations! Your SMTP mail has been setup successfully!' => 'Congratulations! Your SMTP mail has been setup successfully!',
  'Go to test mail to check that its work perfectly or not!' => 'Go to test mail to check that its work perfectly or not!',
  'Send a Test Mail to Your Email ? ' => 'Send a Test Mail to Your Email   ',
  'A test mail will be send to your email to confirm it works perfectly.' => 'A test mail will be send to your email to confirm it works perfectly.',
  'Find SMTP Server Details' => 'Find SMTP Server Details',
  'Contact your email service provider or IT administrator to obtain the SMTP server details, such as hostname, port, username, and password.' => 'Contact your email service provider or IT administrator to obtain the SMTP server details  such as hostname  port  username  and password.',
  'Note: If you\'re not sure where to find these details, check the email provider\'s documentation or support resources for guidance.' => 'Note: If you re not sure where to find these details  check the email provider s documentation or support resources for guidance.',
  'Configure SMTP Settings' => 'Configure SMTP Settings',
  'Go to the SMTP mail setup page in the admin panel.' => 'Go to the SMTP mail setup page in the admin panel.',
  'Enter the obtained SMTP server details, including the hostname, port, username, and password.' => 'Enter the obtained SMTP server details  including the hostname  port  username  and password.',
  'Choose the appropriate encryption method (e.g., SSL, TLS) if required. Save the settings.' => 'Choose the appropriate encryption method (e.g.  SSL  TLS) if required. Save the settings.',
  'Test SMTP Connection' => 'Test SMTP Connection',
  'Click on the "Send Test Mail" button to verify the SMTP connection.' => 'Click on the  Send Test Mail  button to verify the SMTP connection.',
  'If successful, you will see a confirmation message indicating that the connection is working fine.' => 'If successful  you will see a confirmation message indicating that the connection is working fine.',
  'If not, double-check your SMTP settings and try again.' => 'If not  double-check your SMTP settings and try again.',
  'Note: If you\'re unsure about the SMTP settings, contact your email service provider or IT administrator for assistance.' => 'Note: If you re unsure about the SMTP settings  contact your email service provider or IT administrator for assistance.',
  'Enable Mail Configuration' => 'Enable Mail Configuration',
  'If the SMTP connection test is successful, you can now enable the mail configuration services by toggling the switch to "ON."' => 'If the SMTP connection test is successful  you can now enable the mail configuration services by toggling the switch to  ON. ',
  'This will allow the system to send emails using the configured SMTP settings.' => 'This will allow the system to send emails using the configured SMTP settings.',
  'Got It' => 'Got It',
  'Google Map API Setup' => 'Google Map API Setup',
  'Without configuring this section map functionality will not work properly. Thus the whole
                                 system will not work as it planned' => 'Without configuring this section map functionality will not work properly. Thus the whole
                                 system will not work as it planned',
  'Please go to settings and select module for this zone' => 'Please go to settings and select module for this zone',
  'Otherwise this zone won\'t function properly & will work show anything against this zone' => 'Otherwise this zone won t function properly & will work show anything against this zone',
  'Facebook Login Turned Off' => 'Facebook Login Turned Off',
  'Facebook is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'Facebook is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.',
  'reCAPTCHA is now enabled for added security. Users may be prompted to complete a reCAPTCHA challenge to verify their human identity and protect against spam and malicious activity.' => 'ReCAPTCHA is now enabled for added security. Users may be prompted to complete a reCAPTCHA challenge to verify their human identity and protect against spam and malicious activity.',
  'Credential Setup' => 'Credential Setup',
  'Reset' => 'Reset',
  'Disabling reCAPTCHA may leave your website vulnerable to spam and malicious activity and suspects that a user may be a bot. It is highly recommended to keep reCAPTCHA enabled to ensure the security and integrity of your website.' => 'Disabling reCAPTCHA may leave your website vulnerable to spam and malicious activity and suspects that a user may be a bot. It is highly recommended to keep reCAPTCHA enabled to ensure the security and integrity of your website.',
  'Push Notification' => 'Push Notification',
  'Firebase Configuration' => 'Firebase Configuration',
  'Read Documentation' => 'Read Documentation',
  'Where to get this information' => 'Where to get this information',
  '*Select Module Here' => '*Select Module Here',
  'Write your message' => 'Write your message',
  'Ex: AAAAaBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789' => 'Ex: AAAAaBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789',
  'Ex: abcd1234efgh5678ijklmnop90qrstuvwxYZ' => 'Ex: abcd1234efgh5678ijklmnop90qrstuvwxYZ',
  'Ex: my-awesome-app-12345' => 'Ex: my-awesome-app-12345',
  'Ex: my-awesome-app.firebaseapp.com' => 'Ex: my-awesome-app.firebaseapp.com',
  'Ex: my-awesome-app.appspot.com' => 'Ex: my-awesome-app.appspot.com',
  'Ex: 1234567890' => 'Ex: 1234567890',
  'Ex: 9876543210' => 'Ex: 9876543210',
  'Ex: F-12345678' => 'Ex: F-12345678',
  'Go to Firebase Console' => 'Go to Firebase Console',
  'Open your web browser and go to the Firebase Console' => 'Open your web browser and go to the Firebase Console',
  '(https://console.firebase.google.com/)' => '(https://console.firebase.google.com/)',
  'Select the project for which you want to configure FCM from the Firebase Console dashboard.' => 'Select the project for which you want to configure FCM from the Firebase Console dashboard.',
  'Navigate to Project Settings' => 'Navigate to Project Settings',
  'In the left-hand menu, click on the "Settings" gear icon, and then select "Project settings" from the dropdown.' => 'In the left-hand menu  click on the  Settings  gear icon  and then select  Project settings  from the dropdown.',
  'In the Project settings page, click on the "Cloud Messaging" tab from the top menu.' => 'In the Project settings page  click on the  Cloud Messaging  tab from the top menu.',
  'Obtain All The Information Asked!' => 'Obtain All The Information Asked!',
  'In the Firebase Project settings page, click on the "General" tab from the top menu.' => 'In the Firebase Project settings page  click on the  General  tab from the top menu.',
  'Under the "Your apps" section, click on the "Web" app for which you want to configure FCM.' => 'Under the  Your apps  section  click on the  Web  app for which you want to configure FCM.',
  'Then Obtain API Key, FCM Project ID, Auth Domain, Storage Bucket, Messaging Sender ID.' => 'Then Obtain API Key  FCM Project ID  Auth Domain  Storage Bucket  Messaging Sender ID.',
  'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation, terms of service, and any applicable laws and regulations.' => 'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation  terms of service  and any applicable laws and regulations.',
  'Please Visit the Docs to Set FCM on Mobile Apps' => 'Please Visit the Docs to Set FCM on Mobile Apps',
  'Please check the documentation below for detailed instructions on setting up your mobile app to receive Firebase Cloud Messaging (FCM) notifications.' => 'Please check the documentation below for detailed instructions on setting up your mobile app to receive Firebase Cloud Messaging (FCM) notifications.',
  'Click Here' => 'Click Here',
  'Facebook is Disabled!' => 'Facebook is Disabled!',
  'Customers will be able to select COD as a payment method during checkout. Please review your settings to ensure proper configuration and availability of COD as a payment option.' => 'Customers will be able to select COD as a payment method during checkout. Please review your settings to ensure proper configuration and availability of COD as a payment option.',
  'Facebook is Enabled' => 'Facebook is Enabled',
  'Turned ON ' => 'Turned ON ',
  'Turned OFF ' => 'Turned OFF ',
  'is now enabled. Customers will be able to sign up or log in using their social media accounts.' => 'Is now enabled. Customers will be able to sign up or log in using their social media accounts.',
  'is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'Is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.',
  'Login Turned ON ' => 'Login Turned ON ',
  'Login Turned OFF ' => 'Login Turned OFF ',
  'Login is now enabled. Customers will be able to sign up or log in using their social media accounts.' => 'Login is now enabled. Customers will be able to sign up or log in using their social media accounts.',
  'Login is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'Login is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.',
  'Customer will not see wallet option from his profile settings' => 'Customer will not see wallet option from his profile settings',
  'How the Setting Works' => 'How the Setting Works',
  'Check how the settings works' => 'Check how the settings works',
  'View Image' => 'View Image',
  'Copy Link' => 'Copy Link',
  'Copy Path' => 'Copy Path',
  'User App Version Control' => 'User App Version Control',
  'For android' => 'For android',
  'For iOS' => 'For iOS',
  'What is App Version ?' => 'What is App Version  ',
  'App Download Link' => 'App Download Link',
  'By Turning OFF Order ' => 'By Turning OFF Order ',
  'Pending Message' => 'Pending Message',
  'User can\'t get a clear message to know that order is pending or not' => 'User can t get a clear message to know that order is pending or not',
  'By Turning ON Order ' => 'By Turning ON Order ',
  'User will get a clear message to know that order is pending' => 'User will get a clear message to know that order is pending',
  'User can`t get a clear message to know that order is pending or not' => 'User can`t get a clear message to know that order is pending or not',
  'User can not get a clear message to know that order is pending or not' => 'User can not get a clear message to know that order is pending or not',
  'pending Message' => 'Pending Message',
  'confirmation Message' => 'Confirmation Message',
  'User will get a clear message to know that order is confirmed' => 'User will get a clear message to know that order is confirmed',
  'User can not get a clear message to know that order is confirmed or not' => 'User can not get a clear message to know that order is confirmed or not',
  'processing Message' => 'Processing Message',
  'User will get a clear message to know that order is processing' => 'User will get a clear message to know that order is processing',
  'User can not get a clear message to know that order is processing or not' => 'User can not get a clear message to know that order is processing or not',
  'Order Handover Message' => 'Order Handover Message',
  'User will get a clear message to know that order is handovered' => 'User will get a clear message to know that order is handovered',
  'User can not get a clear message to know that order is handovered or not' => 'User can not get a clear message to know that order is handovered or not',
  'Out For Delivery Message' => 'Out For Delivery Message',
  'User will get a clear message to know that order is out for delivery' => 'User will get a clear message to know that order is out for delivery',
  'User can not get a clear message to know that order is out for delivery or not' => 'User can not get a clear message to know that order is out for delivery or not',
  'delivered Message' => 'Delivered Message',
  'User will get a clear message to know that order is delivered' => 'User will get a clear message to know that order is delivered',
  'User can not get a clear message to know that order is delivered or not' => 'User can not get a clear message to know that order is delivered or not',
  'Delivery Man Assigned Message' => 'Delivery Man Assigned Message',
  'User will get a clear message to know that order is delivery man assigned' => 'User will get a clear message to know that order is delivery man assigned',
  'User can not get a clear message to know that order is delivery man assigned or not' => 'User can not get a clear message to know that order is delivery man assigned or not',
  'User will get a clear message to know that order is assigned to delivery man' => 'User will get a clear message to know that order is assigned to delivery man',
  'User can not get a clear message to know that order is assigned to delivery man or not' => 'User can not get a clear message to know that order is assigned to delivery man or not',
  'Delivery Man Delivered Message' => 'Delivery Man Delivered Message',
  'User will get a clear message to know that order is delivered by delivery man' => 'User will get a clear message to know that order is delivered by delivery man',
  'User can not get a clear message to know that order is delivered by delivery man or not' => 'User can not get a clear message to know that order is delivered by delivery man or not',
  'canceled Message' => 'Canceled Message',
  'User will get a clear message to know that order is canceled' => 'User will get a clear message to know that order is canceled',
  'User can not get a clear message to know that order is canceled or not' => 'User can not get a clear message to know that order is canceled or not',
  'Order Refund Message' => 'Order Refund Message',
  'User will get a clear message to know that order is refunded' => 'User will get a clear message to know that order is refunded',
  'User can not get a clear message to know that order is refunded or not' => 'User can not get a clear message to know that order is refunded or not',
  'Refund Request Cancel Message' => 'Refund Request Cancel Message',
  'User will get a clear message to know that orders refund request canceled' => 'User will get a clear message to know that orders refund request canceled',
  'User can not get a clear message to know that orders refund request canceled or not' => 'User can not get a clear message to know that orders refund request canceled or not',
  'Customers will be able to select COD as a payment method during checkout.' => 'Customers will be able to select COD as a payment method during checkout.',
  'By Turning ON Cash On Delivery Option' => 'By Turning ON Cash On Delivery Option',
  'By Turning ON Digital Payment Option' => 'By Turning ON Digital Payment Option',
  'By Turning OFF Digital Payment Option' => 'By Turning OFF Digital Payment Option',
  'Customers will not be able to select digital payment as a payment method during checkout. Please review your settings and enable digital payment if you wish to offer this payment option to customers.' => 'Customers will not be able to select digital payment as a payment method during checkout. Please review your settings and enable digital payment if you wish to offer this payment option to customers.',
  'Customers will be able to select digital payment as a payment method during checkout.' => 'Customers will be able to select digital payment as a payment method during checkout.',
  'Don\'t show this anymore' => 'Don t show this anymore',
  'I will do it later' => 'I will do it later',
  'Go to the Settings' => 'Go to the Settings',
  'By switching the status to “ON”,  this zone and under all the functionality of this zone will be turned on' => 'By switching the status to “ON”   this zone and under all the functionality of this zone will be turned on',
  'In the user app & website all stores & products  already assigned under this zone will show to the customers' => 'In the user app & website all stores & products  already assigned under this zone will show to the customers',
  'By switching the status off, this zone and under all the functionality of this zone will be turned off' => 'By switching the status off  this zone and under all the functionality of this zone will be turned off',
  'In the user app & website all stores & products  already assigned under this zone will show to the customers.' => 'In the user app & website all stores & products  already assigned under this zone will show to the customers.',
  'In the user app & website no stores & products  already assigned under this zon this zone will not show to the customers.' => 'In the user app & website no stores & products  already assigned under this zon this zone will not show to the customers.',
  'Add New Module' => 'Add New Module',
  'How does it works ?' => 'How does it works  ',
  'Module Status' => 'Module Status',
  'is now enabled. You can use its features and functionality.' => 'Is now enabled. You can use its features and functionality.',
  'currently disabled. You can enable it in the settings to access its features and functionality.' => 'Currently disabled. You can enable it in the settings to access its features and functionality.',
  'module is now enabled. You can use its features and functionality.' => 'Module is now enabled. You can use its features and functionality.',
  'module currently disabled. You can enable it in the settings to access its features and functionality.' => 'Module currently disabled. You can enable it in the settings to access its features and functionality.',
  'admin_landing_page' => 'Admin landing page',
  'admin_landing_pages' => 'Admin landing pages',
  'fixed_data' => 'Fixed data',
  'promotional_section' => 'Promotional section',
  'feature_list' => 'Feature list',
  'earn_money' => 'Earn money',
  'download_apps' => 'Download apps',
  'testimonials' => 'Testimonials',
  'contact_us_page' => 'Contact us page',
  'header_section' => 'Header section',
  'title_here...' => 'Title here...',
  'Sub Title' => 'Sub Title',
  'sub_title_here...' => 'Sub title here...',
  'module_list_section' => 'Module list section',
  'NB: Client key should have enable map javascript api and you can restrict it with http refere. Server key should have enable place api key and you can restrict it with ip. You can use same api for both field without any restrictions.' => 'NB: Client key should have enable map javascript api and you can restrict it with http refere. Server key should have enable place api key and you can restrict it with ip. You can use same api for both field without any restrictions.',
  'Referral & Earning' => 'Referral & Earning',
  'To Change the illustrations & primary colour please change primary colour according to the ' => 'To Change the illustrations & primary colour please change primary colour according to the ',
  '(size: 3:1)' => '(size: 3:1)',
  'Promotional Banners' => 'Promotional Banners',
  'Search by ID or name' => 'Search by ID or name',
  'By Turning OFF Promotional Banner Section' => 'By Turning OFF Promotional Banner Section',
  'Promotional banner will be disabled. You will be unable to see promotional activity' => 'Promotional banner will be disabled. You will be unable to see promotional activity',
  'By Turning ON Promotional Banner Section' => 'By Turning ON Promotional Banner Section',
  'Promotional banner will be enabled. You will be able to see promotional activity' => 'Promotional banner will be enabled. You will be able to see promotional activity',
  'Feature Title & Short Description' => 'Feature Title & Short Description',
  'Section View' => 'Section View',
  'short description' => 'Short description',
  'Feature List' => 'Feature List',
  'Feature List Section' => 'Feature List Section',
  'Feature list will be disabled. You can enable it in the settings to access its features and functionality' => 'Feature list will be disabled. You can enable it in the settings to access its features and functionality',
  'Feature list is enabled. You can now access its features and functionality' => 'Feature list is enabled. You can now access its features and functionality',
  'Download User App Section Content ' => 'Download User App Section Content ',
  'Download Seller App Section' => 'Download Seller App Section',
  'Playstore Button' => 'Playstore Button',
  'Download Link' => 'Download Link',
  'Ex: https://play.google.com/store/apps' => 'Ex: https://play.google.com/store/apps',
  'App Store Button' => 'App Store Button',
  'Ex: https://www.apple.com/app-store/' => 'Ex: https://www.apple.com/app-store/',
  'Download Delivery Man App Section' => 'Download Delivery Man App Section',
  'Admin Earn Money' => 'Admin Earn Money',
  'Download Delivery Man App Section ' => 'Download Delivery Man App Section ',
  'App Store Button Disabled for Seller' => 'App Store Button Disabled for Seller',
  'App Store button is enabled now everyone can use or see the button' => 'App Store button is enabled now everyone can use or see the button',
  'App Store Button Enabled for Seller' => 'App Store Button Enabled for Seller',
  'App Store button is disabled now no one can use or see the button' => 'App Store button is disabled now no one can use or see the button',
  'Playstore Button Disabled for Seller' => 'Playstore Button Disabled for Seller',
  'Playstore button is disabled now no one can use or see the button' => 'Playstore button is disabled now no one can use or see the button',
  'Playstore Button Enabled for Seller' => 'Playstore Button Enabled for Seller',
  'Playstore button is enabled now everyone can use or see the button' => 'Playstore button is enabled now everyone can use or see the button',
  'App Store Button Disabled for Deliveryman' => 'App Store Button Disabled for Deliveryman',
  'App Store Button Enabled for Deliveryman' => 'App Store Button Enabled for Deliveryman',
  'Ex: Title of the section' => 'Ex: Title of the section',
  'Special Criteria List Section ' => 'Special Criteria List Section ',
  'Special Criteria Title' => 'Special Criteria Title',
  'Ex:  Multi Store System' => 'Ex:  Multi Store System',
  'Criteria Icon/ Image' => 'Criteria Icon/ Image',
  ' Special Criteria' => ' Special Criteria',
  'This Criteria' => 'This Criteria',
  'This section  will be disabled. You can enable it in the settings' => 'This section  will be disabled. You can enable it in the settings',
  'This section will be enabled. You can see this section on your landing page.' => 'This section will be enabled. You can see this section on your landing page.',
  'Counter Section' => 'Counter Section',
  'Total App Download' => 'Total App Download',
  'Ex: 500' => 'Ex: 500',
  'Total Seller' => 'Total Seller',
  'Total Delivery Man' => 'Total Delivery Man',
  'Total Customer' => 'Total Customer',
  'Download User App Section Content' => 'Download User App Section Content',
  'Download Apps Section' => 'Download Apps Section',
  'By Turning OFF Counter Section' => 'By Turning OFF Counter Section',
  'Counter section will be disabled. You can enable it in the settings to access its features and functionality' => 'Counter section will be disabled. You can enable it in the settings to access its features and functionality',
  'By Turning ON Counter Section' => 'By Turning ON Counter Section',
  'Counter Section is enabled. You can now access its features and functionality' => 'Counter Section is enabled. You can now access its features and functionality',
  'By Turning OFF App Store Button' => 'By Turning OFF App Store Button',
  'App store button will be disabled now no one can use or see the button' => 'App store button will be disabled now no one can use or see the button',
  'By Turning ON App Store Button' => 'By Turning ON App Store Button',
  'App store button is enabled now everyone can use or see the button' => 'App store button is enabled now everyone can use or see the button',
  'By Turning OFF PlayStore Button' => 'By Turning OFF PlayStore Button',
  'Playstore button will be disabled now no one can use or see the button' => 'Playstore button will be disabled now no one can use or see the button',
  'By Turning ON PlayStore Button' => 'By Turning ON PlayStore Button',
  'Playstore button will be enabled now everyone can use or see the button' => 'Playstore button will be enabled now everyone can use or see the button',
  'Testimonial List Section' => 'Testimonial List Section',
  'Reviewer Name' => 'Reviewer Name',
  'Ex:  John Doe' => 'Ex:  John Doe',
  'Designation' => 'Designation',
  'Ex:  CTO' => 'Ex:  CTO',
  'Very Good Company' => 'Very Good Company',
  'Reviewer Image *' => 'Reviewer Image *',
  'Company Logo *' => 'Company Logo *',
  'Reviews' => 'Reviews',
  'Reviewer Image' => 'Reviewer Image',
  'Company Image' => 'Company Image',
  'Start Time' => 'Start Time',
  'End Time' => 'End Time',
  'Start Day' => 'Start Day',
  'End Day' => 'End Day',
  'Notice!' => 'Notice!',
  'If you want to disable or turn off any section please leave that section empty, don’t make any changes there!' => 'If you want to disable or turn off any section please leave that section empty  don’t make any changes there!',
  'If You Want to Change Language' => 'If You Want to Change Language',
  'Change the language on tab bar and input your data again!' => 'Change the language on tab bar and input your data again!',
  'Let’s See The Changes!' => 'Let’s See The Changes!',
  'Visit landing page to see the changes you made in the settings option!' => 'Visit landing page to see the changes you made in the settings option!',
  'admin_landing_page_settings' => 'Admin landing page settings',
  'flutter_landing_page' => 'Flutter landing page',
  'Header' => 'Header',
  'Company Intro' => 'Company Intro',
  'Download User App' => 'Download User App',
  'Business Section' => 'Business Section',
  'Header Section' => 'Header Section',
  'Tag Line' => 'Tag Line',
  'tag_line...' => 'Tag line...',
  'Button Content' => 'Button Content',
  'Button Name' => 'Button Name',
  'Ex: Order now' => 'Ex: Order now',
  'Redirect Link' => 'Redirect Link',
  'Section Title' => 'Section Title',
  'Seller Section Content' => 'Seller Section Content',
  'Button Redirect' => 'Button Redirect',
  'Delivery Man Section Content' => 'Delivery Man Section Content',
  'Download Delivery Man App' => 'Download Delivery Man App',
  'By Turning OFF User App Button' => 'By Turning OFF User App Button',
  'User app button will be disabled. Nobody can use or see the button' => 'User app button will be disabled. Nobody can use or see the button',
  'By Turning ON User App Button' => 'By Turning ON User App Button',
  'User app button will be enabled. everyone can use or see the button' => 'User app button will be enabled. everyone can use or see the button',
  'By Turning OFF Delivery Man App Button' => 'By Turning OFF Delivery Man App Button',
  'Seller app button will be disabled. Nobody can use or see the button' => 'Seller app button will be disabled. Nobody can use or see the button',
  'By Turning ON Delivery Man App Button' => 'By Turning ON Delivery Man App Button',
  'Playstore button will be enabled. everyone can use or see the button' => 'Playstore button will be enabled. everyone can use or see the button',
  'By Turning OFF Seller App Button' => 'By Turning OFF Seller App Button',
  'By Turning ON Seller App Button' => 'By Turning ON Seller App Button',
  'Testimonials' => 'Testimonials',
  'This Testimonial' => 'This Testimonial',
  'This testimonial will be disable. You can see this testimonial in review section.' => 'This testimonial will be disable. You can see this testimonial in review section.',
  'This testimonial will be enabled. You can see this testimonial in review section.' => 'This testimonial will be enabled. You can see this testimonial in review section.',
  'flutter_web_landing_page' => 'Flutter web landing page',
  'special_criteria' => 'Special criteria',
  'join_as' => 'Join as',
  'location_setup' => 'Location setup',
  'module_setup' => 'Module setup',
  'Feature Special Criteria Status' => 'Feature Special Criteria Status',
  'Criteria list will be disabled. You can enable it in the settings to access its features and functionality' => 'Criteria list will be disabled. You can enable it in the settings to access its features and functionality',
  'Special Criteria is enabled. You can now access its features and functionality' => 'Special Criteria is enabled. You can now access its features and functionality',
  'Join as a Seller' => 'Join as a Seller',
  'Select Language' => 'Select Language',
  'Join as a Delivery Man' => 'Join as a Delivery Man',
  'Join as Seller' => 'Join as Seller',
  'Join as Deliveryman' => 'Join as Deliveryman',
  'By Turning OFF Download Apps Status' => 'By Turning OFF Download Apps Status',
  'By Turning ON Download Apps Status' => 'By Turning ON Download Apps Status',
  'a_5_digit_verification_code_has_been' => 'A 5 digit verification code has been',
  'sent_to' => 'Sent to',
  'enter_the_verification_code' => 'Enter the verification code',
  'Didn`t receive the code?' => 'Didn`t receive the code ',
  'Resend_it' => 'Resend it',
  'Link_expired' => 'Link expired',
  'Failed_to_sent_otp' => 'Failed to sent otp',
  'Otp_successfull_sent' => 'Otp successfull sent',
  'By Turning ONN Promotional Banner Section' => 'By Turning ONN Promotional Banner Section',
  'short_description_here...' => 'Short description here...',
  'feature_added_successfully' => 'Feature added successfully',
  'feature_section_updated' => 'Feature section updated',
  'feature_status_updated' => 'Feature status updated',
  'feature_deleted_successfully' => 'Feature deleted successfully',
  'Playstore Button Enabled for Delivery Man' => 'Playstore Button Enabled for Delivery Man',
  'Playstore Button Disabled for Delivery Man' => 'Playstore Button Disabled for Delivery Man',
  'App Store Button Enabled for Delivery Man' => 'App Store Button Enabled for Delivery Man',
  'App Store Button Disabled for Delivery Man' => 'App Store Button Disabled for Delivery Man',
  'seller_links_updated' => 'Seller links updated',
  'delivery_man_links_updated' => 'Delivery man links updated',
  'criteria_added_successfully' => 'Criteria added successfully',
  'why_choose_section_updated' => 'Why choose section updated',
  'criteria_status_updated' => 'Criteria status updated',
  'criteria_updated_successfully' => 'Criteria updated successfully',
  'Want to delete this criteria ?' => 'Want to delete this criteria  ',
  'criteria' => 'Criteria',
  'criteria_deleted_successfully' => 'Criteria deleted successfully',
  'download_app_section_updated' => 'Download app section updated',
  'button_name_here...' => 'Button name here...',
  'sbutton_name_here...' => 'Sbutton name here...',
  'Button URL' => 'Button URL',
  'join_as_seller_data_updated' => 'Join as seller data updated',
  'join_as_delivery_man_data_updated' => 'Join as delivery man data updated',
  'earning_section_updated' => 'Earning section updated',
  'landing_page_header_updated' => 'Landing page header updated',
  'This review' => 'This review',
  'Want to delete this review ?' => 'Want to delete this review  ',
  ' Special review' => ' Special review',
  'testimonial_section_updated' => 'Testimonial section updated',
  'testimonial_added_successfully' => 'Testimonial added successfully',
  'review_status_updated' => 'Review status updated',
  'review_deleted_successfully' => 'Review deleted successfully',
  'review_updated_successfully' => 'Review updated successfully',
  'Seller Button Enabled for Delivery Man' => 'Seller Button Enabled for Delivery Man',
  'Seller Button Disabled for Delivery Man' => 'Seller Button Disabled for Delivery Man',
  'Seller button is enabled now everyone can use or see the button' => 'Seller button is enabled now everyone can use or see the button',
  'Seller button is disabled now no one can use or see the button' => 'Seller button is disabled now no one can use or see the button',
  'Delivery Man Button Enabled for Delivery Man' => 'Delivery Man Button Enabled for Delivery Man',
  'Delivery Man Button Disabled for Delivery Man' => 'Delivery Man Button Disabled for Delivery Man',
  'Delivery Man button is enabled now everyone can use or see the button' => 'Delivery Man button is enabled now everyone can use or see the button',
  'Delivery Man button is disabled now no one can use or see the button' => 'Delivery Man button is disabled now no one can use or see the button',
  'User Button Enabled for Delivery Man' => 'User Button Enabled for Delivery Man',
  'User Button Disabled for Delivery Man' => 'User Button Disabled for Delivery Man',
  'User button is enabled now everyone can use or see the button' => 'User button is enabled now everyone can use or see the button',
  'User button is disabled now no one can use or see the button' => 'User button is disabled now no one can use or see the button',
  'business_section_updated' => 'Business section updated',
  'header_section_updated' => 'Header section updated',
  'Company Section' => 'Company Section',
  'company_section_updated' => 'Company section updated',
  'Start a new message' => 'Start a new message',
  'email_template' => 'Email template',
  'Email Templates' => 'Email Templates',
  'Admin Registration' => 'Admin Registration',
  'Forgot Password' => 'Forgot Password',
  'New Store Registration' => 'New Store Registration',
  'New Delivery Man Registration' => 'New Delivery Man Registration',
  'Campaign Join Request' => 'Campaign Join Request',
  'Login mail' => 'Login mail',
  'Send Mail on Place Order ?' => 'Send Mail on Place Order  ',
  'Read Instructions' => 'Read Instructions',
  'Header Content' => 'Header Content',
  'Main Title' => 'Main Title',
  'Background image' => 'Background image',
  'Footer Content' => 'Footer Content',
  'Section Text' => 'Section Text',
  'Page Links' => 'Page Links',
  'Privacy Policy' => 'Privacy Policy',
  'Contact Us' => 'Contact Us',
  'Social Media Links' => 'Social Media Links',
  'Copyright Content' => 'Copyright Content',
  'Want to disable Place Order' => 'Want to disable Place Order',
  'User will not get a confirmation email when they placed a order.' => 'User will not get a confirmation email when they placed a order.',
  'Want to enable Place Order' => 'Want to enable Place Order',
  'User will get a confirmation email when they placed a order.' => 'User will get a confirmation email when they placed a order.',
  'Send Mail on Forgot Password ?' => 'Send Mail on Forgot Password  ',
  'Icon / Vector' => 'Icon / Vector',
  'Want to disable Forgot Password' => 'Want to disable Forgot Password',
  'User will not get a OTP by email when they forgot password. they will get OTP by SMS if sms module is on.' => 'User will not get a OTP by email when they forgot password. they will get OTP by SMS if sms module is on.',
  'Want to enable Forgot Password' => 'Want to enable Forgot Password',
  'User will get a OTP by email when they forgot password.' => 'User will get a OTP by email when they forgot password.',
  'Get Mail on New Store Registration ?' => 'Get Mail on New Store Registration  ',
  'Logo' => 'Logo',
  'By Turning OFF Get Mail on New Store Registration' => 'By Turning OFF Get Mail on New Store Registration',
  'User will not get mail when they register for a new store!' => 'User will not get mail when they register for a new store!',
  'By Turning ON Get Mail on New Store Registration' => 'By Turning ON Get Mail on New Store Registration',
  'User will get mail when they register for a new store!' => 'User will get mail when they register for a new store!',
  'Select Theme' => 'Select Theme',
  'Input a Title' => 'Input a Title',
  'Give email template a descriptive title that will help users identify what it for.' => 'Give email template a descriptive title that will help users identify what it for.',
  'Add the message body' => 'Add the message body',
  'Add Button & Link' => 'Add Button & Link',
  'Specify the text and URL for the button that you want to include in your email.' => 'Specify the text and URL for the button that you want to include in your email.',
  'Choose a background image' => 'Choose a background image',
  'Select a background image to display behind your email content.' => 'Select a background image to display behind your email content.',
  'Select footer content' => 'Select footer content',
  'Add footer content, such as your company address and contact information.' => 'Add footer content  such as your company address and contact information.',
  'Create a copyright notice' => 'Create a copyright notice',
  'Include a copyright notice at the bottom of your email to protect your content.' => 'Include a copyright notice at the bottom of your email to protect your content.',
  'Save and publish' => 'Save and publish',
  'Once you\'ve set up all the elements of your email template, save and publish it for use.' => 'Once you ve set up all the elements of your email template  save and publish it for use.',
  'Mail Body Message' => 'Mail Body Message',
  'template_added_successfully' => 'Template added successfully',
  'Icon' => 'Icon',
  'Banner image' => 'Banner image',
  'email_status_updated' => 'Email status updated',
  'Send Mail on Campaign Request ?' => 'Send Mail on Campaign Request  ',
  'Want to enable Campaign Request' => 'Want to enable Campaign Request',
  'Want to disable Campaign Request' => 'Want to disable Campaign Request',
  'User will get a confirmation email when they requests to join campaign.' => 'User will get a confirmation email when they requests to join campaign.',
  'User will not get a confirmation email when they requests to join campaign.' => 'User will not get a confirmation email when they requests to join campaign.',
  'Send Mail on Delivery Man Registration ?' => 'Send Mail on Delivery Man Registration  ',
  'Want to enable Delivery Man Registration' => 'Want to enable Delivery Man Registration',
  'Want to disable Delivery Man Registration' => 'Want to disable Delivery Man Registration',
  'User will get a confirmation email when they newly register.' => 'User will get a confirmation email when they newly register.',
  'User will not get a confirmation email when they newly register.' => 'User will not get a confirmation email when they newly register.',
  'Send Mail on Store Registration ?' => 'Send Mail on Store Registration  ',
  'Want to enable Store Registration' => 'Want to enable Store Registration',
  'Want to disable Store Registration' => 'Want to disable Store Registration',
  'Send Mail on Withdraw REquest ?' => 'Send Mail on Withdraw REquest  ',
  'Want to enable Withdraw REquest' => 'Want to enable Withdraw REquest',
  'Want to disable Withdraw REquest' => 'Want to disable Withdraw REquest',
  'User will get a confirmation email when requests withdraw.' => 'User will get a confirmation email when requests withdraw.',
  'User will not get a confirmation email when requests withdraw.' => 'User will not get a confirmation email when requests withdraw.',
  'Send Mail on Forget Password ?' => 'Send Mail on Forget Password  ',
  'Want to enable Forget Password' => 'Want to enable Forget Password',
  'Want to disable Forget Password' => 'Want to disable Forget Password',
  'User will get a confirmation email when requests forget password.' => 'User will get a confirmation email when requests forget password.',
  'User will not get a confirmation email when requests forget password.' => 'User will not get a confirmation email when requests forget password.',
  'Send Mail on Login ?' => 'Send Mail on Login  ',
  'Want to enable Login' => 'Want to enable Login',
  'Want to disable Login' => 'Want to disable Login',
  'User will get a confirmation email when login.' => 'User will get a confirmation email when login.',
  'User will not get a confirmation email when login.' => 'User will not get a confirmation email when login.',
  'Send Mail on Refund Request ?' => 'Send Mail on Refund Request  ',
  'Want to enable Refund Request' => 'Want to enable Refund Request',
  'Want to disable Refund Request' => 'Want to disable Refund Request',
  'User will get a confirmation email when requests refund.' => 'User will get a confirmation email when requests refund.',
  'User will not get a confirmation email when requests refund.' => 'User will not get a confirmation email when requests refund.',
  'User_app_settings_updated' => 'User app settings updated',
  'Store_app_settings_updated' => 'Store app settings updated',
  'Delivery_app_settings_updated' => 'Delivery app settings updated',
  'Email Verification' => 'Email Verification',
  'mail_received_successfully' => 'Mail received successfully',
  'Thanks & Regards' => 'Thanks & Regards',
  'Send Mail on Withdraw Request ?' => 'Send Mail on Withdraw Request  ',
  'Want to enable Withdraw Request' => 'Want to enable Withdraw Request',
  'Want to disable Withdraw Request' => 'Want to disable Withdraw Request',
  'Admin_mail_templates' => 'Admin mail templates',
  'Store_mail_templates' => 'Store mail templates',
  'Delivery_man_mail_templates' => 'Delivery man mail templates',
  'User_mail_templates' => 'User mail templates',
  'New Store Approve' => 'New Store Approve',
  'New Store Deny' => 'New Store Deny',
  'Withdraw Approve' => 'Withdraw Approve',
  'Withdraw Deny' => 'Withdraw Deny',
  'Campaign Join Approve' => 'Campaign Join Approve',
  'Campaign Join Deny' => 'Campaign Join Deny',
  'Send Mail on Campaign deny ?' => 'Send Mail on Campaign deny  ',
  'Want to enable Campaign deny' => 'Want to enable Campaign deny',
  'Want to disable Campaign deny' => 'Want to disable Campaign deny',
  'User will get a confirmation email when they appove to join campaign.' => 'User will get a confirmation email when they appove to join campaign.',
  'User will not get a confirmation email when they appove to join campaign.' => 'User will not get a confirmation email when they appove to join campaign.',
  'Send Mail on Campaign Approve ?' => 'Send Mail on Campaign Approve  ',
  'Want to enable Campaign Approve' => 'Want to enable Campaign Approve',
  'Want to disable Campaign Approve' => 'Want to disable Campaign Approve',
  'Send Mail on Withdraw deny ?' => 'Send Mail on Withdraw deny  ',
  'Want to enable Withdraw deny' => 'Want to enable Withdraw deny',
  'Want to disable Withdraw deny' => 'Want to disable Withdraw deny',
  'User will get a confirmation email when denys withdraw.' => 'User will get a confirmation email when denys withdraw.',
  'User will not get a confirmation email when denys withdraw.' => 'User will not get a confirmation email when denys withdraw.',
  'New Delivery Man Approve' => 'New Delivery Man Approve',
  'New Delivery Man Deny' => 'New Delivery Man Deny',
  'Suspension' => 'Suspension',
  'Cash Collected' => 'Cash Collected',
  'Send Mail on Delivery Man approve ?' => 'Send Mail on Delivery Man approve  ',
  'Want to enable Delivery Man approve' => 'Want to enable Delivery Man approve',
  'Want to disable Delivery Man approve' => 'Want to disable Delivery Man approve',
  'Send Mail on Delivery Man deny ?' => 'Send Mail on Delivery Man deny  ',
  'Want to enable Delivery Man deny' => 'Want to enable Delivery Man deny',
  'Want to disable Delivery Man deny' => 'Want to disable Delivery Man deny',
  'Send Mail on Suspend ?' => 'Send Mail on Suspend  ',
  'Want to enable Suspend' => 'Want to enable Suspend',
  'Want to disable Suspend' => 'Want to disable Suspend',
  'Send Mail on cash collect ?' => 'Send Mail on cash collect  ',
  'Want to enable cash collect' => 'Want to enable cash collect',
  'Want to disable cash collect' => 'Want to disable cash collect',
  'User will get a confirmation email when requests cash collect.' => 'User will get a confirmation email when requests cash collect.',
  'User will not get a confirmation email when requests cash collect.' => 'User will not get a confirmation email when requests cash collect.',
  'New User Registration' => 'New User Registration',
  'Send Mail on Withdraw approve ?' => 'Send Mail on Withdraw approve  ',
  'Want to enable Withdraw approve' => 'Want to enable Withdraw approve',
  'Want to disable Withdraw approve' => 'Want to disable Withdraw approve',
  'User will get a confirmation email when approves withdraw.' => 'User will get a confirmation email when approves withdraw.',
  'User will not get a confirmation email when approves withdraw.' => 'User will not get a confirmation email when approves withdraw.',
  'Send Mail on Store deny ?' => 'Send Mail on Store deny  ',
  'Want to enable Store deny' => 'Want to enable Store deny',
  'Want to disable Store deny' => 'Want to disable Store deny',
  'Send Mail on Store approve ?' => 'Send Mail on Store approve  ',
  'Want to enable Store approve' => 'Want to enable Store approve',
  'Want to disable Store approve' => 'Want to disable Store approve',
  'Send Mail on User Registration ?' => 'Send Mail on User Registration  ',
  'Want to enable User Registration' => 'Want to enable User Registration',
  'Want to disable User Registration' => 'Want to disable User Registration',
  'New User Registration OTP' => 'New User Registration OTP',
  'New Order Verification' => 'New Order Verification',
  'Refund Request Deny' => 'Refund Request Deny',
  'Add fund' => 'Add fund',
  'Send Mail on add fund ?' => 'Send Mail on add fund  ',
  'Want to enable add fund' => 'Want to enable add fund',
  'Want to disable add fund' => 'Want to disable add fund',
  'User will get a confirmation email when requests add fund.' => 'User will get a confirmation email when requests add fund.',
  'User will not get a confirmation email when requests add fund.' => 'User will not get a confirmation email when requests add fund.',
  'Registration OTP' => 'Registration OTP',
  'Login OTP' => 'Login OTP',
  'Send Mail on User login ?' => 'Send Mail on User login  ',
  'Want to enable User login' => 'Want to enable User login',
  'Want to disable User login' => 'Want to disable User login',
  'Order Verification' => 'Order Verification',
  'Send Mail on Refund Order ?' => 'Send Mail on Refund Order  ',
  'Want to enable Refund Order' => 'Want to enable Refund Order',
  'Want to disable Refund Order' => 'Want to disable Refund Order',
  'User will get a confirmation email when registration appoved.' => 'User will get a confirmation email when registration appoved.',
  'User will not get a confirmation email when registration appoved.' => 'User will not get a confirmation email when registration appoved.',
  'User will get a confirmation email when they request denied to join campaign.' => 'User will get a confirmation email when they request denied to join campaign.',
  'User will not get a confirmation email when they request denied to join campaign.' => 'User will not get a confirmation email when they request denied to join campaign.',
  'User will get a confirmation email when request denied to join campaign.' => 'User will get a confirmation email when request denied to join campaign.',
  'User will not get a confirmation email when request denied to join campaign.' => 'User will not get a confirmation email when request denied to join campaign.',
  'User will get a confirmation email when request appoved to join campaign.' => 'User will get a confirmation email when request appoved to join campaign.',
  'User will not get a confirmation email when request appoved to join campaign.' => 'User will not get a confirmation email when request appoved to join campaign.',
  'User will get a confirmation email when request denied.' => 'User will get a confirmation email when request denied.',
  'User will not get a confirmation email when request denied.' => 'User will not get a confirmation email when request denied.',
  'User will get a confirmation email when registration approved.' => 'User will get a confirmation email when registration approved.',
  'User will not get a confirmation email when registration approved.' => 'User will not get a confirmation email when registration approved.',
  'User will get a confirmation email when registration denied.' => 'User will get a confirmation email when registration denied.',
  'User will not get a confirmation email when registration denied.' => 'User will not get a confirmation email when registration denied.',
  'User will get a confirmation email when admin collect cash from them.' => 'User will get a confirmation email when admin collect cash from them.',
  'User will not get a confirmation email when admin collect cash from them.' => 'User will not get a confirmation email when admin collect cash from them.',
  'User will get a confirmation email when they login.' => 'User will get a confirmation email when they login.',
  'User will not get a confirmation email when they login.' => 'User will not get a confirmation email when they login.',
  'User will get a confirmation email when order verification is on.' => 'User will get a confirmation email when order verification is on.',
  'User will not get a confirmation email when order verification is on.' => 'User will not get a confirmation email when order verification is on.',
  'Send Mail on Order Verification ?' => 'Send Mail on Order Verification  ',
  'Want to enable Order Verification' => 'Want to enable Order Verification',
  'Want to disable Order Verification' => 'Want to disable Order Verification',
  'Send Mail on Refund Request Denied ?' => 'Send Mail on Refund Request Denied  ',
  'Want to enable Refund Request Denied' => 'Want to enable Refund Request Denied',
  'Want to disable Refund Request Denied' => 'Want to disable Refund Request Denied',
  'User will get a confirmation email when refund request denied.' => 'User will get a confirmation email when refund request denied.',
  'User will not get a confirmation email when refund request denied.' => 'User will not get a confirmation email when refund request denied.',
  'User will get a confirmation email when admin add fund.' => 'User will get a confirmation email when admin add fund.',
  'User will not get a confirmation email when admin add fund.' => 'User will not get a confirmation email when admin add fund.',
  'transaction_id' => 'Transaction id',
  'User will get a confirmation email when they are suspended.' => 'User will get a confirmation email when they are suspended.',
  'User will not get a confirmation email when they are suspended.' => 'User will not get a confirmation email when they are suspended.',
  'view_order_list' => 'View order list',
  'Turn ON' => 'Turn ON',
  'Hour' => 'Hour',
  'No variation added' => 'No variation added',
  'Multiple Selection' => 'Multiple Selection',
  'Single Selection' => 'Single Selection',
  'Default' => 'Default',
  'ABC Company' => 'ABC Company',
  'House#94, Road#8, Abc City' => 'House#94  Road#8  Abc City',
  'Store Logo & Covers' => 'Store Logo & Covers',
  'Store Cover' => 'Store Cover',
  '2:1' => '2:1',
  'Estimated Delivery Time ( Min & Maximum Time)' => 'Estimated Delivery Time ( Min & Maximum Time)',
  'Minimum Time' => 'Minimum Time',
  'Maximum Time' => 'Maximum Time',
  'add_store_name' => 'Add store name',
  'done' => 'Done',
  'Order Notification' => 'Order Notification',
  'Turning off this, admin will not get a popup notification with sound for all orders.' => 'Turning off this  admin will not get a popup notification with sound for all orders.',
  'pages_&_social_media' => 'Pages & social media',
  'system_management' => 'System management',
  '3rd_party_&_configurations' => '3rd party & configurations',
  '3rd_party' => '3rd party',
  'firebase_notification' => 'Firebase notification',
  'Change status to cooking ?' => 'Change status to cooking  ',
  'Are you sure ?' => 'Are you sure  ',
  'Enter processing time' => 'Enter processing time',
  'Enter Processing time in minutes' => 'Enter Processing time in minutes',
  'No' => 'No',
  'Cancel Order' => 'Cancel Order',
  'Banner Section' => 'Banner Section',
  'banner Image' => 'Banner Image',
  'is_organic' => 'Is organic',
  'plesae_enter_your_registerd_email' => 'Plesae enter your registerd email',
  'Email_does_not_exists' => 'Email does not exists',
  'company' => 'Company',
  'new_company' => 'New company',
  'digit_after_decimal_point' => 'Digit after decimal point',
  'Ex_:_Copyright_Text' => 'Ex : Copyright Text',
  'ex_:_2' => 'Ex : 2',
  'how_many_fractional_digit_to_show_after_decimal_value' => 'How many fractional digit to show after decimal value',
  'NB: Must select atlest one payment method' => 'NB: Must select atlest one payment method',
  'Main_Title_or_Subject_of_the_Mail' => 'Main Title or Subject of the Mail',
  'Hi_Sabrina,' => 'Hi Sabrina ',
  'Order_Info' => 'Order Info',
  'Order_Summary' => 'Order Summary',
  'Order' => 'Order',
  'Delivery_Address' => 'Delivery Address',
  'Price' => 'Price',
  'Please_contact_us_for_any_queries,_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Thanks_&_Regards' => 'Thanks & Regards',
  'Privacy_Policy' => 'Privacy Policy',
  'Refund_Policy' => 'Refund Policy',
  'Cancelation_Policy' => 'Cancelation Policy',
  'Contact_us' => 'Contact us',
  'Copyright 2023 6ammart. All right reserved' => 'Copyright 2023 6ammart. All right reserved',
  'react_site' => 'React site',
  'Identity_Type' => 'Identity Type',
  'driving_license' => 'Driving license',
  'login_page_setup' => 'Login page setup',
  'Admin_login_url' => 'Admin login url',
  'For_admin' => 'For admin',
  'admin_login_url' => 'Admin login url',
  'admin_employee_login_page' => 'Admin employee login page',
  'For_admin_employee' => 'For admin employee',
  'admin_employee_login_url' => 'Admin employee login url',
  'store_login_page' => 'Store login page',
  'For_stores' => 'For stores',
  'store_login_url' => 'Store login url',
  'store_employee_login_page' => 'Store employee login page',
  'For_store_employee' => 'For store employee',
  'store_employee_login_url' => 'Store employee login url',
  'login_url_page' => 'Login url page',
  'update_successfull' => 'Update successfull',
  'don’t_forget_to_click_the_‘Save Information’_button_below_to_save_changes.' => 'Don’t forget to click the ‘Save Information’ button below to save changes.',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_‘Turn_Off’ _maintenance_mode.' => 'All your apps and customer website will be disabled until you ‘Turn Off’  maintenance mode.',
  'make_visitors_aware_of_your_business‘s_rights_&_legal_information.' => 'Make visitors aware of your business‘s rights & legal information.',
  'Business_Rules_setup' => 'Business Rules setup',
  'Default_Commission_Rate_On_Order' => 'Default Commission Rate On Order',
  'Set_up_‘Default_Commission_Rate’_on_every_Order._Admin_can_also_set_store-wise_different_commission_rates_from_respective_store_settings.' => 'Set up ‘Default Commission Rate’ on every Order. Admin can also set store-wise different commission rates from respective store settings.',
  'Commission_Rate_On_Delivery_Charge' => 'Commission Rate On Delivery Charge',
  'Set_a_default_‘Commission_Rate’_for_freelance_deliverymen_(under_admin)_on_every_deliveryman. ' => 'Set a default ‘Commission Rate’ for freelance deliverymen (under admin) on every deliveryman. ',
  'Who_Will_Confirm_Order?' => 'Who Will Confirm Order?',
  'After_a_customer_order_placement,_Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store?_For_example,_if_you_choose_‘Delivery_man’,_the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_‘Store’.' => 'After a customer order placement  Admin can define who will confirm the order first- Deliveryman or Store?  For example  if you choose ‘Delivery man’  the deliveryman nearby will confirm the order and forward it to the related store to process the order. It works vice-versa if you choose ‘Store’.',
  'If_enabled,_the_customer_will_see_the_total_product_price,_including_VAT/Tax._If_it’s_disabled,_the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'If enabled  the customer will see the total product price  including VAT/Tax. If it’s disabled  the VAT/Tax will be added separately with the total cost of the product.',
  'Want_to' => 'Want to',
  '‘Include_Tax_Amount?’' => '‘Include Tax Amount?’',
  'Want_to_disable' => 'Want to disable',
  'Tax_Amount’?' => 'Tax Amount’?',
  'If_you_enable_it,_customers_will_see_the_product_Price_including_Tax,_during_checkout. ' => 'If you enable it  customers will see the product Price including Tax  during checkout. ',
  'If_you_disable_it,_customers_will_see_the_product_or_service_price_without_Tax,_during_checkout.' => 'If you disable it  customers will see the product or service price without Tax  during checkout.',
  'Customer’s_Food_Preference' => 'Customer’s Food Preference',
  'If_this_feature_is_active,_customers_can_filter_food_according_to_their_preference_from_the_Customer_App_or_Website.' => 'If this feature is active  customers can filter food according to their preference from the Customer App or Website.',
  'Want_to_enable_the' => 'Want to enable the',
  '‘Veg/Non-Veg’_feature?' => '‘Veg/Non-Veg’ feature?',
  'the_Veg/Non-Veg_Feature?' => 'The Veg/Non-Veg Feature?',
  'If_you_enable_this,_customers_can_filter_food_items_by_choosing_food_from_the_Veg/Non-Veg_feature.' => 'If you enable this  customers can filter food items by choosing food from the Veg/Non-Veg feature.',
  'If_you_disable_this,_the_Veg/Non-Veg_feature_will_be_hidden_in_the_Customer_App_&_Website.' => 'If you disable this  the Veg/Non-Veg feature will be hidden in the Customer App & Website.',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded,_the_Delivery_Fee_is_deducted_from_Admin’s_commission_and_added_to_Admin’s_expense.' => 'Set a minimum order value for automated free delivery. If the minimum amount is exceeded  the Delivery Fee is deducted from Admin’s commission and added to Admin’s expense.',
  'Want_to_enable_Free_Delivery_on_Minimum_Orders?' => 'Want to enable Free Delivery on Minimum Orders?',
  'Want_to_disable_Free_Delivery_on_Minimum_Order?' => 'Want to disable Free Delivery on Minimum Order?',
  'If_you_enable_this,_customers_can_get_FREE_Delivery_by_fulfilling_the_minimum_order_requirement.' => 'If you enable this  customers can get FREE Delivery by fulfilling the minimum order requirement.',
  'If_you_disable_this,_the_FREE_Delivery_option_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the FREE Delivery option will be hidden from the Customer App or Website.',
  'Order_Notification_for_Admin' => 'Order Notification for Admin',
  'Admin_will_get_a_pop-up_notification_with_sounds_for_any_order_placed_by_customers.' => 'Admin will get a pop-up notification with sounds for any order placed by customers.',
  'Want_to_enable' => 'Want to enable',
  'Order_Notification_for_Admin?' => 'Order Notification for Admin?',
  'If_you_enable_this,_the_Admin_will_receive_a_Notification_for_every_order_placed.' => 'If you enable this  the Admin will receive a Notification for every order placed.',
  'If_you_disable_this,_the_Admin_will_NOT_receive_a_Notification_for_every_order_placed.' => 'If you disable this  the Admin will NOT receive a Notification for every order placed.',
  'order_settings' => 'Order settings',
  'When_a_deliveryman_arrives_for_delivery,_Customers_will_get_a_4-digit_verification_code_on_the_order_details_section_in_the_Customer_App_and_needs_to_provide_the_code_to_the_delivery_man_to_verify_the_order.' => 'When a deliveryman arrives for delivery  Customers will get a 4-digit verification code on the order details section in the Customer App and needs to provide the code to the delivery man to verify the order.',
  'Delivery_Verification?' => 'Delivery Verification?',
  'If you enable this, the Deliveryman has to verify the order during delivery through a 4-digit verification code.' => 'If you enable this  the Deliveryman has to verify the order during delivery through a 4-digit verification code.',
  'If you disable this, Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.' => 'If you disable this  Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.',
  'With_this_feature,_customers_can_place_an_order_by_uploading_prescription._Stores_can_enable/disable_this_feature_from_the_store_settings_if_needed.' => 'With this feature  customers can place an order by uploading prescription. Stores can enable/disable this feature from the store settings if needed.',
  'Place_Order_by_Prescription?' => 'Place Order by Prescription?',
  'If_you_enable_this,_customers_can_place_an_order_by_simply_uploading_their_prescriptions_in_the_Pharmacy_module_from_the_Customer_App_or_Website._Stores_can_enable/disable_this_feature_from_store_settings_if_needed.' => 'If you enable this  customers can place an order by simply uploading their prescriptions in the Pharmacy module from the Customer App or Website. Stores can enable/disable this feature from store settings if needed.',
  'If_disabled,_this_feature_will_be_hidden_from_the_Customer_App,_Website,_and_Store_App_&_Panel.' => 'If disabled  this feature will be hidden from the Customer App  Website  and Store App & Panel.',
  'Place_Order_by_Prescription' => 'Place Order by Prescription',
  'If_you_enable_this_feature,_customers_can_choose_‘Home_Delivery’_and_get_the_product_delivered_to_their_preferred_location.' => 'If you enable this feature  customers can choose ‘Home Delivery’ and get the product delivered to their preferred location.',
  'Home_Delivery?' => 'Home Delivery?',
  'If_you_enable_this,_customers_can_use_Home_Delivery_Option_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use Home Delivery Option during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Home_Delivery_feature_will_be_hidden_from_the_customer_app_and_website.' => 'If you disable this  the Home Delivery feature will be hidden from the customer app and website.',
  'If_you_enable_this_feature,_customers_can_place_an_order_and_request_‘Takeaways’_or_‘self-pick-up’_from_stores.' => 'If you enable this feature  customers can place an order and request ‘Takeaways’ or ‘self-pick-up’ from stores.',
  'the_Takeaway_feature?' => 'The Takeaway feature ',
  'If_you_enable_this,_customers_can_use_the_Takeaway_feature_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use the Takeaway feature during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Takeaway_feature_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the Takeaway feature will be hidden from the Customer App or Website.',
  'Scheduled_Delivery' => 'Scheduled Delivery',
  'With_this_feature,_customers_can_choose_their_preferred_delivery_slot._Customers_can_select_a_delivery_slot_for_ASAP_or_a_specific_date_(within_2_days_from_the_order).' => 'With this feature  customers can choose their preferred delivery slot. Customers can select a delivery slot for ASAP or a specific date (within 2 days from the order).',
  'Scheduled Delivery?' => 'Scheduled Delivery?',
  'If_you_enable_this,_customers_can_choose_a_suitable_delivery_schedule_during checkout.' => 'If you enable this  customers can choose a suitable delivery schedule during checkout.',
  'If_you_disable_this,_the_Scheduled_Delivery_feature_will_be_hidden.' => 'If you disable this  the Scheduled Delivery feature will be hidden.',
  'Time_Interval_for_Scheduled_Delivery' => 'Time Interval for Scheduled Delivery',
  'By_activating_this_feature,_customers_can_choose_their_suitable_delivery_slot_according_to_a_30-minute_or_1-hour_interval_set_by_the_Admin.' => 'By activating this feature  customers can choose their suitable delivery slot according to a 30-minute or 1-hour interval set by the Admin.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_‘Cancel_Order‘_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the ‘Cancel Order‘ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  'Choose_different_users_for_different_Order_Cancellation_Reasons,_such_as_Customer,_Store,_Deliveryman,_and_Admin.' => 'Choose different users for different Order Cancellation Reasons  such as Customer  Store  Deliveryman  and Admin.',
  'Customers cannot request a Refund if the Admin does not specify a cause for cancellation, even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.' => 'Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’,_customers_can_request_a_refund.' => 'If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'If_you_want_to_delete_this_reason,_please_confirm_your_decision.' => 'If you want to delete this reason  please confirm your decision.',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  'Please_confirm_your_decision_if_you_want_to_delete_this_‘Refund_Reason’.' => 'Please confirm your decision if you want to delete this ‘Refund Reason’.',
  'Can_a_Store_Cancel_Order?' => 'Can a Store Cancel Order?',
  'Admin_can_enable/disable_Store’s_order_cancellation_option.' => 'Admin can enable/disable Store’s order cancellation option.',
  'A_store_can_send_a_registration_request_through_their_store_or_customer.' => 'A store can send a registration request through their store or customer.',
  'Store_Self_Registration?' => 'Store Self Registration?',
  'If_you_enable_this,_Stores_can_do_self-registration_from_the_store_or_customer_app_or_website.' => 'If you enable this  Stores can do self-registration from the store or customer app or website.',
  'If_you_disable_this,_the_Store_Self-Registration_feature_will_be_hidden_from_the_store_or_customer_app,_website,_or_admin_landing_page.' => 'If you disable this  the Store Self-Registration feature will be hidden from the store or customer app  website  or admin landing page.',
  'Tips_for_Deliveryman' => 'Tips for Deliveryman',
  'Customers_can_give_tips_to_deliverymen_during_checkout_from_the_Customer_App_&_Website._Admin_has_no_commission_on_it.' => 'Customers can give tips to deliverymen during checkout from the Customer App & Website. Admin has no commission on it.',
  'Tips_for_Deliveryman_feature?' => 'Tips for Deliveryman feature?',
  'If_you_enable_this,_Customers_can_give_tips_to_a_deliveryman_during_checkout.' => 'If you enable this  Customers can give tips to a deliveryman during checkout.',
  'If_you_disable_this,_the_Tips_for_Deliveryman_feature_will_be_hidden_from_the_Customer_App_and_Website.' => 'If you disable this  the Tips for Deliveryman feature will be hidden from the Customer App and Website.',
  'With_this_feature,_Deliverymen_can_see_their_earnings_on_a_specific_order_while_accepting_it.' => 'With this feature  Deliverymen can see their earnings on a specific order while accepting it.',
  'Show_Earnings_in_App?' => 'Show Earnings in App?',
  'If_you_enable_this,_Deliverymen_can_see_their_earning_per_order_request_from_the_Order_Details_page_in_the_Deliveryman_App.' => 'If you enable this  Deliverymen can see their earning per order request from the Order Details page in the Deliveryman App.',
  'If_you_disable_this,_the_feature_will_be_hidden_from_the_Deliveryman_App.' => 'If you disable this  the feature will be hidden from the Deliveryman App.',
  'With_this_feature,_deliverymen_can_register_themselves_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page._The_admin_will_receive_an_email_notification_and_can_accept_or_reject_the_request.' => 'With this feature  deliverymen can register themselves from the Customer App  Website or Deliveryman App or Admin Landing Page. The admin will receive an email notification and can accept or reject the request.',
  'Deliveryman_Self_Registration?' => 'Deliveryman Self Registration?',
  'If_you_enable_this,_users_can_register_as_Deliverymen_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you enable this  users can register as Deliverymen from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you disable this  this feature will be hidden from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'Set_the_maximum_order_limit_a_Deliveryman_can_take_at_a_time.' => 'Set the maximum order limit a Deliveryman can take at a time.',
  'Can_A_Deliveryman_Cancel_Order?' => 'Can A Deliveryman Cancel Order?',
  'Admin_can_enable/disable_Deliveryman’s_order_cancellation_option_in_the_respective_app.' => 'Admin can enable/disable Deliveryman’s order cancellation option in the respective app.',
  'Here,_customers_can_store_their_refunded_order_amount,_referral_earnings,_and_loyalty_points.' => 'Here  customers can store their refunded order amount  referral earnings  and loyalty points.',
  'With_this_feature,_customers_can_have_virtual_wallets_in_their_account_via_Customer_App_&_Website._They_can_also_earn_(via_referral,_refund,_or_loyalty_points)_and_buy_with_the_wallet’s_amount.' => 'With this feature  customers can have virtual wallets in their account via Customer App & Website. They can also earn (via referral  refund  or loyalty points) and buy with the wallet’s amount.',
  'the_Wallet_feature?' => 'The Wallet feature?',
  'If_you_enable_this,_Customers_can_see_&_use_the_Wallet_option_from_their_profile_in_the_Customer_App_&_Website.' => 'If you enable this  Customers can see & use the Wallet option from their profile in the Customer App & Website.',
  'If_you_disable_this,_the_Wallet_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the Wallet feature will be hidden from the Customer App & Website.',
  'If_it’s_enabled,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets._But_if_it’s_disabled,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If it’s enabled  Customers will automatically receive the refunded amount in their wallets. But if it’s disabled  the Admin will handle the Refund Request in his convenient transaction channel.',
  'Refund_to_Wallet_feature?' => 'Refund to Wallet feature?',
  'If_you_enable_this,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets.' => 'If you enable this  Customers will automatically receive the refunded amount in their wallets.',
  'If_you_disable_this,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If you disable this  the Admin will handle the Refund Request in his convenient transaction channel.',
  'Existing_Customers_can_share_a_referral_code_with_others_to_earn_a_referral_bonus._For_this,_the_new_user_MUST_sign_up_using_the_referral_code_and_make_their_first_purchase.' => 'Existing Customers can share a referral code with others to earn a referral bonus. For this  the new user MUST sign up using the referral code and make their first purchase.',
  'Referral_Earning?' => 'Referral Earning?',
  'If_you_enable_this,_Customers_can_earn_points_by_referring_others_to_sign_up_&_purchase_from_your_business.' => 'If you enable this  Customers can earn points by referring others to sign up & purchase from your business.',
  'If_you_disable_this,_the_referral-earning_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the referral-earning feature will be hidden from the Customer App & Website.',
  'If_you_activate_this_feature,_customers_need_to_verify_their_account_information_via_OTP_during_the_signup_process.' => 'If you activate this feature  customers need to verify their account information via OTP during the signup process.',
  'Customer_Verification?' => 'Customer Verification?',
  'If_you_enable_this,_Customers_have_to_verify_their_account_via_OTP.' => 'If you enable this  Customers have to verify their account via OTP.',
  'If_you_disable_this,_Customers_don’t_need_to_verify_their_account_via_OTP.' => 'If you disable this  Customers don’t need to verify their account via OTP.',
  'ex_:_Item_is_Broken' => 'Ex : Item is Broken',
  'add_Business_Module' => 'Add Business Module',
  'Add_New_Business_Module' => 'Add New Business Module',
  'Business_Module' => 'Business Module',
  'Ex:_Grocery,eCommerce,Pharmacy,etc.' => 'Ex: Grocery,eCommerce,Pharmacy etc.',
  '*Set_up_your_New_Business_Module_type_theme_icon_&_thumbnail.' => '*Set up your New Business Module, type, theme, icon & thumbnail.',
  'business_modules' => 'Business modules',
  'business_module_type' => 'Business module type',
  'business_module_type_change_warning' => 'NB: You CANNOT change the module type in the future after you save changes.',
  'Write_a_short_description_of_your_new_business_module_within_100_words_(550_characters)' => 'Write a short description of your new business module within 100 words (550 characters)',
  'Choose_a_Theme_for_the_module' => 'Choose a Theme for the module',
  'Add_Module' => 'Add Module',
  'Update_Business_Module' => 'Update Business Module',
  'Edit_Business_Module' => 'Edit Business Module',
  'choose_theme' => 'Choose theme',
  'business_Modules' => 'Business Modules',
  'business_Module_list' => 'Business Module list',
  'ex_:_Search_Module_by_Name' => 'Ex : Search Module by Name',
  'business_Module_type' => 'Business Module type',
  1 => '1',
  'Create_Business_Module' => 'Create Business Module',
  'To_create_a_new_business_module,_go_to:_‘Module_Setup’_→_‘Add_Business_Module.’' => 'To create a new business module  go to: ‘Module Setup’ → ‘Add Business Module.’',
  2 => '2',
  'Add_Module_to_Zone' => 'Add Module to Zone',
  'Go_to_‘Zone_Setup’→_‘Business_Zone_List’→_‘Zone_Settings’→_Choose_Payment_Method→Add_Business_Module_into_Zone_with_Parameters.' => 'Go to ‘Zone Setup’→ ‘Business Zone List’→ ‘Zone Settings’→ Choose Payment Method→Add Business Module into Zone with Parameters.',
  3 => '3',
  'Create_Stores' => 'Create Stores',
  'Select_your_Module_from_the_Module_Section,_Click_→_’Store_Management’→’Add_Store’→Add_Store_details_&_select_Zone_to_integrate_Module+Zone+Store.' => 'Select your Module from the Module Section  Click → ’Store Management’→’Add Store’→Add Store details & select Zone to integrate Module+Zone+Store.',
  'Want_to_activate_this' => 'Want to activate this',
  'Business_Module?' => 'Business Module?',
  'Want_to_deactivate_this' => 'Want to deactivate this',
  'If_you_activate_this_business_module,_all_its_features_and_functionalities_will_be_available_and_accessible_to_all_users.' => 'If you activate this business module  all its features and functionalities will be available and accessible to all users.',
  'If_you_deactivate_this_business_module,_all_its_features_and_functionalities_will_be_disabled_and_hidden_from_users.' => 'If you deactivate this business module  all its features and functionalities will be disabled and hidden from users.',
  'Add_New_Business_Zone' => 'Add New Business Zone',
  'Create_&_connect_dots_in_a_specific_area_on_the_map_to_add_a_new_business_zone.' => 'Create & connect dots in a specific area on the map to add a new business zone.',
  'Use_this_‘Hand_Tool’_to_find_your_target_zone.' => 'Use this ‘Hand Tool’ to find your target zone.',
  'Use_this_‘Shape_Tool’_to_point_out_the_areas_and_connect_the_dots._Minimum_3_points/dots_are_required.' => 'Use this ‘Shape Tool’ to point out the areas and connect the dots. Minimum 3 points/dots are required.',
  'Choose_your_preferred_language_&_set_your_zone_name.' => 'Choose your preferred language & set your zone name.',
  'business_Zone' => 'Business Zone',
  'Write_a_New_Business_Zone_Name' => 'Write a New Business Zone Name',
  'Search_Business_Zone' => 'Search Business Zone',
  'zone_Id' => 'Zone Id',
  'Want_to_disable_‘Cash_On_Delivery’?' => 'Want to disable ‘Cash On Delivery’?',
  'If_yes,_the_Cash_on_Delivery_option_will_be_hidden_during_checkout.' => 'If yes  the Cash on Delivery option will be hidden during checkout.',
  'The_Business_Zone_will_NOT_work_if_you_don’t_select_your_business_module_&_payment_method.' => 'The Business Zone will NOT work if you don’t select your business module & payment method.',
  'Want_to_enable_‘Cash_On_Delivery’?' => 'Want to enable ‘Cash On Delivery’?',
  'If_yes,_Customers_can_choose_the_‘Cash_On_Delivery’_option_during_checkout.' => 'If yes  Customers can choose the ‘Cash On Delivery’ option during checkout.',
  'New_Business_Zone_Created_Successfully!' => 'New Business Zone Created Successfully!',
  'NEXT_IMPORTANT_STEP:_You_need_to_select_‘Payment_Method’_and_add_‘Business_Modules’_with_other_details_from_the_Zone_Settings._If_you_don’t_finish_the_setup,_the_Zone_you_created_won’t_function_properly.' => 'NEXT IMPORTANT STEP: You need to select ‘Payment Method’ and add ‘Business Modules’ with other details from the Zone Settings. If you don’t finish the setup  the Zone you created won’t function properly.',
  'Go_to_zone_Settings' => 'Go to zone Settings',
  'Want_to_enable_‘Digital_Payment’?' => 'Want to enable ‘Digital Payment’?',
  'If_yes,_Customers_can_choose_the_‘Digital_Payment’_option_during_checkout.' => 'If yes  Customers can choose the ‘Digital Payment’ option during checkout.',
  'Want_to_disable_‘Digital_Payment’?' => 'Want to disable ‘Digital Payment’?',
  'If_yes,_the_digital_payment_option_will_be_hidden_during_checkout.' => 'If yes  the digital payment option will be hidden during checkout.',
  'Want_to_Delete_this_Zone?' => 'Want to Delete this Zone?',
  'If_yes,_all_its_modules,_stores,_and_products_will_be_DELETED_FOREVER.' => 'If yes  all its modules  stores  and products will be DELETED FOREVER.',
  'Want_to_activate_this_Zone?' => 'Want to activate this Zone?',
  'Want_to_deactivate_this_Zone?' => 'Want to deactivate this Zone?',
  'If_you_activate_this_zone,_Customers_can_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you activate this zone  Customers can see all stores & products available under this Zone from the Customer App & Website.',
  'If_you_deactivate_this_zone,_Customers_Will_NOT_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you deactivate this zone  Customers Will NOT see all stores & products available under this Zone from the Customer App & Website.',
  'Zone_Settings' => 'Zone Settings',
  'Select_Payment_Method' => 'Select Payment Method',
  'NB:_MUST_select_at_least_‘one’_payment_method.' => 'NB: MUST select at least ‘one’ payment method.',
  'Choose_Business_Module' => 'Choose Business Module',
  'enter_Amount' => 'Enter Amount',
  'Ex:_Item_is_Broken' => 'Ex: Item is Broken',
  'When this field is active, user can cancel an order with proper reason.' => 'When this field is active  user can cancel an order with proper reason.',
  'Refund_Reason' => 'Refund Reason',
  'Admin_Mail_Templates' => 'Admin Mail Templates',
  'Store_Mail_Templates' => 'Store Mail Templates',
  'Delivery_Man_Mail_Templates' => 'Delivery Man Mail Templates',
  'User_Mail_Templates' => 'User Mail Templates',
  'Want_to_enable_Forget_Password_mail?' => 'Want to enable Forget Password mail?',
  'Want_to_disable_Forget_Password_mail?' => 'Want to disable Forget Password mail?',
  'Want_to_enable_Store_Registration_mail?' => 'Want to enable Store Registration mail?',
  'Want_to_disable_Store_Registration_mail?' => 'Want to disable Store Registration mail?',
  'Want_to_enable_Delivery_Man_Registration_mail?' => 'Want to enable Delivery Man Registration mail?',
  'Want_to_disable_Delivery_Man_Registration_mail?' => 'Want to disable Delivery Man Registration mail?',
  'Want_to_enable_Withdraw_Request_mail?' => 'Want to enable Withdraw Request mail?',
  'Want_to_disable_Withdraw_Request_mail?' => 'Want to disable Withdraw Request mail?',
  'Want_to_enable_Campaign_Request_mail?' => 'Want to enable Campaign Request mail?',
  'Want_to_disable_Campaign_Request_mail?' => 'Want to disable Campaign Request mail?',
  'Want_to_enable_Refund_Request_mail?' => 'Want to enable Refund Request mail?',
  'Want_to_disable_Refund_Request_mail?' => 'Want to disable Refund Request mail?',
  'Want_to_enable_Login_mail?' => 'Want to enable Login mail?',
  'Want_to_disable_Login_mail?' => 'Want to disable Login mail?',
  'Want_to_enable_Store_approve_mail?' => 'Want to enable Store approve mail?',
  'Want_to_disable_Store_approve_mail?' => 'Want to disable Store approve mail?',
  'Want_to_enable_Store_deny_mail?' => 'Want to enable Store deny mail?',
  'Want_to_disable_Store_deny_mail?' => 'Want to disable Store deny mail?',
  'Want_to_enable_Withdraw_approve_mail?' => 'Want to enable Withdraw approve mail?',
  'Want_to_disable_Withdraw_approve_mail?' => 'Want to disable Withdraw approve mail?',
  'Want_to_enable_Withdraw_deny_mail?' => 'Want to enable Withdraw deny mail?',
  'Want_to_disable_Withdraw_deny_mail?' => 'Want to disable Withdraw deny mail?',
  'Want_to_enable_Campaign_Approve_mail?' => 'Want to enable Campaign Approve mail?',
  'Want_to_disable_Campaign_Approve_mail?' => 'Want to disable Campaign Approve mail?',
  'Want_to_enable_Campaign_deny_mail?' => 'Want to enable Campaign deny mail?',
  'Want_to_disable_Campaign_deny_mail?' => 'Want to disable Campaign deny mail?',
  'Want_to_enable_Delivery_Man_approve_mail?' => 'Want to enable Delivery Man approve mail?',
  'Want_to_disable_Delivery_Man_approve_mail?' => 'Want to disable Delivery Man approve mail?',
  'Want_to_enable_Delivery_Man_deny_mail?' => 'Want to enable Delivery Man deny mail?',
  'Want_to_disable_Delivery_Man_deny_mail?' => 'Want to disable Delivery Man deny mail?',
  'Want_to_enable_Suspend_mail?' => 'Want to enable Suspend mail?',
  'Want_to_disable_Suspend_mail?' => 'Want to disable Suspend mail?',
  'Want_to_enable_cash_collect_mail?' => 'Want to enable cash collect mail?',
  'Want_to_disable_cash_collect_mail?' => 'Want to disable cash collect mail?',
  'Want_to_enable_User_Registration_mail?' => 'Want to enable User Registration mail?',
  'Want_to_disable_User_Registration_mail?' => 'Want to disable User Registration mail?',
  'Want_to_enable_User_login_mail?' => 'Want to enable User login mail?',
  'Want_to_disable_User_login_mail?' => 'Want to disable User login mail?',
  'Want_to_enable_Order_Verification_mail?' => 'Want to enable Order Verification mail?',
  'Want_to_disable_Order_Verification_mail?' => 'Want to disable Order Verification mail?',
  'Want_to_enable_Place_Order_mail?' => 'Want to enable Place Order mail?',
  'Want_to_disable_Place_Order_mail?' => 'Want to disable Place Order mail?',
  'Want_to_enable_Refund_Order_mail?' => 'Want to enable Refund Order mail?',
  'Want_to_disable_Refund_Order_mail?' => 'Want to disable Refund Order mail?',
  'Want_to_enable_Refund_Request_Denied_mail?' => 'Want to enable Refund Request Denied mail?',
  'Want_to_disable_Refund_Request_Denied_mail?' => 'Want to disable Refund Request Denied mail?',
  'Want_to_enable_add_fund_mail?' => 'Want to enable add fund mail?',
  'Want_to_disable_add_fund_mail?' => 'Want to disable add fund mail?',
  'Send_Mail_On_‘Forgot_Password’?' => 'Send Mail On ‘Forgot Password’ ',
  'If_a_user_clicks_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_to_the_admin.' => 'If a user clicks ‘Forgot Password’ during login  an automated email will be sent to the admin.',
  'Choose_a_related_email_template_theme_for_the_purpose_for_which_you_are_creating_the_email.' => 'Choose a related email template theme for the purpose for which you are creating the email.',
  'Select_Theme' => 'Select Theme',
  'Choose_Logo' => 'Choose Logo',
  'Upload_your_company_logo_in_1:1_format._This_will_show_above_the_Main_Title_of_the_email.' => 'Upload your company logo in 1:1 format. This will show above the Main Title of the email.',
  'Write_a_Title' => 'Write a Title',
  'Give_your_email_a_‘Catchy_Title’_to_help_the_reader_understand_easily.' => 'Give your email a ‘Catchy Title’ to help the reader understand easily.',
  'Write_a_message_in_the_Email_Body' => 'Write a message in the Email Body',
  'you_can_add_your_message_using_placeholders_to_include_dynamic_content._Here_are_some_examples_of_placeholders_you_can_use:' => 'You can add your message using placeholders to include dynamic content. Here are some examples of placeholders you can use:',
  'the_name_of_the_user.' => 'The name of the user.',
  'the_name_of_the_delivery_person.' => 'The name of the delivery person.',
  'the_name_of_the_store.' => 'The name of the store.',
  'the_order_id.' => 'The order id.',
  'the_transaction_id.' => 'The transaction id.',
  'Add_Button_&_Link' => 'Add Button & Link',
  'Specify_the_text_and_URL_for_the_button_that_you_want_to_include_in_your_email.' => 'Specify the text and URL for the button that you want to include in your email.',
  'Change_Banner_Image_if_needed' => 'Change Banner Image if needed',
  'Choose_the_relevant_banner_image_for_the_email_theme_you_use_for_this_mail.' => 'Choose the relevant banner image for the email theme you use for this mail.',
  'Add_Content_to_Email_Footer' => 'Add Content to Email Footer',
  'Write_text_on_the_footer_section_of_the_email,_and_choose_important_page_links_and_social_media_links.' => 'Write text on the footer section of the email  and choose important page links and social media links.',
  'Create_a_copyright_notice' => 'Create a copyright notice',
  'Include_a_copyright_notice_at_the_bottom_of_your_email_to_protect_your_content.' => 'Include a copyright notice at the bottom of your email to protect your content.',
  'Save_and_publish' => 'Save and publish',
  'Once_you\'ve_set_up_all_the_elements_of_your_email_template,_save_and_publish_it_for_use.' => 'Once you ve set up all the elements of your email template  save and publish it for use.',
  'Got_It' => 'Got It',
  'Receive_Mail_On_‘New_Store_Registration’?' => 'Receive Mail On ‘New Store Registration’ ',
  'If_a_store_registers_from_the_customer_website_or_app_or_store_app,_admin_will_receive_an_automated_email.' => 'If a store registers from the customer website or app or store app  admin will receive an automated email.',
  'If_enabled,_the_admin_will_get_an_automated_email_when_a_store_registers.' => 'If enabled  the admin will get an automated email when a store registers.',
  'If_disabled,_the_admin_will_not_get_an_automated_email_when_a_store_registers.' => 'If disabled  the admin will not get an automated email when a store registers.',
  'Receive_Mail_On_‘New_Deliveryman_Registration’?' => 'Receive Mail On ‘New Deliveryman Registration’ ',
  'If_Deliveryman_registers_from_the_customer_App_or_Website_or_Deliveryman_App,_Admin_receive_an_automated_email.' => 'If Deliveryman registers from the customer App or Website or Deliveryman App  Admin receive an automated email.',
  'If_enabled,_the_admin_will_get_an_automated_email_when_a_deliveryman_registers.' => 'If enabled  the admin will get an automated email when a deliveryman registers.',
  'If_disabled,_the_admin_will_not_get_an_automated_email_when_a_deliveryman_registers.' => 'If disabled  the admin will not get an automated email when a deliveryman registers.',
  'If_a_store_requests_for_a_withdrawal,_admin_will_receive_an_automated_email.' => 'If a store requests for a withdrawal  admin will receive an automated email.',
  'If_enabled,_admin_will_receive_an_email_when_a_store_requests_a_withdrawal.' => 'If enabled  admin will receive an email when a store requests a withdrawal.',
  'If_disabled,_admin_will_not_receive_an_email_when_a_store_requests_a_withdrawal.' => 'If disabled  admin will not receive an email when a store requests a withdrawal.',
  'Receive_Mail_on_‘Campaign_Join_Request’?' => 'Receive Mail on ‘Campaign Join Request’ ',
  'If_a_store_requests_to_join_campaign_an_automated_email_will_be_sent_to_the_admin.' => 'If a store requests to join campaign an automated email will be sent to the admin.',
  'If_enabled,_the_admin_will_receive_a_mail_when_a_store_requests_to_join_a_campaign.' => 'If enabled  the admin will receive a mail when a store requests to join a campaign.',
  'If_disabled,_the_admin_will_not_receive_mail_when_a_store_requests_to_join_a_campaign.' => 'If disabled  the admin will not receive mail when a store requests to join a campaign.',
  'Receive_Mail_on_‘Refund_Request’?' => 'Receive Mail on ‘Refund Request’ ',
  'If_a_customer_requests_a_refund,_the_admin_will_receive_an_automated_email_on_the_customer`s_Refund_Request.' => 'If a customer requests a refund  the admin will receive an automated email on the customer`s Refund Request.',
  'If_enabled,_the_admin_will_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website.' => 'If enabled  the admin will receive an email when a customer requests a refund from the customer app or website.',
  'If_disabled,_the_admin_will_not_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website. ' => 'If disabled  the admin will not receive an email when a customer requests a refund from the customer app or website. ',
  'Receive_Login_Notification_via_Mail?' => 'Receive Login Notification via Mail ',
  'If_a_user_login_from_their_respective_system,_the_Admin_gets_notified_via_email.' => 'If a user login from their respective system  the Admin gets notified via email.',
  'If_enabled,_Admin_will_receive_an_automated_email_on_every_login_from_all_users.' => 'If enabled  Admin will receive an automated email on every login from all users.',
  'If_disabled,_Admin_will_not_receive_an_automated_email_on_every_login_of_users.' => 'If disabled  Admin will not receive an automated email on every login of users.',
  'If_enabled,_admin_will_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If enabled  admin will receive an email when a user click on ‘Forgot Password.’',
  'If_disabled,_admin_will_not_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If disabled  admin will not receive an email when a user click on ‘Forgot Password.’',
  'If_a_Store_registers_from_the_Customer_app_or_Website,_Admin_Landing_Page_or_Store_app,_they_will_get_a_confirmation_email.' => 'If a Store registers from the Customer app or Website  Admin Landing Page or Store app  they will get a confirmation email.',
  'If_enabled,_stores_will_get_a_registration_confirmation_email_when_they_register.' => 'If enabled  stores will get a registration confirmation email when they register.',
  'If_disabled,_stores_will_not_get_a_registration_confirmation_email_when_a_store_registers.' => 'If disabled  stores will not get a registration confirmation email when a store registers.',
  'New_Store_Approval' => 'New Store Approval',
  'Send_Mail_on_New_Store_Approval?' => 'Send Mail on New Store Approval ',
  'If_Admin_accepts_a_Store’s_self-registration,_the_store_will_get_an_automatic_approval_mail_from_the_system.' => 'If Admin accepts a Store’s self-registration  the store will get an automatic approval mail from the system.',
  'If_enabled,_Users_will_get_a_confirmation_email_when_the_Admin_approves_the_registration.' => 'If enabled  Users will get a confirmation email when the Admin approves the registration.',
  'If_disabled,_Users_will_not_get_a_registration_approval_email.' => 'If disabled  Users will not get a registration approval email.',
  'New_Store_Rejection' => 'New Store Rejection',
  'Send_Mail_on_‘New_Store_Rejection’?' => 'Send Mail on ‘New Store Rejection’ ',
  'If_Admin_rejects_a_Store’s_self-registration,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin rejects a Store’s self-registration  the store will get an automatic disapproval mail from the system.',
  'If_enabled,_Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_registration_request.' => 'If enabled  Users will receive a confirmation email when the Admin rejects their registration request.',
  'If_disabled,__Users_will_not_get_a_registration_rejection_mail.' => 'If disabled   Users will not get a registration rejection mail.',
  'If disabled, Stores will not receive any Withdraw Approval mail.' => 'If disabled  Stores will not receive any Withdraw Approval mail.',
  'Withdraw_Approval' => 'Withdraw Approval',
  'Send_Mail_On_Withdraw_approve?' => 'Send Mail On Withdraw approve ',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_approves_it,_the_Store_will_get_an_automated_Withdraw_Approval_email_from_the_system' => 'If a Store requests for a withdrawal and Admin approves it  the Store will get an automated Withdraw Approval email from the system',
  'If_enabled,_Stores_will_receive_an_approval_mail_for_requesting_a_withdrawal.' => 'If enabled  Stores will receive an approval mail for requesting a withdrawal.',
  'If_disabled,_Stores_will_not_receive_any_Withdraw_Approval_mail.' => 'If disabled  Stores will not receive any Withdraw Approval mail.',
  'Withdraw_Rejection' => 'Withdraw Rejection',
  'Send_Mail_on_‘Withdraw_Rejection’?' => 'Send Mail on ‘Withdraw Rejection’ ',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_rejects_it,_the_Store_will_get_an_automated_Withdraw_Rejection_email_from_the_system.' => 'If a Store requests for a withdrawal and Admin rejects it  the Store will get an automated Withdraw Rejection email from the system.',
  'If_enabled,_Stores_will_not_receive_any_Withdrawal_Rejection_mail.' => 'If enabled  Stores will not receive any Withdrawal Rejection mail.',
  'If_disabled,_Stores_will_receive_an_automated_mail_from_the_system_when_the_Admin_Rejects_their_Withdraw_Request.' => 'If disabled  Stores will receive an automated mail from the system when the Admin Rejects their Withdraw Request.',
  'If_a_Store_requests_to_join_a_campaign,_they_will_receive_an_automated_mail_for_successful_registration.' => 'If a Store requests to join a campaign  they will receive an automated mail for successful registration.',
  'If_enabled,_Stores_will_receive_an_automated_confirmation_mail_that_their_join_request_is_successful.' => 'If enabled  Stores will receive an automated confirmation mail that their join request is successful.',
  'If_disabled,_Stores_will_not_receive_any_confirmation_mail_on_campaign_join_request.' => 'If disabled  Stores will not receive any confirmation mail on campaign join request.',
  'Campaign_Join_Approval' => 'Campaign Join Approval',
  'Campaign_Join_Rejection' => 'Campaign Join Rejection',
  'Send_Mail_on_‘Campaign_Join_Approval’?' => 'Send Mail on ‘Campaign Join Approval’ ',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_approves_their_joining_request,_they_get_an_automated_Approval_email_from_the_system.' => 'If a Store requests to join a Campaign and Admin approves their joining request  they get an automated Approval email from the system.',
  'If_enabled,_Stores_will_receive_an_email_when_Admin_approves_their_Campaign_Join_Request.' => 'If enabled  Stores will receive an email when Admin approves their Campaign Join Request.',
  'Send_Mail_on_‘Campaign_Join_Rejection’?' => 'Send Mail on ‘Campaign Join Rejection’ ',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_rejects_their_joining_request,_they_will_get_an_automated_Rejection_email_from_the_system.' => 'If a Store requests to join a Campaign and Admin rejects their joining request  they will get an automated Rejection email from the system.',
  'If_enabled,_Stores_will_receive_an_email_on_campaign_joining_Rejection.' => 'If enabled  Stores will receive an email on campaign joining Rejection.',
  'If_disabled,_Stores_will_not_receive_any_email_on_campaign_joining_Rejection.' => 'If disabled  Stores will not receive any email on campaign joining Rejection.',
  'New_Deliveryman_Registration' => 'New Deliveryman Registration',
  'New_Deliveryman_Approval' => 'New Deliveryman Approval',
  'New_Deliveryman_Rejection' => 'New Deliveryman Rejection',
  'Account_Suspension' => 'Account Suspension',
  'Cash_Collection' => 'Cash Collection',
  'Forgot_Password' => 'Forgot Password',
  'Send_Mail_on_New_Deliveryman_Registration?' => 'Send Mail on New Deliveryman Registration ',
  'If_a_Deliveryman_registers_from_the_Customer_app_or_Website,_Admin_Landing_Page_or_Store_app,_they_will_get_a_Registration_Confirmation_email.' => 'If a Deliveryman registers from the Customer app or Website  Admin Landing Page or Store app  they will get a Registration Confirmation email.',
  'If_enabled,_Deliverymen_will_receive_an_automated_mail_from_the_system_when_their_registration_is_successful.' => 'If enabled  Deliverymen will receive an automated mail from the system when their registration is successful.',
  'If_disabled,_Deliverymen_will_not_receive_any_registration_confirmation_email.' => 'If disabled  Deliverymen will not receive any registration confirmation email.',
  'Send_Mail_on_New_Deliveryman_Rejection?' => 'Send Mail on New Deliveryman Rejection ',
  'If_Admin_rejects_a_Deliveryman’s_self-registration,_the_Deliveryman_will_get_an_automatic_rejection_mail.' => 'If Admin rejects a Deliveryman’s self-registration  the Deliveryman will get an automatic rejection mail.',
  'If_enabled,__Users_will_receive_an_email_when_the_admin_rejects_their_registration_request.' => 'If enabled   Users will receive an email when the admin rejects their registration request.',
  'If_disabled,_Users_will_not_receive_any_email_upon_rejection_for_registration.' => 'If disabled  Users will not receive any email upon rejection for registration.',
  'Send_Mail_On_Deliveryman’s_‘Account_Suspension’?' => 'Send Mail On Deliveryman’s ‘Account Suspension’ ',
  'If_Store/Admin_wants,_they_can_suspend_a_Deliveryman’s_account._If_a_Store_or_Admin_suspends_a_Deliveryman’s_account,_he_will_receive_an_automated_email.' => 'If Store/Admin wants  they can suspend a Deliveryman’s account. If a Store or Admin suspends a Deliveryman’s account  he will receive an automated email.',
  'If_enabled,_deliverymen_will_receive_an_email_for_account_suspension.' => 'If enabled  deliverymen will receive an email for account suspension.',
  'If_disabled,_deliverymen_will_not_receive_an_email_for_account_suspension.' => 'If disabled  deliverymen will not receive an email for account suspension.',
  'Send_Mail_on_‘Cash_Collection’?' => 'Send Mail on ‘Cash Collection’ ',
  'If_Admin_or_Store_collects_cash_from_a_Deliveryman,_he_will_receive_an_automated_email_from_the_system_showing_how_much_cash_is_collected.' => 'If Admin or Store collects cash from a Deliveryman  he will receive an automated email from the system showing how much cash is collected.',
  'If_enabled,_the_Deliveryman_will_receive_an_email_after_the_Admin/Store_collects_cash_from_him.' => 'If enabled  the Deliveryman will receive an email after the Admin/Store collects cash from him.',
  'If_disabled,_the_Deliveryman_will_not_receive_any_email_on_Cash_Collection.' => 'If disabled  the Deliveryman will not receive any email on Cash Collection.',
  'If_a_Deliveryman_tap_on_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_from_the_system_with_a_Reset_Password_Link.' => 'If a Deliveryman tap on ‘Forgot Password’ during login  an automated email will be sent from the system with a Reset Password Link.',
  'If_enabled,_the_Deliveryman_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'If enabled  the Deliveryman will receive an automated email with a Reset Password link.',
  'If_disabled,_the_Deliveryman_will_not_receive_any_for_password_reset.' => 'If disabled  the Deliveryman will not receive any for password reset.',
  'Customer_Mail_Templates' => 'Customer Mail Templates',
  'New_Customer_Registration' => 'New Customer Registration',
  'Receive_Mail_On_‘New_Customer_Registration’?' => 'Receive Mail On ‘New Customer Registration’ ',
  'If_a_user_registers_or_sign_up_from_the_Customer_App_or_Website,_they_will_receive_an_automated_confirmation.' => 'If a user registers or sign up from the Customer App or Website  they will receive an automated confirmation.',
  'If_enabled,_customers_will_receive_a_confirmation_email_that_their_registration_was_successful.' => 'If enabled  customers will receive a confirmation email that their registration was successful.',
  'If_disabled,_customers_will_receive_a_registration_confirmation_email.' => 'If disabled  customers will receive a registration confirmation email.',
  'Send_Mail_On_‘Registration_OTP’?' => 'Send Mail On ‘Registration OTP’ ',
  'Customers_will_receive_an_automated_email_with_an_OTP_to_confirm_their_registration.' => 'Customers will receive an automated email with an OTP to confirm their registration.',
  'If_enabled,_Customers_will_receive_OTP_in_their_mail_to_confirm_registration.' => 'If enabled  Customers will receive OTP in their mail to confirm registration.',
  'If_disabled,_Customers_will_not_receive_any_email_on_registration_OTP.' => 'If disabled  Customers will not receive any email on registration OTP.',
  'Send_Mail_On_‘Login_OTP’?' => 'Send Mail On ‘Login OTP’ ',
  'Customers_will_receive_an_OTP_every_time_they_log_in_to_their_account_via_the_Customer_App_or_Website.' => 'Customers will receive an OTP every time they log in to their account via the Customer App or Website.',
  'If_enabled,_customers_will_receive_a_login_OTP_email_every_time_they_log_in_to_their_account.' => 'If enabled  customers will receive a login OTP email every time they log in to their account.',
  'If_disabled,_customers_will_not_receive_any_OTP_email_during_Login.' => 'If disabled  customers will not receive any OTP email during Login.',
  'Delivery_Verification' => 'Delivery Verification',
  'Send_Mail_On_‘Delivery_Verification’?' => 'Send Mail On ‘Delivery Verification’ ',
  'Customers_will_receive_a_Delivery_Verification_code_via_email_during_delivery._The_Customer_then_gives_the_code_to_the_Deliveryman_to_confirm_delivery.' => 'Customers will receive a Delivery Verification code via email during delivery. The Customer then gives the code to the Deliveryman to confirm delivery.',
  'If_enabled,_Customers_will_receive_a_Verification_code_via_mail_during_delivery_and_Deliveryman_can_verify_the_order_with_the_given_code.' => 'If enabled  Customers will receive a Verification code via mail during delivery and Deliveryman can verify the order with the given code.',
  'If_disabled,_Customers_will_not_receive_any_Verification_code_via_mail_for_delivery_verification.' => 'If disabled  Customers will not receive any Verification code via mail for delivery verification.',
  'Order_Placement' => 'Order Placement',
  'Send_Mail_On_‘Order_Placement’?' => 'Send Mail On ‘Order Placement’ ',
  'Customers_will_receive_an_automated_email_after_a_successful_order_placement.' => 'Customers will receive an automated email after a successful order placement.',
  'If_enabled,_customers_will_get_an_automatic_confirmation_mail_for_successful_Order_Placement_with_an_invoice.' => 'If enabled  customers will get an automatic confirmation mail for successful Order Placement with an invoice.',
  'If_disabled,_customers_will_NOT_get_any_Order_Placement_email.' => 'If disabled  customers will NOT get any Order Placement email.',
  'Customers_will_get_an_automated_email_when_they_receive_a_refund_to_their_wallet_from_Admin_with_refund_details.' => 'Customers will get an automated email when they receive a refund to their wallet from Admin with refund details.',
  'If_enabled,_Customers_will_get_an_automated_email_when_they_receive_a_refund.' => 'If enabled  Customers will get an automated email when they receive a refund.',
  'If_disabled,_Customers_will_not_receive_any_mail_on_Refund_Orders.' => 'If disabled  Customers will not receive any mail on Refund Orders.',
  'If_a_Customer_clicks_on_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_with_a_Reset_Password_Link.' => 'If a Customer clicks on ‘Forgot Password’ during login  an automated email will be sent with a Reset Password Link.',
  'If_enabled,_the_Customer_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'If enabled  the Customer will receive an automated email with a Reset Password link.',
  'Refund_Request_Rejected' => 'Refund Request Rejected',
  'Fund_Add' => 'Fund Add',
  'Send_Mail_On_‘Refund_Request_Rejected’?' => 'Send Mail On ‘Refund Request Rejected’ ',
  'Customers_will_receive_an_automated_mail_from_the_system_if_the_Admin_rejects_their_Refund_Request.' => 'Customers will receive an automated mail from the system if the Admin rejects their Refund Request.',
  'If_enabled,_Customers_will_receive_a_mail_when_Admin_rejects_their_Refund_Request.' => 'If enabled  Customers will receive a mail when Admin rejects their Refund Request.',
  'If_disabled,_Customers_will_not_receive_any_mail_for_Refund_Request_rejection.' => 'If disabled  Customers will not receive any mail for Refund Request rejection.',
  'Send_Mail_On_‘Fund_Add’?' => 'Send Mail On ‘Fund Add’ ',
  'Customers_will_receive_an_automated_mail_from_the_system_when_Admin_add_fund_to_their_Wallet.' => 'Customers will receive an automated mail from the system when Admin add fund to their Wallet.',
  'If_enabled,_customers_will_receive_an_email_when_Admin_adds_funds_to_their_wallet.' => 'If enabled  customers will receive an email when Admin adds funds to their wallet.',
  'If_disabled,_Customers_will_not_receive_an_email_on_Added_Funds.' => 'If disabled  Customers will not receive an email on Added Funds.',
  'When_disabled,_item_management_feature_will_be_hidden_from_store_panel_&_store_app' => 'When disabled  item management feature will be hidden from store panel & store app',
  'When_enabled,_store_owners_can_see_customer_feedback_in_the_store_panel_&_store_app.' => 'When enabled  store owners can see customer feedback in the store panel & store app.',
  'Enable_or_Disable_Point_of_Sale_(POS)_in_the_store_panel.' => 'Enable or Disable Point of Sale (POS) in the store panel.',
  'When_enabled,_store_owner_can_take_scheduled_orders_from_customers.' => 'When enabled  store owner can take scheduled orders from customers.',
  'Store-managed_Delivery' => 'Store-managed Delivery',
  'When_this_option_is_enabled,_stores_must_deliver_orders_using_their_own_deliverymen._Plus,_stores_will_get_the_option_to_add_their_own_deliverymen_from_the_store_panel.' => 'When this option is enabled  stores must deliver orders using their own deliverymen. Plus  stores will get the option to add their own deliverymen from the store panel.',
  'When_enabled,_customers_can_make_home_delivery_orders_from_this_store.' => 'When enabled  customers can make home delivery orders from this store.',
  'takeaway' => 'Takeaway',
  'When_enabled,_customers_can_place_takeaway_orders_from_this_store.' => 'When enabled  customers can place takeaway orders from this store.',
  'Specify_the_minimum_order_amount_required_for_customers_when_ordering_from_this_store.' => 'Specify the minimum order amount required for customers when ordering from this store.',
  'Set_the_total_time_to_process_the_order_after_order_confirmation.' => 'Set the total time to process the order after order confirmation.',
  'Set_the_total_time_to_deliver_products.' => 'Set the total time to deliver products.',
  'When_enabled,_admin_will_only_receive_the_certain_commission_percentage_he_set_for_this_store._Otherwise,_the_system_default_commission_will_be_applied.' => 'When enabled  admin will only receive the certain commission percentage he set for this store. Otherwise  the system default commission will be applied.',
  'Want_to_delete_this_schedule?' => 'Want to delete this schedule?',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted' => 'If you select Yes  the time schedule will be deleted.',
  'Define_the_food_type_this_store_can_sell.' => 'Define the food type this store can sell.',
  'See_how_it_works!' => 'See how it works!',
  'Visit_Now' => 'Visit Now',
  'Ex_:_Manage_your_daily_life_on_one_platform' => 'Ex : Manage your daily life on one platform',
  'Write_the_title_within_50_characters' => 'Write the title within 50 characters',
  'Write_the_sub_title_within_50_characters' => 'Write the sub title within 50 characters',
  'Ex_:_More_than_just_a_reliable_eCommerce_platform' => 'Ex : More than just a reliable eCommerce platform',
  'Ex_:_Your_eCommerce_venture_starts_here' => 'Ex : Your eCommerce venture starts here',
  'Ex_:_Enjoy_all_services_in_one_platform' => 'Ex : Enjoy all services in one platform',
  'NB_:_All_the_modules_and_their_information_will_be_dynamically_added_from_the_module_setup_section._You_just_need_to_add_the_title_and_subtitle_of_the_Module_List_Section.' => 'NB : All the modules and their information will be dynamically added from the module setup section. You just need to add the title and subtitle of the Module List Section.',
  'Ex_:_Earn_Point' => 'Ex : Earn Point',
  'Ex_:_By_referring_your_friend' => 'Ex : By referring your friend',
  'Promotional_Banner_List' => 'Promotional Banner List',
  'Features_List' => 'Features List',
  'Ex_:_Remarkable_Features_that_You_Can_Count' => 'Ex : Remarkable Features that You Can Count',
  'Ex_:_Jam-packed_with_outstanding_features…' => 'Ex : Jam-packed with outstanding features…',
  'Ex_:_Shopping' => 'Ex : Shopping',
  'Ex_:_Best_shopping_experience' => 'Ex : Best shopping experience',
  'See_the_changes_here.' => 'See the changes here.',
  'Download_Store_App_Section' => 'Download Store App Section',
  'When_disabled,_the_Play_Store_download_button_will_be_hidden_from_the_landing_page' => 'When disabled  the Play Store download button will be hidden from the landing page',
  'Want_to_enable_the_Play_Store_button_for_Store_App?' => 'Want to enable the Play Store button for Store App ',
  'Want_to_disable_the_Play_Store_button_for_Store_App?' => 'Want to disable the Play Store button for Store App ',
  'If_enabled,_the_Store_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the Store app download button will be visible on the Landing page.',
  'If_disabled,_this_button_will_be_hidden_from_the_landing_page.' => 'If disabled  this button will be hidden from the landing page.',
  'When_disabled,_the_App_Store_download_button_will_be_hidden_from_the_landing_page' => 'When disabled  the App Store download button will be hidden from the landing page',
  'Want_to_enable_the_App_Store_button_for_Store_App?' => 'Want to enable the App Store button for Store App ',
  'Want_to_disable_the_App_Store_button_for_Store_App?' => 'Want to disable the App Store button for Store App ',
  'Download_Deliveryman_App_Section' => 'Download Deliveryman App Section',
  'Want_to_enable_the_Play_Store_button_for_Deliveryman_App?' => 'Want to enable the Play Store button for Deliveryman App ',
  'Want_to_disable_the_Play_Store_button_for_Deliveryman_App?' => 'Want to disable the Play Store button for Deliveryman App ',
  'If_enabled,_the_Deliveryman_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the Deliveryman app download button will be visible on the Landing page.',
  'Want_to_enable_the_App_Store_button_for_Deliveryman_App?' => 'Want to enable the App Store button for Deliveryman App ',
  'Want_to_disable_the_App_Store_button_for_Deliveryman_App?' => 'Want to disable the App Store button for Deliveryman App ',
  'Text_:_Icon_ratio_(1:1)_and_max_size_2_MB.' => 'Text : Icon ratio (1:1) and max size 2 MB.',
  'Want_to_enable_this_feature?' => 'Want to enable this feature ',
  'Want_to_disable_this_feature?' => 'Want to disable this feature ',
  'It_will_be_available_on_the_landing_page.' => 'It will be available on the landing page.',
  'It_will_be_hidden_from_the_landing_page.' => 'It will be hidden from the landing page.',
  'see_the_changes_here' => 'See the changes here',
  'Write_the_title_within_200_characters' => 'Write the title within 200 characters',
  'Ex_:_Contact_Us' => 'Ex : Contact Us',
  'Ex_:_Any_questions_or_remarks_?_Just_write_us_a_message!' => 'Ex : Any questions or remarks   Just write us a message!',
  'cancellation_policy' => 'Cancellation policy',
  'status updated!' => 'Status updated!',
  'cancellation_policy_updated' => 'Cancellation policy updated',
  'refund_policy' => 'Refund policy',
  'Minimum_User_App_Version' => 'Minimum User App Version',
  'The_minimum_user_app_version_required_for_the_app_functionality.' => 'The minimum user app version required for the app functionality.',
  'Download_URL_for_User_App' => 'Download URL for User App',
  'Users_will_download_the_latest_user_app_version_using_this_URL.' => 'Users will download the latest user app version using this URL.',
  'Store_App_Version_Control' => 'Store App Version Control',
  'Minimum_Store_App_Version' => 'Minimum Store App Version',
  'The_minimum_store_app_version_required_for_the_app_functionality.' => 'The minimum store app version required for the app functionality.',
  'Download_URL_for_Store_App' => 'Download URL for Store App',
  'Users_will_download_the_latest_store_app_using_this_URL.' => 'Users will download the latest store app using this URL.',
  'Users_will_download_the_latest_store_app_version_using_this_URL.' => 'Users will download the latest store app version using this URL.',
  'Deliveryman_App_Version_Control' => 'Deliveryman App Version Control',
  'Minimum_Deliveryman_App_Version' => 'Minimum Deliveryman App Version',
  'The_minimum_deliveryman_app_version_required_for_the_app_functionality.' => 'The minimum deliveryman app version required for the app functionality.',
  'Download_URL_for_Deliveryman_App' => 'Download URL for Deliveryman App',
  'Users_will_download_the_latest_deliveryman_app_version_using_this_URL.' => 'Users will download the latest deliveryman app version using this URL.',
  'What_is_App_Version?' => 'What is App Version ',
  'This_app_version_defines_the_Store,_Deliveryman,_and_User_app_version_of_6amMart.' => 'This app version defines the Store  Deliveryman  and User app version of 6amMart.',
  'It_doesn’t_represent_the_Play_Store_or_App_Store_version.' => 'It doesn’t represent the Play Store or App Store version.',
  'The_app_download_link_is_the_URL_from_which_users_can_update_the_app_by_clicking_the_`Update_App`_button_from_their_app.' => 'The app download link is the URL from which users can update the app by clicking the `Update App` button from their app.',
  'Admin_login_page' => 'Admin login page',
  'Icon_ratio_(1:1)_and_max_size_2_MB.' => 'Icon ratio (1:1) and max size 2 MB.',
  'Special_Feature_Section ' => 'Special Feature Section ',
  'Special_Feature_List_Section ' => 'Special Feature List Section ',
  'Want_to_delete_this_feature_?' => 'Want to delete this feature  ',
  'If_yes,_It_will_be_removed_from_this_list_and_the_landing_page.' => 'If yes  It will be removed from this list and the landing page.',
  'this_feature?' => 'This feature ',
  'If_yes,_it_will_be_available_on_the_landing_page.' => 'If yes  it will be available on the landing page.',
  'If_yes,_it_will_be_hidden_from_the_landing_page.' => 'If yes  it will be hidden from the landing page.',
  'Join_as_a_Seller_Section' => 'Join as a Seller Section',
  'Write_the_title_within_20_characters' => 'Write the title within 20 characters',
  'The_website_page_where_people_will_register_as_sellers.' => 'The website page where people will register as sellers.',
  'Join_as_a_Deliveryman_Section' => 'Join as a Deliveryman Section',
  'The_website_page_where_people_will_register_as_deliveryman.' => 'The website page where people will register as deliveryman.',
  'Want_to_enable_the_Play_Store_button_for_User_App?' => 'Want to enable the Play Store button for User App ',
  'Want_to_disable_the_Play_Store_button_for_User_App?' => 'Want to disable the Play Store button for User App ',
  'If_enabled,_the_User_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the User app download button will be visible on the Landing page.',
  'Want_to_enable_the_App_Store_button_for_User_App?' => 'Want to enable the App Store button for User App ',
  'Want_to_disable_the_App_Store_button_for_User_App?' => 'Want to disable the App Store button for User App ',
  'contact_section_updated' => 'Contact section updated',
  'newsletter' => 'Newsletter',
  'Ex_:_Sign_Up_to_Our_Newsletter' => 'Ex : Sign Up to Our Newsletter',
  'Ex_:_Receive_Latest_News,_Updates_and_Many_Other_News_Every_Week' => 'Ex : Receive Latest News  Updates and Many Other News Every Week',
  'Footer_Article' => 'Footer Article',
  'Ex_:_6amMart_is_a_complete_package!__It`s_time_to_empower_your_multivendor_online_business_with__powerful_features!' => 'Ex : 6amMart is a complete package!  It`s time to empower your multivendor online business with  powerful features!',
  '(size: 1:1)' => '(size: 1:1)',
  'Download_Seller_App_From_Playstore' => 'Download Seller App From Playstore',
  'Download_the_User_App_from_Playstore' => 'Download the User App from Playstore',
  'Download_the_User_App_from_Applestore' => 'Download the User App from Applestore',
  'The_button_will_direct_users_to_the_link_contained_within_this_box.' => 'The button will direct users to the link contained within this box.',
  'When_disabled,_the_Play_Store_download_button_will_be_hidden_from_the_React_landing_page.' => 'When disabled  the Play Store download button will be hidden from the React landing page.',
  'If_enabled,_the_User_app_download_button_will_be_visible_on_React_Landing_page.' => 'If enabled  the User app download button will be visible on React Landing page.',
  'If_disabled,_this_button_will_be_hidden_from_the_React_landing_page.' => 'If disabled  this button will be hidden from the React landing page.',
  'When_disabled,_the_User_app_download_button_will_be_hidden_on_React_Landing_page.' => 'When disabled  the User app download button will be hidden on React Landing page.',
  'Want_to_enable_the_App_Store_button_for_User_App?
                                                ' => 'Want to enable the App Store button for User App
                                                ',
  'If_enabled,_the_Store_app_download_button_will_be_visible_on_React_Landing_page.' => 'If enabled  the Store app download button will be visible on React Landing page.',
  'When_disabled,_the_App_Store_download_button_will_be_hidden_from_the_React_landing_page' => 'When disabled  the App Store download button will be hidden from the React landing page',
  'select_all' => 'Select all',
  'provide_dm_earning' => 'Provide dm earning',
  'Transaction Overview' => 'Transaction Overview',
  'Hello, here you can manage your transactions.' => 'Hello  here you can manage your transactions.',
  'customer_management' => 'Customer management',
  'Hello,_here_you_can_manage_your_users_by_zone.' => 'Hello  here you can manage your users by zone.',
  'All_Zones' => 'All Zones',
  'Jan' => 'Jan',
  'Feb' => 'Feb',
  'Mar' => 'Mar',
  'Apr' => 'Apr',
  'May' => 'May',
  'Jun' => 'Jun',
  'Jul' => 'Jul',
  'Aug' => 'Aug',
  'Sep' => 'Sep',
  'Oct' => 'Oct',
  'Nov' => 'Nov',
  'Dec' => 'Dec',
  'Deliveryman_Section_Content' => 'Deliveryman Section Content',
  'Download the Deliveryman App' => 'Download the Deliveryman App',
  'Footer_Content' => 'Footer Content',
  'fixed_Data' => 'Fixed Data',
  'Newsleter Section Content ' => 'Newsleter Section Content ',
  'short_Description' => 'Short Description',
  'promotional_Banner' => 'Promotional Banner',
  '(size: 1:5)' => '(size: 1:5)',
  'landing_page_newsletter_content_updated' => 'Landing page newsletter content updated',
  'landing_page_footer_content_updated' => 'Landing page footer content updated',
  'Ex_:_Search_by_type...' => 'Ex : Search by type...',
  'Vehicle_status_updated' => 'Vehicle status updated',
  'edit_vehicle' => 'Edit vehicle',
  'delete_vehicle' => 'Delete vehicle',
  'Previous_customer' => 'Previous customer',
  'Next_customer' => 'Next customer',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted.' => 'If you select Yes  the time schedule will be deleted.',
  'ex_:_bike' => 'Ex : bike',
  'Sun' => 'Sun',
  'Mon' => 'Mon',
  'Tue' => 'Tue',
  'Wed' => 'Wed',
  'Thu' => 'Thu',
  'Fri' => 'Fri',
  'Sat' => 'Sat',
  'Day' => 'Day',
  'This_Year' => 'This Year',
  'This_Month' => 'This Month',
  'This_Week' => 'This Week',
  'import_items_file' => 'Import items file',
  'cutlery' => 'Cutlery',
  'Email_Template' => 'Email Template',
  'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery  Coupon discount & item discounts(partial according to order commission).',
  'Are_you_sure_you_want_to_remove_this_image' => 'Are you sure you want to remove this image',
  'Are_you_sure_you_want_to_remove_this_image.' => 'Are you sure you want to remove this image.',
  'Image_removed_successfully' => 'Image removed successfully',
  'By Turning ON' => 'By Turning ON',
  'feature_updated_successfully' => 'Feature updated successfully',
  'Write_the_sub_title_within_100_characters' => 'Write the sub title within 100 characters',
  'Write_the_title_within_40_characters' => 'Write the title within 40 characters',
  'Write_the_sub_title_within_80_characters' => 'Write the sub title within 80 characters',
  'Write_the_title_within_180_characters' => 'Write the title within 180 characters',
  'Write_the_title_within_80_characters' => 'Write the title within 80 characters',
  'Write_the_title_within_240_characters' => 'Write the title within 240 characters',
  'Write_the_title_within_250_characters' => 'Write the title within 250 characters',
  'Write_the_title_within_120_characters' => 'Write the title within 120 characters',
  'Write_the_title_within_30_characters' => 'Write the title within 30 characters',
  'Write_the_title_within_15_characters' => 'Write the title within 15 characters',
  'Write_the_title_within_60_characters' => 'Write the title within 60 characters',
  'Write_the_title_within_65_characters' => 'Write the title within 65 characters',
  'Write_the_title_within_35_characters' => 'Write the title within 35 characters',
  'Write_the_title_within_140_characters' => 'Write the title within 140 characters',
  'Write_the_title_within_100_characters' => 'Write the title within 100 characters',
  'promotion_banner' => 'Promotion banner',
  'If_you_want_to_upload_one_banner_then_you_have_to_upload_it_in_6:1_ratio_otherwise_the_ratio_will_be_same_as_before.' => 'If you want to upload one banner then you have to upload it in 6:1 ratio otherwise the ratio will be same as before.',
  'Don’t_forget_to_click_the_‘Add_Module’_button_below_to_save_the_new_business_module.' => 'Don’t forget to click the ‘Add Module’ button below to save the new business module.',
  'unavailable_item_note' => 'Unavailable item note',
  'delivery_instruction' => 'Delivery instruction',
  'default_data_is_required' => 'Default data is required',
  'Write_a_message_in_the_Notification_Body' => 'Write a message in the Notification Body',
  'If You Want to Change Text Color' => 'If You Want to Change Text Color',
  'Replace the text with ($ text $) format' => 'Replace the text with ($ text $) format',
  'If You Want to Change Text Color To Primary Color' => 'If You Want to Change Text Color To Primary Color',
  'Cookies Text' => 'Cookies Text',
  'Ex_:_Cookies_Text' => 'Ex : Cookies Text',
  'react_site_setup' => 'React site setup',
  'React Site Setup' => 'React Site Setup',
  'React license code' => 'React license code',
  'React Domain' => 'React Domain',
  'license_code_is_required' => 'License code is required',
  'doamain_is_required' => 'Doamain is required',
  'Invalid_license_code_or_unregistered_domain' => 'Invalid license code or unregistered domain',
  'react_data_updated' => 'React data updated',
  'System_Configeration_Instruction' => 'System Configeration Instruction',
  'mm/dd/yyyy' => 'Mm/dd/yyyy',
  'Note_about_transaction_or_request' => 'Note about transaction or request',
  'Monitor_store’s_business_analytics_&_Reports' => 'Monitor store’s business analytics & Reports',
  'Total_canceled' => 'Total canceled',
  'Total_ongoing' => 'Total ongoing',
  'Total_delivered' => 'Total delivered',
  'Filters' => 'Filters',
  'Proceed,_If_thermal_printer_is_ready.' => 'Proceed  If thermal printer is ready.',
  'Admin Comission' => 'Admin Comission',
  'Delivery Comission' => 'Delivery Comission',
  'Total canceled' => 'Total canceled',
  'Total ongoing' => 'Total ongoing',
  'Total delivered' => 'Total delivered',
  'Display_name' => 'Display name',
  'ok' => 'Ok',
  'Delete Bank Info ?' => 'Delete Bank Info  ',
  'withdraw_list' => 'Withdraw list',
  'provide dm earning' => 'Provide dm earning',
  'withdraw list' => 'Withdraw list',
  'customer management' => 'Customer management',
  'custom role' => 'Custom role',
  'Select All' => 'Select All',
  'Site' => 'Site',
  'Key' => 'Key',
  'Secret' => 'Secret',
  'System_Configuration_Instruction' => 'System Configuration Instruction',
  'Join us' => 'Join us',
  'Turotial' => 'Turotial',
  'Tour' => 'Tour',
  'Search' => 'Search',
  'Please_contact_us_for_any_queries;_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Ex:_Copyright_2023_6amMart._All_right_reserved' => 'Ex: Copyright 2023 6amMart. All right reserved',
  'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned' => 'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned',
  'Add_dynamic_url_to_secure_admin_login_access.' => 'Add dynamic url to secure admin login access.',
  'Add_dynamic_url_to_secure_admin_employee_login_access.' => 'Add dynamic url to secure admin employee login access.',
  'Add_dynamic_url_to_secure_store_login_access.' => 'Add dynamic url to secure store login access.',
  'Add_dynamic_url_to_secure_store_employee_login_access.' => 'Add dynamic url to secure store employee login access.',
  'New_Customer_Growth' => 'New Customer Growth',
  'Want to update admin info ?' => 'Want to update admin info  ',
  'Don`t_Logout' => 'Don`t Logout',
  'Newsletter Section Content ' => 'Newsletter Section Content ',
  'select_payment_mode' => 'Select payment mode',
  'delivery_man_id' => 'Delivery man id',
  'store_amount' => 'Store amount',
  'updated_at' => 'Updated at',
  'original_delivery_charge' => 'Original delivery charge',
  'parcel_catgory_id' => 'Parcel catgory id',
  'dm_tips' => 'Dm tips',
  'delivery_fee_comission' => 'Delivery fee comission',
  'admin_expense' => 'Admin expense',
  'store_expense' => 'Store expense',
  'discount_amount_by_store' => 'Discount amount by store',
  'طعام' => 'طعام',
  'system_addons' => 'System addons',
  'How_the_Setting_Works' => 'How the Setting Works',
  'get_your_zip_file_from_the_purchased_theme_and_upload_it_and_activate_theme_with_your_Codecanyon_username_and_purchase_code' => 'Get your zip file from the purchased theme and upload it and activate theme with your Codecanyon username and purchase code',
  'now_you’ll_be_successfully_able_to_use_the_theme_for_your_6Valley_website' => 'Now you’ll be successfully able to use the theme for your 6Valley website',
  'N:B you_can_upload_only_6Valley’s_theme_templates' => 'N:B you can upload only 6Valley’s theme templates',
  'upload_theme' => 'Upload theme',
  'please_make_sure' => 'Please make sure',
  'your_server_php' => 'Your server php',
  'value_is_grater
                                       _or_equal_to_180M' => 'Value is grater
                                        or equal to 180M',
  'current_value_is' => 'Current value is',
  'value_is_grater_or_equal_to_200M' => 'Value is grater or equal to 200M',
  'updated successfully!' => 'Updated successfully!',
  'instructions' => 'Instructions',
  'value_is_grater
                                   _or_equal_to_20MB' => 'Value is grater
                                    or equal to 20MB',
  'value_is_grater_or_equal_to_20MB' => 'Value is grater or equal to 20MB',
  'file_upload_successfully!' => 'File upload successfully!',
  'are_you_sure_you_want_to_delete_the_theme' => 'Are you sure you want to delete the theme',
  'once_you_delete' => 'Once you delete',
  'you_will_lost_the_this_theme' => 'You will lost the this theme',
  'are_you_sure?' => 'Are you sure ',
  'want_to_change_status' => 'Want to change status',
  'codecanyon_username' => 'Codecanyon username',
  'purchase_code' => 'Purchase code',
  'activate' => 'Activate',
  'file_delete_successfully' => 'File delete successfully',
  'Codecanyon' => 'Codecanyon',
  'usename' => 'Usename',
  'Ex:_Riad_Uddin' => 'Ex: Riad Uddin',
  'Purchase' => 'Purchase',
  'Ex: 987652' => 'Ex: 987652',
  'Activate' => 'Activate',
  'activated_successfully' => 'Activated successfully',
  'Payment Setup' => 'Payment Setup',
  'payment_gateway_configuration' => 'Payment gateway configuration',
  'payment_gateway_title' => 'Payment gateway title',
  'addon_menus' => 'Addon menus',
  'payment_setup' => 'Payment setup',
  'sms_setup' => 'Sms setup',
  'sms_gateways_configuration' => 'Sms gateways configuration',
  'Browse Web Button' => 'Browse Web Button',
  'Browse Web Button Enabled for Landing Page' => 'Browse Web Button Enabled for Landing Page',
  'Browse Web Button Disabled for Landing Page' => 'Browse Web Button Disabled for Landing Page',
  'Browse Web button is enabled now everyone can use or see the button' => 'Browse Web button is enabled now everyone can use or see the button',
  'Browse Web button is disabled now no one can use or see the button' => 'Browse Web button is disabled now no one can use or see the button',
  'Web Link' => 'Web Link',
  'Ex: https://6ammart-web.6amtech.com/' => 'Ex: https://6ammart-web.6amtech.com/',
  'background_colors' => 'Background colors',
  'default_title_is_required' => 'Default title is required',
  'default_description_is_required' => 'Default description is required',
  'default_name_is_required' => 'Default name is required',
  'default_reason_is_required' => 'Default reason is required',
  'default_unit_is_required' => 'Default unit is required',
  'bank info' => 'Bank info',
  'item_default_name_required' => 'Item default name required',
  'item_default_description_required' => 'Item default description required',
  'vendor_employee' => 'Vendor employee',
  'please_select_a_store_first' => 'Please select a store first',
  'maximum_cart_quantity' => 'Maximum cart quantity',
  'Want_to_enable_additional_charge?' => 'Want to enable additional charge ',
  'Want_to_disable_additional_charge?' => 'Want to disable additional charge ',
  'If_you_enable_this,_additional_charge_will_be_added_with_order_amount,_it_will_be_added_in_admin_wallet' => 'If you enable this  additional charge will be added with order amount  it will be added in admin wallet',
  'If_you_disable_this,_additional_charge_will_not_be_added_with_order_amount.' => 'If you disable this  additional charge will not be added with order amount.',
  'additional_charge' => 'Additional charge',
  'Admin' => 'Admin',
  'ssl commerz' => 'Ssl commerz',
  'If_you_disable_this,_additional_charge_will_be_added_with_order_amount.' => 'If you disable this  service charge will be added with order amount.',
  'Set_a_value_that_will_be_added_to_Admin’s_commission.' => 'Set a value that will be added to Admin’s commission.',
  'order_proof' => 'Order proof',
  'add_order_proof' => 'Add order proof',
  'order_proof_added' => 'Order proof added',
  'order_proof_image' => 'Order proof image',
  'order_proof_image_removed_successfully' => 'Order proof image removed successfully',
  'Invalid date range!' => 'Invalid date range!',
  'partial_payment' => 'Partial payment',
  'partial_payment_?' => 'Partial payment  ',
  'If_you_enable_this,_customers_can_choose_partial_payment_during_checkout.' => 'If you enable this  customers can choose partial payment during checkout.',
  'If_you_disable_this,_the_partial_payment_feature_will_be_hidden.' => 'If you disable this  the partial payment feature will be hidden.',
  'With_this_feature,_customers_can_choose_partial_payment_with_wallet_and_another_payment_method.' => 'With this feature  customers can choose partial payment with wallet and another payment method.',
  'partially_paid' => 'Partially paid',
  'partial payment' => 'Partial payment',
  'partially_paid_amount' => 'Partially paid amount',
  'meta_data' => 'Meta data',
  'store_meta_data' => 'Store meta data',
  'store_title' => 'Store title',
  'store_image' => 'Store image',
  'meta_title' => 'Meta title',
  'meta_description' => 'Meta description',
  'store_meta_image' => 'Store meta image',
  'meta_image' => 'Meta image',
  'default_meta_title_is_required' => 'Default meta title is required',
  'default_meta_description_is_required' => 'Default meta description is required',
  'meta_data_updated' => 'Meta data updated',
  'Customer_can_add_fund_to_wallet_by_payment_module_&_earn_bonus_balance.' => 'Customer can add fund to wallet by payment module & earn bonus balance.',
  'wallet_add_fund_bonus_status_feature?' => 'Wallet add fund bonus status feature ',
  'wallet_add_fund_bonus' => 'Wallet add fund bonus',
  'wallet_add_fund_bonus_feature?' => 'Wallet add fund bonus feature ',
  'If_you_enable_this,_Customers_will_automatically_receive_the_bonus_amount_in_their_wallets.' => 'If you enable this  Customers will automatically receive the bonus amount in their wallets.',
  'If_you_disable_this,_Customers_will_not_automatically_receive_the_bonus_amount_in_their_wallets.' => 'If you disable this  Customers will not automatically receive the bonus amount in their wallets.',
  'Wallet_add_fund_bonus_percentage_(%)' => 'Wallet add fund bonus percentage (%)',
  'can_combine_payment' => 'Can combine payment',
  'cod' => 'Cod',
  'With_this_feature,_customers_can_choose_additional_charge_with_wallet_and_another_payment_method.' => 'With this feature  customers can choose additional charge with wallet and another payment method.',
  'charge_amount' => 'Charge amount',
  'additional_charge_name' => 'Additional charge name',
  'take_picture_before_complete' => 'Take picture before complete',
  'dm_picture_upload_status' => 'Dm picture upload status',
  'picture_upload_before_complete?' => 'Picture upload before complete ',
  'bonus' => 'Bonus',
  'bonuses' => 'Bonuses',
  'wallet_bonus_setup' => 'Wallet bonus setup',
  'bonus_type' => 'Bonus type',
  'percentage' => 'Percentage',
  'bonus_amount' => 'Bonus amount',
  'minimum_add_amount' => 'Minimum add amount',
  'maximum_bonus' => 'Maximum bonus',
  'bonus_list' => 'Bonus list',
  'Ex_:_bonus_title' => 'Ex : bonus title',
  'bonus_title' => 'Bonus title',
  'bonus_info' => 'Bonus info',
  'started_on' => 'Started on',
  'expires_on' => 'Expires on',
  'bonus_added_successfully' => 'Bonus added successfully',
  'Want to delete this bonus ?' => 'Want to delete this bonus  ',
  'bonus_status_updated' => 'Bonus status updated',
  'maximum_bonus_amount' => 'Maximum bonus amount',
  'wallet_bonus_update' => 'Wallet bonus update',
  'bonus_updated_successfully' => 'Bonus updated successfully',
  'edit_bonus' => 'Edit bonus',
  'bonus_deleted_successfully' => 'Bonus deleted successfully',
  'add_fund_bonus' => 'Add fund bonus',
  'Ø·Ø¹Ø§Ù…' => 'Ø·Ø¹Ø§Ù…',
  'admin_default_landing_page' => 'Admin default landing page',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_‘Turn_Off’ _landing_page.' => 'All your apps and customer website will be disabled until you ‘Turn Off’  landing page.',
  'want_to_turn_off_admin_landing_page' => 'Want to turn off admin landing page',
  'landing_page_is_on.' => 'Landing page is on.',
  'want_to_turn_on_admin_landing_page' => 'Want to turn on admin landing page',
  'landing_page_is_off.' => 'Landing page is off.',
  'integrate_landing_page_via' => 'Integrate landing page via',
  'file_upload' => 'File upload',
  'none' => 'None',
  'landing_page_url' => 'Landing page url',
  'updated_successfully!' => 'Updated successfully!',
  'url_saved_successfully!' => 'Url saved successfully!',
  'zip_file_is_required' => 'Zip file is required',
  'customer_can_add_fund_to_wallet' => 'Customer can add fund to wallet',
  'With_this_feature,_customers_can_add_fund_to_wallet_if_the_payment_module_is_available.' => 'With this feature  customers can add fund to wallet if the payment module is available.',
  'add_fund_status' => 'Add fund status',
  'add_fund_to_Wallet_feature?' => 'Add fund to Wallet feature ',
  'If_you_enable_this,_Customers_can_add_fund_to_wallet_using_payment_module' => 'If you enable this  Customers can add fund to wallet using payment module',
  'If_you_disable_this,_add_fund_to_wallet_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  add fund to wallet will be hidden from the Customer App & Website.',
  'Current_value' => 'Current value',
  '5. For parent category  position  will 0 and for sub category it will be 1.' => '5. For parent category  position  will 0 and for sub category it will be 1.',
  '5. You can get module id and  zone id from their list  please input the right ids.' => '5. You can get module id and  zone id from their list  please input the right ids.',
  '6. By default status will be 1  please input the right ids.' => '6. By default status will be 1  please input the right ids.',
  '6. For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.' => '6. For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.',
  '6. You can upload your product images in product folder from gallery  and copy image`s path.' => '6. You can upload your product images in product folder from gallery  and copy image`s path.',
  '7. For a category parent_id will be empty  for sub category it will be the category id.' => '7. For a category parent id will be empty  for sub category it will be the category id.',
  '7. You can upload your store images in store folder from gallery  and copy image`s path.' => '7. You can upload your store images in store folder from gallery  and copy image`s path.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_âCancel_Orderâ_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the âCancel Orderâ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_âCancel_Orderâ_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the âCancel Orderâ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_‘Cancel_Order‘_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the ‘Cancel Order‘ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  10 => '10',
  '4. You can get store id  module id and unit id from their list  please input the right ids.' => '4. You can get store id  module id and unit id from their list  please input the right ids.',
  'filter_criteria' => 'filter criteria',
  'order_type' => 'order type',
  'erwrw' => 'erwrw',
  100093 => '100093',
  'search_bar_content' => 'search bar content',
  'zones' => 'zones',
  '  +data[count].name +  ' => '  +data[count].name +  ',
  '$mod- module_name' => '$mod- module name',
  '* When this discount is available  is applied on all the items in this stores.' => '* When this discount is available  is applied on all the items in this stores.',
  '*By Turning OFF mail configuration  all your mailing services will be off.' => '*By Turning OFF mail configuration  all your mailing services will be off.',
  '*By Turning ON Refund Mode  Customers Can Sent Refund Requests' => '*By Turning ON Refund Mode  Customers Can Sent Refund Requests',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*If the Admin enables the âRefund Request Modeâ  customers can request a refund.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*If the Admin enables the âRefund Request Modeâ  customers can request a refund.',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’ _customers_can_request_a_refund.' => '*If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'Basic_Campaign_List' => 'Basic Campaign List',
  'Message_Analytics' => 'Message Analytics',
  'Total_Campaign' => 'Total Campaign',
  'Currently_Running' => 'Currently Running',
  'Cmapaign_Name' => 'Cmapaign Name',
  'Start_Date' => 'Start Date',
  'End_Date' => 'End Date',
  'Daily_Start_Time' => 'Daily Start Time',
  'Daily_End_Time' => 'Daily End Time',
  'Total_Store_Joined' => 'Total Store Joined',
  'Search_Criteria' => 'Search Criteria',
  'Search_Bar_Conten' => 'Search Bar Conten',
  'N/A' => 'N/A',
  'Campaign uploaded successfully' => 'Campaign uploaded successfully',
  'Item_Campaign_List' => 'Item Campaign List',
  'Filter_Criteria' => 'Filter Criteria',
  'Module' => 'Module',
  'Search_Bar_Content' => 'Search Bar Content',
  'Item_Name' => 'Item Name',
  'Categrory_Name' => 'Categrory Name',
  'Campaign updated successfully' => 'Campaign updated successfully',
  'Sub_Categrory_Name' => 'Sub Categrory Name',
  'Item_Unit' => 'Item Unit',
  'Available_Variations' => 'Available Variations',
  'Discount_Type' => 'Discount Type',
  'Available_Stock' => 'Available Stock',
  'Store_Name' => 'Store Name',
  'order_transactions_report' => 'order transactions report',
  'Transaction_Analytics' => 'Transaction Analytics',
  'Completed_Transactions' => 'Completed Transactions',
  'Refunded_Transactions' => 'Refunded Transactions',
  'Earning_Analytics' => 'Earning Analytics',
  'Admin_Earnings' => 'Admin Earnings',
  'Store_Earnings' => 'Store Earnings',
  'Delivery_Man_Earnings' => 'Delivery Man Earnings',
  'item_name' => 'item name',
  'total_order_count' => 'total order count',
  'unit_price' => 'unit price',
  'total_ratings_given' => 'total ratings given',
  'all_time' => 'all time',
  'store_summary_reports' => 'store summary reports',
  'Analytics' => 'Analytics',
  'new_registered_store' => 'new registered store',
  'completed_orders' => 'completed orders',
  'incomplete_orders' => 'incomplete orders',
  'Payment_Statistics' => 'Payment Statistics',
  'cash_payments' => 'cash payments',
  'digital_payments' => 'digital payments',
  'wallet_payments' => 'wallet payments',
  'Total_Refund_requests' => 'Total Refund requests',
  'Pending_Refund_requests' => 'Pending Refund requests',
  'store_sales_reports' => 'store sales reports',
  'total_tax' => 'total tax',
  'total_commission' => 'total commission',
  'total_store_earning' => 'total store earning',
  'Product_name' => 'Product name',
  'QTY_Sold' => 'QTY Sold',
  'Gross_Sale' => 'Gross Sale',
  'Discount_Given' => 'Discount Given',
  'Coupon_List' => 'Coupon List',
  'Coupon_Title' => 'Coupon Title',
  'Coupon_Code' => 'Coupon Code',
  'Coupon_Type' => 'Coupon Type',
  'Number_of_Uses' => 'Number of Uses',
  'Min_Purchase_Amount' => 'Min Purchase Amount',
  'Max_Discount_Amount' => 'Max Discount Amount',
  'Push_Notification_List' => 'Push Notification List',
  'Notification_Title' => 'Notification Title',
  'Created_At' => 'Created At',
  'Zone' => 'Zone',
  'Targeted Users' => 'Targeted Users',
  'All' => 'All',
  'limited_stock_report' => 'limited stock report',
  'current_stock' => 'current stock',
  'module_name' => 'module name',
  'product_image' => 'product image',
  'store_withdraw_transactions' => 'store withdraw transactions',
  'request_status' => 'request status',
  'requested_amount' => 'requested amount',
  'bank_account_no.' => 'bank account no.',
  'transaction_time' => 'transaction time',
  'collected_amount' => 'collected amount',
  'collected_from' => 'collected from',
  'user_type' => 'user type',
  'references' => 'references',
  'collect_cash_transactions' => 'collect cash transactions',
  'delivery_man_payments' => 'delivery man payments',
  'provided_st' => 'provided st',
  'delivery_man_name' => 'delivery man name',
  'customer_list' => 'customer list',
  'Customer_Analytics' => 'Customer Analytics',
  'Total_Customer' => 'Total Customer',
  'Active_Customer' => 'Active Customer',
  'Inactive_Customer' => 'Inactive Customer',
  'saved_address' => 'saved address',
  'total_wallet_amount' => 'total wallet amount',
  'total_loyality_points' => 'total loyality points',
  'customer_order_list' => 'customer order list',
  'customer_id' => 'customer id',
  11 => '11',
  'Ellen Reyna' => 'Ellen Reyna',
  '+*************' => '+*************',
  '<EMAIL>' => '<EMAIL>',
  8 => '8',
  'subscriber_list' => 'subscriber list',
  'subscribed_at' => 'subscribed at',
  'Addon_List' => 'Addon List',
  'Addon_Name' => 'Addon Name',
  'Store_name' => 'Store name',
  'Food_Campaign_List' => 'Food Campaign List',
  'Categories' => 'Categories',
  'Category_Name' => 'Category Name',
  'Sub_Category_Name' => 'Sub Category Name',
  'Food_Type' => 'Food Type',
  'Available_Addons' => 'Available Addons',
  'Available_From' => 'Available From',
  'Available_Till' => 'Available Till',
  'Tags' => 'Tags',
  'Non_Veg' => 'Non Veg',
  'Wallet_transaction_history' => 'Wallet transaction history',
  'transaction_date' => 'transaction date',
  'Ashek Elahe' => 'Ashek Elahe',
  'inactive_delivery_man' => 'inactive delivery man',
  'delivery_man_type' => 'delivery man type',
  'total_completed' => 'total completed',
  'total_running_orders' => 'total running orders',
  'identity_type' => 'identity type',
  'identinty_number' => 'identinty number',
  'delivery_man_review_list' => 'delivery man review list',
  'review_list' => 'review list',
  'delivery_man_info' => 'delivery man info',
  'total_rating' => 'total rating',
  'average_review' => 'average review',
  'delivery_man_earning_list' => 'delivery man earning list',
  'delivery_fee_earned' => 'delivery fee earned',
  'tips' => 'tips',
  'active_employee' => 'active employee',
  'inactive_employee' => 'inactive employee',
  'joining_date' => 'joining date',
  'office' => 'office',
  'If_you_enable_this,_delivery_man_can_upload_order_proof_before_order_delivery.' => 'If you enable this  delivery man can upload order proof before order delivery.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_delivery_man_app.' => 'If you disable this  this feature will be hidden from the delivery man app.',
  'Review_List' => 'Review List',
  'Order_ID' => 'Order ID',
  'Customer_Name' => 'Customer Name',
  'Rating' => 'Rating',
  'Review' => 'Review',
  'Store_List' => 'Store List',
  'Total_Store' => 'Total Store',
  'Active_Store' => 'Active Store',
  'Inactive_Store' => 'Inactive Store',
  'Newly_Joined' => 'Newly Joined',
  'Store_ID' => 'Store ID',
  'Store_Logo' => 'Store Logo',
  'Ratings' => 'Ratings',
  'Owner_Information' => 'Owner Information',
  'Total_Items' => 'Total Items',
  'Total_Orders' => 'Total Orders',
  'Featured_?' => 'Featured  ',
  'Total_reviews' => 'Total reviews',
  'Category' => 'Category',
  'Store_Wise_Review_List' => 'Store Wise Review List',
  'Store_details' => 'Store details',
  'store_deleted' => 'store deleted',
  'Food_List' => 'Food List',
  'Total_items' => 'Total items',
  'Active_Items' => 'Active Items',
  'Inactive_items' => 'Inactive items',
  'Veg' => 'Veg',
  'Item_List' => 'Item List',
  'Store_Cash_Transactions' => 'Store Cash Transactions',
  'Transaction_ID' => 'Transaction ID',
  'Transaction_Time' => 'Transaction Time',
  'Balance_Before_Transaction' => 'Balance Before Transaction',
  'Transaction_Amount' => 'Transaction Amount',
  'Payment_method' => 'Payment method',
  'Store_Order_Transactions' => 'Store Order Transactions',
  'Order_Time' => 'Order Time',
  'Total_order_amount' => 'Total order amount',
  'Delivery_Fee' => 'Delivery Fee',
  'Vat/Tax' => 'Vat/Tax',
  'Store_Withdraw_Transactions' => 'Store Withdraw Transactions',
  'Requested_Created_At' => 'Requested Created At',
  'Requested_Amount' => 'Requested Amount',
  'Request_Created_At' => 'Request Created At',
  'Store_Details' => 'Store Details',
  'Total_Order' => 'Total Order',
  'Scheduled_Order' => 'Scheduled Order',
  'Pending_Order' => 'Pending Order',
  'Delivered_Order' => 'Delivered Order',
  'Canceled_Order' => 'Canceled Order',
  'Refunded_Order' => 'Refunded Order',
  'Order_Date' => 'Order Date',
  'Item_Price' => 'Item Price',
  'Item_Discount' => 'Item Discount',
  'Coupon_Discount' => 'Coupon Discount',
  'Discounted_Amount' => 'Discounted Amount',
  'Total_Amount' => 'Total Amount',
  'Payment_Status' => 'Payment Status',
  'Order_Status' => 'Order Status',
  'Order_Type' => 'Order Type',
  'Want to delete this' => 'Want to delete this',
  'expense_reports' => 'expense reports',
  'invalid_customer' => 'invalid customer',
  'collect cash' => 'collect cash',
  'customerList' => 'customerList',
  'Bonus_Title' => 'Bonus Title',
  'Ex:_EID_Dhamaka' => 'Ex: EID Dhamaka',
  'Ex:_100' => 'Ex: 100',
  'Ex:_10' => 'Ex: 10',
  'Ex:_1000' => 'Ex: 1000',
  'Ex_:_Search_by_bonus_title' => 'Ex : Search by bonus title',
  'Wallet_bonus_is_only_applicable_when_a_customer_add_fund_to_wallet_via_outside_payment_gateway_!' => 'Wallet bonus is only applicable when a customer add fund to wallet via outside payment gateway !',
  'Customer_will_get_extra_amount_to_his_/_her_wallet_additionally_with_the_amount_he_/_she_added_from_other_payment_gateways._The_bonus_amount_will_be_deduct_from_admin_wallet_&_will_consider_as_admin_expense.' => 'Customer will get extra amount to his / her wallet additionally with the amount he / she added from other payment gateways. The bonus amount will be deduct from admin wallet & will consider as admin expense.',
  'Ex:_Service_Charge' => 'Ex: Service Charge',
  'Want_to_Integrate_Your_Own_Customised_Landing_Page_?' => 'Want to Integrate Your Own Customised Landing Page  ',
  'Read_Instructions' => 'Read Instructions',
  'If_you_want_to_set_up_your_own_landing_page_please_flow_tha_instructions_below' => 'If you want to set up your own landing page please flow tha instructions below',
  'You_can_add_your_customised_landing_page_via_URL_or_upload_ZIP_file_of_the_landing_page.' => 'You can add your customised landing page via URL or upload ZIP file of the landing page.',
  'If_you_want_to_use_URL_option._Just_host_you_landing_page_and_copy_the_page_URL_and_click_save_information.' => 'If you want to use URL option. Just host you landing page and copy the page URL and click save information.',
  'If_you_want_to_Upload_your_landing_page_source_code_file.' => 'If you want to Upload your landing page source code file.',
  'a._Create_an_html_file_named_index.blade.php_and_insert_your_landing_page_design_code_and_make_a_zip_file.' => 'a. Create an html file named index.blade.php and insert your landing page design code and make a zip file.',
  'b._upload_the_zip_file_in_file_upload_section_and_click_save_information.' => 'b. upload the zip file in file upload section and click save information.',
  'a._Create_an_html_file_named' => 'a. Create an html file named',
  '_and_insert_your_landing_page_design_code_and_make_a_zip_file.' => ' and insert your landing page design code and make a zip file.',
  'The_Zip_will_content_only_one_file_named' => 'The Zip will content only one file named',
  'Take_Picture_For_Completing_Delivery' => 'Take Picture For Completing Delivery',
  'If_enabled,_deliverymen_will_see_an_option_to_take_pictures_of_the_delivered_products_when_he_swipes_the_delivery_confirmation_slide.' => 'If enabled  deliverymen will see an option to take pictures of the delivered products when he swipes the delivery confirmation slide.',
  'If_enabled,_customers_need_to_pay_an_extra_charge_while_checking_out_orders.' => 'If enabled  customers need to pay an extra charge while checking out orders.',
  'Set_a_name_for_the_additional_charge,_e.g._“Processing_Fee”.' => 'Set a name for the additional charge  e.g. “Processing Fee”.',
  'Ex:_Processing_Fee' => 'Ex: Processing Fee',
  'Set_the_value_(amount)_customers_need_to_pay_as_additional_charge.' => 'Set the value (amount) customers need to pay as additional charge.',
  'If_enabled,_customers_can_make_partial_payments._For_example,_a_customer_can_pay_$20_initially_out_of_their_$50_payment_&_use_other_payment_methods_for_the_rest._Partial_payments_must_be_made_through_their_wallets.' => 'If enabled  customers can make partial payments. For example  a customer can pay $20 initially out of their $50 payment & use other payment methods for the rest. Partial payments must be made through their wallets.',
  'Can_Pay_the_Rest_Amount_using' => 'Can Pay the Rest Amount using',
  'Set_the_method(s)_that_customers_can_pay_the_remainder_after_partial_payment.' => 'Set the method(s) that customers can pay the remainder after partial payment.',
  'Currently_you_are_using_6amMart_Default_Admin_Landing_Page_Theme.' => 'Currently you are using 6amMart Default Admin Landing Page Theme.',
  'Visit_Landing_Page' => 'Visit Landing Page',
  'Save_Information' => 'Save Information',
  'brrm' => 'brrm',
  'Maximum_Purchase_Quantity_Limit' => 'Maximum Purchase Quantity Limit',
  'If_this_limit_is_exceeded,_customers_can_not_buy_the_item_in_a_single_purchase.' => 'If this limit is exceeded  customers can not buy the item in a single purchase.',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores.' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores.',
  'Bonus_Type' => 'Bonus Type',
  'Bonus_Amount' => 'Bonus Amount',
  'Set_the_bonus_amount/percentage_a_customer_will_receive_after_adding_money_to_his_wallet.' => 'Set the bonus amount/percentage a customer will receive after adding money to his wallet.',
  'Minimum_Add_Money_Amount' => 'Minimum Add Money Amount',
  'Set_the_minimum_add_money_amount_for_a_customer_to_be_eligible_for_the_bonus.' => 'Set the minimum add money amount for a customer to be eligible for the bonus.',
  'Maximum_Bonus' => 'Maximum Bonus',
  'Set_the_maximum_bonus_amount_a_customer_can_receive_for_adding_money_to_his_wallet.' => 'Set the maximum bonus amount a customer can receive for adding money to his wallet.',
  'Short_Description' => 'Short Description',
  'Ex : Search' => 'Ex : Search',
  'Are you sure ' => 'Are you sure ',
  'Are you sure  ' => 'Are you sure  ',
  'Are you sure you want to turn on self-registration for stores ' => 'Are you sure you want to turn on self-registration for stores ',
  'Are you sure' => 'Are you sure',
);
