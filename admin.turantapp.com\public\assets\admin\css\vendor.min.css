.select2-container {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle;
}
.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-inline-start: 0px;
    padding-inline-end: 0px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.select2-container .select2-selection--single .select2-selection__clear {
    position: relative;
}
.select2-container[dir="rtl"]
    .select2-selection--single
    .select2-selection__rendered {
    padding-inline-end: 8px;
    padding-inline-start: 20px;
}
.select2-container .select2-selection--multiple {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-inline-start: 8px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.select2-container .select2-search--inline {
    float: left;
}
.select2-container .select2-search--inline .select2-search__field {
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
    padding: 0;
}
.select2-container
    .select2-search--inline
    .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none;
}
.select2-dropdown {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    box-sizing: border-box;
    display: block;
    position: absolute;
    inset-inline-start: -100000px;
    width: 100%;
    z-index: 1051;
}
.select2-results {
    display: block;
}
.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0;
}
.select2-results__option {
    padding: 6px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-results__option[aria-selected] {
    cursor: pointer;
}
.select2-container--open .select2-dropdown {
    inset-inline-start: 0;
}
.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-search--dropdown {
    display: block;
    padding: 4px;
}
.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    box-sizing: border-box;
}
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none;
}
.select2-search--dropdown.select2-search--hide {
    display: none;
}
.select2-close-mask {
    border: 0;
    margin: 0;
    padding: 0;
    display: block;
    position: fixed;
    inset-inline-start: 0;
    top: 0;
    min-height: 100%;
    min-width: 100%;
    height: auto;
    width: auto;
    opacity: 0;
    z-index: 99;
    background-color: #fff;
}
.select2-hidden-accessible {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important;
    height: 1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    white-space: nowrap !important;
}
.select2-container--default .select2-selection--single {
    display: inline-block;
    width: 100%;
    height: calc(1.6em + 1.21875rem);
    padding: 0.54688rem 0.875rem;
    padding-inline-end: 25px;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.6;
    color: #1e2022;
    background-color: #fff;
    background-clip: padding-box;
    border: 0.0625rem solid #e7eaf3;
    border-radius: 0.3125rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__rendered {
    color: #444;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__placeholder {
    color: #999;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__arrow {
    display: none;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__arrow
    b {
    display: none;
}
.select2-container--default[dir="rtl"]
    .select2-selection--single
    .select2-selection__clear {
    float: left;
}
.select2-container--default[dir="rtl"]
    .select2-selection--single
    .select2-selection__arrow {
    inset-inline-start: 1px;
    inset-inline-end: auto;
}
.select2-container--default.select2-container--disabled
    .select2-selection--single {
    background-color: #eee;
    cursor: default;
}
.select2-container--default.select2-container--disabled
    .select2-selection--single
    .select2-selection__clear {
    display: none;
}
.select2-container--default.select2-container--open
    .select2-selection--single
    .select2-selection__arrow
    b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px;
}
.select2-container--default .select2-selection--multiple {
    display: inline-block;
    width: 100%;
    height: calc(1.6em + 1.21875rem);
    padding: 0.54688rem 0.875rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.6;
    color: #1e2022;
    background-color: #fff;
    background-clip: padding-box;
    border: 0.0625rem solid #e7eaf3;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__rendered {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__rendered
    li {
    list-style: none;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700;
    margin-top: 5px;
    margin-inline-end: 10px;
    padding: 1px;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-inline-end: 5px;
    margin-top: 5px;
    padding: 0 5px;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__choice__remove {
    color: #999;
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    margin-inline-end: 2px;
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__choice__remove:hover {
    color: #333;
}
.select2-container--default[dir="rtl"]
    .select2-selection--multiple
    .select2-search--inline,
.select2-container--default[dir="rtl"]
    .select2-selection--multiple
    .select2-selection__choice {
    float: right;
}
.select2-container--default[dir="rtl"]
    .select2-selection--multiple
    .select2-selection__choice {
    margin-inline-start: 5px;
    margin-inline-end: auto;
}
.select2-container--default[dir="rtl"]
    .select2-selection--multiple
    .select2-selection__choice__remove {
    margin-inline-start: 2px;
    margin-inline-end: auto;
}
.select2-container--default.select2-container--focus
    .select2-selection--multiple {
    border: solid #000 1px;
    outline: 0;
}
.select2-container--default.select2-container--disabled
    .select2-selection--multiple {
    background-color: #eee;
    cursor: default;
}
.select2-container--default.select2-container--disabled
    .select2-selection__choice__remove {
    display: none;
}
.select2-container--default.select2-container--open.select2-container--above
    .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above
    .select2-selection--single {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-container--default.select2-container--open.select2-container--below
    .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--below
    .select2-selection--single {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
}
.select2-container--default .select2-search--inline .select2-search__field {
    background: 0 0;
    border: none;
    outline: 0;
    box-shadow: none;
    -webkit-appearance: textfield;
}
.select2-container--default .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}
.select2-container--default .select2-results__option[role="group"] {
    padding: 0;
}
.select2-container--default .select2-results__option[aria-disabled="true"] {
    color: #999;
}
.select2-container--default .select2-results__option[aria-selected="true"] {
    background-color: #ddd;
}
.select2-container--default .select2-results__option .select2-results__option {
    padding-inline-start: 1em;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__group {
    padding-inline-start: 0;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__option {
    margin-inline-start: -1em;
    padding-inline-start: 2em;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option {
    margin-inline-start: -2em;
    padding-inline-start: 3em;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option {
    margin-inline-start: -3em;
    padding-inline-start: 4em;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option {
    margin-inline-start: -4em;
    padding-inline-start: 5em;
}
.select2-container--default
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option
    .select2-results__option {
    margin-inline-start: -5em;
    padding-inline-start: 6em;
}
.select2-container--default
    .select2-results__option--highlighted[aria-selected] {
    background-color: #5897fb;
    color: #fff;
}
.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px;
}
.select2-container--classic .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #aaa;
    border-radius: 4px;
    outline: 0;
    background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
    background-repeat: repeat-x;
}
.select2-container--classic .select2-selection--single:focus {
    border: 1px solid #5897fb;
}
.select2-container--classic
    .select2-selection--single
    .select2-selection__rendered {
    color: #444;
    line-height: 28px;
}
.select2-container--classic
    .select2-selection--single
    .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700;
    margin-inline-end: 10px;
}
.select2-container--classic
    .select2-selection--single
    .select2-selection__placeholder {
    color: #999;
}
.select2-container--classic
    .select2-selection--single
    .select2-selection__arrow {
    background-color: #ddd;
    border: none;
    border-inline-start: 1px solid #aaa;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 26px;
    position: absolute;
    top: 1px;
    inset-inline-end: 1px;
    width: 20px;
    background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
    background-repeat: repeat-x;
}
.select2-container--classic
    .select2-selection--single
    .select2-selection__arrow
    b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    inset-inline-start: 50%;
    margin-inline-start: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}
.select2-container--classic[dir="rtl"]
    .select2-selection--single
    .select2-selection__clear {
    float: left;
}
.select2-container--classic[dir="rtl"]
    .select2-selection--single
    .select2-selection__arrow {
    border: none;
    border-inline-end: 1px solid #aaa;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    inset-inline-start: 1px;
    inset-inline-end: auto;
}
.select2-container--classic.select2-container--open .select2-selection--single {
    border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open
    .select2-selection--single
    .select2-selection__arrow {
    background: 0 0;
    border: none;
}
.select2-container--classic.select2-container--open
    .select2-selection--single
    .select2-selection__arrow
    b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px;
}
.select2-container--classic.select2-container--open.select2-container--above
    .select2-selection--single {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #fff 0, #eee 50%);
    background-image: -o-linear-gradient(top, #fff 0, #eee 50%);
    background-image: linear-gradient(to bottom, #fff 0, #eee 50%);
    background-repeat: repeat-x;
}
.select2-container--classic.select2-container--open.select2-container--below
    .select2-selection--single {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
    background-repeat: repeat-x;
}
.select2-container--classic .select2-selection--multiple {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
    outline: 0;
}
.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid #5897fb;
}
.select2-container--classic
    .select2-selection--multiple
    .select2-selection__rendered {
    list-style: none;
    margin: 0;
    padding: 0 5px;
}
.select2-container--classic
    .select2-selection--multiple
    .select2-selection__clear {
    display: none;
}
.select2-container--classic
    .select2-selection--multiple
    .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-inline-end: 5px;
    margin-top: 5px;
    padding: 0 5px;
}
.select2-container--classic
    .select2-selection--multiple
    .select2-selection__choice__remove {
    color: #888;
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    margin-inline-end: 2px;
}
.select2-container--classic
    .select2-selection--multiple
    .select2-selection__choice__remove:hover {
    color: #555;
}
.select2-container--classic[dir="rtl"]
    .select2-selection--multiple
    .select2-selection__choice {
    float: right;
    margin-inline-start: 5px;
    margin-inline-end: auto;
}
.select2-container--classic[dir="rtl"]
    .select2-selection--multiple
    .select2-selection__choice__remove {
    margin-inline-start: 2px;
    margin-inline-end: auto;
}
.select2-container--classic.select2-container--open
    .select2-selection--multiple {
    border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open.select2-container--above
    .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-container--classic.select2-container--open.select2-container--below
    .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    outline: 0;
}
.select2-container--classic .select2-search--inline .select2-search__field {
    outline: 0;
    box-shadow: none;
}
.select2-container--classic .select2-dropdown {
    background-color: #fff;
    border: 1px solid transparent;
}
.select2-container--classic .select2-dropdown--above {
    border-bottom: none;
}
.select2-container--classic .select2-dropdown--below {
    border-top: none;
}
.select2-container--classic .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}
.select2-container--classic .select2-results__option[role="group"] {
    padding: 0;
}
.select2-container--classic .select2-results__option[aria-disabled="true"] {
    color: grey;
}
.select2-container--classic
    .select2-results__option--highlighted[aria-selected] {
    background-color: #3875d7;
    color: #fff;
}
.select2-container--classic .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px;
}
.select2-container--classic.select2-container--open .select2-dropdown {
    border-color: #5897fb;
}
.hs-menu-initialized {
    position: relative;
    z-index: 10;
}
.hs-menu-initialized .animated {
    animation-duration: 0.3s;
}
.hs-overflow-x-locked {
    overflow-x: hidden;
}
.hs-mega-menu,
.hs-sub-menu {
    display: none;
    background-color: #fff;
}
@media (min-width: 576px) {
    .hs-mega-menu-desktop-sm,
    .hs-sub-menu-desktop-sm {
        position: absolute;
        inset-inline-start: 0;
        top: 100%;
        z-index: 2;
        margin-top: -0.002rem;
    }
}
@media (min-width: 768px) {
    .hs-mega-menu-desktop-md,
    .hs-sub-menu-desktop-md {
        position: absolute;
        inset-inline-start: 0;
        top: 100%;
        z-index: 2;
        margin-top: -0.002rem;
    }
}
@media (min-width: 992px) {
    .hs-mega-menu-desktop-lg,
    .hs-sub-menu-desktop-lg {
        position: absolute;
        inset-inline-start: 0;
        top: 100%;
        z-index: 2;
        margin-top: -0.002rem;
    }
}
@media (min-width: 1200px) {
    .hs-mega-menu-desktop-xl,
    .hs-sub-menu-desktop-xl {
        position: absolute;
        inset-inline-start: 0;
        top: 100%;
        z-index: 2;
        margin-top: -0.002rem;
    }
}
.hs-sub-menu {
    min-width: 180px;
}
.hs-has-sub-menu {
    overflow: hidden;
    position: relative;
}
.hs-sub-menu-opened {
    overflow: visible;
}
.hs-mega-menu .hs-mega-menu,
.hs-mega-menu .hs-sub-menu,
.hs-menu-vertical .hs-mega-menu,
.hs-menu-vertical .hs-sub-menu,
.hs-sub-menu .hs-mega-menu,
.hs-sub-menu .hs-sub-menu {
    top: 0;
    inset-inline-start: 100%;
    margin-top: 0;
}
.hs-menu-vertical .hs-sub-menu {
    width: auto;
}
.hs-menu-vertical .hs-mega-menu {
    height: 100%;
}
.hs-mobile-state .hs-mega-menu,
.hs-mobile-state .hs-sub-menu {
    position: static;
    visibility: visible;
}
.hs-mobile-state .hs-has-mega-menu[data-max-width] > .hs-mega-menu,
.hs-mobile-state .hs-has-sub-menu[data-max-width] > .hs-sub-menu {
    max-width: none !important;
}
.hs-menu-initialized.hs-rtl {
    direction: rtl;
    unicode-bidi: embed;
}
.hs-menu-initialized.hs-rtl .hs-mega-menu,
.hs-menu-initialized.hs-rtl .hs-sub-menu {
    inset-inline-start: auto;
    inset-inline-end: 0;
}
.hs-menu-initialized.hs-rtl .hs-mega-menu .hs-mega-menu,
.hs-menu-initialized.hs-rtl .hs-mega-menu .hs-sub-menu,
.hs-menu-initialized.hs-rtl .hs-sub-menu .hs-mega-menu,
.hs-menu-initialized.hs-rtl .hs-sub-menu .hs-sub-menu {
    inset-inline-start: auto;
    inset-inline-end: 100%;
}
.hs-menu-initialized:not(.hs-mobile-state) .hs-mega-menu.hs-reversed,
.hs-menu-initialized:not(.hs-mobile-state) .hs-sub-menu.hs-reversed {
    inset-inline-start: auto;
    inset-inline-end: 0;
}
.hs-menu-initialized:not(.hs-mobile-state)
    .hs-mega-menu
    .hs-mega-menu.hs-reversed,
.hs-menu-initialized:not(.hs-mobile-state)
    .hs-mega-menu
    .hs-sub-menu.hs-reversed,
.hs-menu-initialized:not(.hs-mobile-state)
    .hs-sub-menu
    .hs-mega-menu.hs-reversed,
.hs-menu-initialized:not(.hs-mobile-state)
    .hs-sub-menu
    .hs-sub-menu.hs-reversed {
    inset-inline-start: auto;
    inset-inline-end: 100%;
}
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state) .hs-mega-menu.hs-reversed,
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state) .hs-sub-menu.hs-reversed {
    inset-inline-end: auto;
    inset-inline-start: 0;
}
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state)
    .hs-mega-menu
    .hs-mega-menu.hs-reversed,
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state)
    .hs-mega-menu
    .hs-sub-menu.hs-reversed,
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state)
    .hs-sub-menu
    .hs-mega-menu.hs-reversed,
.hs-menu-initialized.hs-rtl:not(.hs-mobile-state)
    .hs-sub-menu
    .hs-sub-menu.hs-reversed {
    inset-inline-end: auto;
    inset-inline-start: 100%;
}
.hs-menu-initialized.hs-menu-horizontal .hs-mega-menu.hs-position-left {
    inset-inline-start: 0;
    inset-inline-end: auto;
}
.hs-menu-initialized.hs-menu-horizontal .hs-mega-menu.hs-position-right {
    inset-inline-start: auto;
    inset-inline-end: 0;
}
.hs-menu-initialized.hs-menu-horizontal .hs-mega-menu.hs-position-center {
    inset-inline-end: auto;
    inset-inline-start: 50%;
    transform: translate(-50%);
}
:root {
    --tagify-dd-color-primary: rgb(53, 149, 246);
    --tagify-dd-bg-color: white;
}
.tagify {
    --tags-border-color: #ddd;
    --tags-hover-border-color: #ccc;
    --tags-focus-border-color: #3595f6;
    --tag-bg: #e5e5e5;
    --tag-hover: #d3e2e2;
    --tag-text-color: black;
    --tag-text-color--edit: black;
    --tag-pad: 0.3em 0.5em;
    --tag-inset-shadow-size: 1.1em;
    --tag-invalid-color: #d39494;
    --tag-invalid-bg: rgba(211, 148, 148, 0.5);
    --tag-remove-bg: rgba(211, 148, 148, 0.3);
    --tag-remove-btn-color: black;
    --tag-remove-btn-bg: none;
    --tag-remove-btn-bg--hover: #c77777;
    --input-color: inherit;
    --tag--min-width: 1ch;
    --tag--max-width: auto;
    --tag-hide-transition: 0.3s;
    --placeholder-color: rgba(0, 0, 0, 0.4);
    --placeholder-color-focus: rgba(0, 0, 0, 0.25);
    --loader-size: 0.8em;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    border: 1px solid #ddd;
    border: 1px solid var(--tags-border-color);
    padding: 0;
    line-height: 1.1;
    cursor: text;
    outline: 0;
    position: relative;
    box-sizing: border-box;
    transition: 0.1s;
}
@keyframes tags--bump {
    30% {
        transform: scale(1.2);
    }
}
@keyframes rotateLoader {
    to {
        transform: rotate(1turn);
    }
}
.tagify:hover {
    border-color: #ccc;
    border-color: var(--tags-hover-border-color);
}
.tagify.tagify--focus {
    transition: 0s;
    border-color: #3595f6;
    border-color: var(--tags-focus-border-color);
}
.tagify[readonly]:not(.tagify--mix) {
    cursor: default;
}
.tagify[readonly]:not(.tagify--mix) > .tagify__input {
    visibility: hidden;
    width: 0;
    margin: 5px 0;
}
.tagify[readonly]:not(.tagify--mix) .tagify__tag > div {
    padding: 0.3em 0.5em;
    padding: var(--tag-pad);
}
.tagify[readonly]:not(.tagify--mix) .tagify__tag > div::before {
    background: linear-gradient(
            45deg,
            var(--tag-bg) 25%,
            transparent 25%,
            transparent 50%,
            var(--tag-bg) 50%,
            var(--tag-bg) 75%,
            transparent 75%,
            transparent
        )
        0/5px 5px;
    box-shadow: none;
    filter: brightness(0.95);
}
.tagify[readonly] .tagify__tag__removeBtn {
    display: none;
}
.tagify--loading .tagify__input::before {
    content: none;
}
.tagify--loading .tagify__input::after {
    content: "";
    vertical-align: middle;
    opacity: 1;
    width: 0.7em;
    height: 0.7em;
    width: var(--loader-size);
    height: var(--loader-size);
    border: 3px solid;
    border-color: #eee #bbb #888 transparent;
    border-radius: 50%;
    animation: rotateLoader 0.4s infinite linear;
    margin: -2px 0 -2px 0.5em;
}
.tagify--loading .tagify__input:empty::after {
    margin-inline-start: 0;
}
.tagify + input,
.tagify + textarea {
    display: none !important;
}
.tagify__tag {
    display: inline-flex;
    align-items: center;
    margin: 5px 0 5px 5px;
    position: relative;
    z-index: 1;
    outline: 0;
    cursor: default;
    transition: 0.13s ease-out;
}
.tagify__tag > div {
    vertical-align: top;
    box-sizing: border-box;
    max-width: 100%;
    padding: 0.3em 0.5em;
    padding: var(--tag-pad, 0.3em 0.5em);
    color: #000;
    color: var(--tag-text-color, #000);
    line-height: inherit;
    border-radius: 3px;
    white-space: nowrap;
    transition: 0.13s ease-out;
}
.tagify__tag > div > * {
    white-space: pre-wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: top;
    min-width: 1ch;
    max-width: auto;
    min-width: var(--tag--min-width, 1ch);
    max-width: var(--tag--max-width, auto);
    transition: 0.8s ease, 0.1s color;
}
.tagify__tag > div > [contenteditable] {
    outline: 0;
    -webkit-user-select: text;
    user-select: text;
    cursor: text;
    margin: -2px;
    padding: 2px;
    max-width: 350px;
}
.tagify__tag > div::before {
    content: "";
    position: absolute;
    border-radius: inherit;
    inset-inline-start: 0;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    z-index: -1;
    pointer-events: none;
    transition: 120ms ease;
    animation: tags--bump 0.3s ease-out 1;
    box-shadow: 0 0 0 1.1em #e5e5e5 inset;
    box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) var(--tag-bg, #e5e5e5)
        inset;
}
.tagify__tag:hover:not([readonly]) div::before {
    top: -2px;
    inset-inline-end: -2px;
    bottom: -2px;
    inset-inline-start: -2px;
    box-shadow: 0 0 0 1.1em #d3e2e2 inset;
    box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em)
        var(--tag-hover, #d3e2e2) inset;
}
.tagify__tag--loading {
    pointer-events: none;
}
.tagify__tag--loading .tagify__tag__removeBtn {
    display: none;
}
.tagify__tag--loading::after {
    --loader-size: 0.4em;
    content: "";
    vertical-align: middle;
    opacity: 1;
    width: 0.7em;
    height: 0.7em;
    width: var(--loader-size);
    height: var(--loader-size);
    border: 3px solid;
    border-color: #eee #bbb #888 transparent;
    border-radius: 50%;
    animation: rotateLoader 0.4s infinite linear;
    margin: 0 0.5em 0 -0.1em;
}
.tagify__tag--flash div::before {
    animation: none;
}
.tagify__tag--hide {
    width: 0 !important;
    padding-inline-start: 0;
    padding-inline-end: 0;
    margin-inline-start: 0;
    margin-inline-end: 0;
    opacity: 0;
    transform: scale(0);
    transition: 0.3s;
    transition: var(--tag-hide-transition, 0.3s);
    pointer-events: none;
}
.tagify__tag--hide > div > * {
    white-space: nowrap;
}
.tagify__tag.tagify--noAnim > div::before {
    animation: none;
}
.tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div > span {
    opacity: 0.5;
}
.tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div::before {
    box-shadow: 0 0 0 1.1em rgba(211, 148, 148, 0.5) inset !important;
    box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em)
        var(--tag-invalid-bg, rgba(211, 148, 148, 0.5)) inset !important;
    transition: 0.2s;
}
.tagify__tag[readonly] .tagify__tag__removeBtn {
    display: none;
}
.tagify__tag[readonly] > div::before {
    background: linear-gradient(
            45deg,
            var(--tag-bg) 25%,
            transparent 25%,
            transparent 50%,
            var(--tag-bg) 50%,
            var(--tag-bg) 75%,
            transparent 75%,
            transparent
        )
        0/5px 5px;
    box-shadow: none;
    filter: brightness(0.95);
}
.tagify__tag--editable > div {
    color: #000;
    color: var(--tag-text-color--edit, #000);
}
.tagify__tag--editable > div::before {
    box-shadow: 0 0 0 2px #d3e2e2 inset !important;
    box-shadow: 0 0 0 2px var(--tag-hover, #d3e2e2) inset !important;
}
.tagify__tag--editable > .tagify__tag__removeBtn {
    pointer-events: none;
}
.tagify__tag--editable > .tagify__tag__removeBtn::after {
    opacity: 0;
    transform: translateX(100%) translateX(5px);
}
.tagify__tag--editable.tagify--invalid > div::before {
    box-shadow: 0 0 0 2px #d39494 inset !important;
    box-shadow: 0 0 0 2px var(--tag-invalid-color, #d39494) inset !important;
}
.tagify__tag__removeBtn {
    order: 5;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    cursor: pointer;
    font: 14px/1 Arial;
    background: 0 0;
    background: var(--tag-remove-btn-bg, none);
    color: #000;
    color: var(--tag-remove-btn-color, #000);
    width: 14px;
    height: 14px;
    margin-inline-end: 4.66667px;
    margin-inline-start: -4.66667px;
    overflow: hidden;
    transition: 0.2s ease-out;
}
.tagify__tag__removeBtn::after {
    content: "\00D7";
    transition: 0.3s, color 0s;
}
.tagify__tag__removeBtn:hover {
    color: #fff;
    background: #c77777;
    background: var(--tag-remove-btn-bg--hover, #c77777);
}
.tagify__tag__removeBtn:hover + div > span {
    opacity: 0.5;
}
.tagify__tag__removeBtn:hover + div::before {
    box-shadow: 0 0 0 1.1em rgba(211, 148, 148, 0.3) inset !important;
    box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em)
        var(--tag-remove-bg, rgba(211, 148, 148, 0.3)) inset !important;
    transition: box-shadow 0.2s;
}
.tagify:not(.tagify--mix) .tagify__input br {
    display: none;
}
.tagify:not(.tagify--mix) .tagify__input * {
    display: inline;
    white-space: nowrap;
}
.tagify__input {
    flex-grow: 1;
    display: inline-block;
    min-width: 110px;
    margin: 5px;
    padding: 0.3em 0.5em;
    padding: var(--tag-pad, 0.3em 0.5em);
    line-height: inherit;
    position: relative;
    white-space: pre-wrap;
    color: inherit;
    color: var(--input-color, inherit);
    box-sizing: inherit;
}
.tagify__input:empty::before {
    transition: 0.2s ease-out;
    opacity: 1;
    transform: none;
    display: inline-block;
    width: auto;
}
.tagify--mix .tagify__input:empty::before {
    display: inline-block;
}
.tagify__input:focus {
    outline: 0;
}
.tagify__input:focus::before {
    transition: 0.2s ease-out;
    opacity: 0;
    transform: translatex(6px);
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .tagify__input:focus::before {
        display: none;
    }
}
@supports (-ms-ime-align: auto) {
    .tagify__input:focus::before {
        display: none;
    }
}
.tagify__input:focus:empty::before {
    transition: 0.2s ease-out;
    opacity: 1;
    transform: none;
    color: rgba(0, 0, 0, 0.25);
    color: var(--placeholder-color-focus);
}
@-moz-document url-prefix() {
    .tagify__input:focus:empty::after {
        display: none;
    }
}
.tagify__input::before {
    content: attr(data-placeholder);
    height: 1em;
    line-height: 1em;
    margin: auto 0;
    z-index: 1;
    color: rgba(0, 0, 0, 0.4);
    color: var(--placeholder-color);
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    position: absolute;
}
.tagify--mix .tagify__input::before {
    display: none;
    position: static;
    line-height: inherit;
}
.tagify__input::after {
    content: attr(data-suggest);
    display: inline-block;
    white-space: pre;
    color: #000;
    opacity: 0.3;
    pointer-events: none;
    max-width: 100px;
}
.tagify__input .tagify__tag {
    margin: 0;
}
.tagify__input .tagify__tag > div {
    padding-top: 0;
    padding-bottom: 0;
}
.tagify--mix {
    display: block;
}
.tagify--mix .tagify__input {
    padding: 5px;
    margin: 0;
    width: 100%;
    height: 100%;
    line-height: 1.5;
}
.tagify--mix .tagify__input::before {
    height: auto;
}
.tagify--mix .tagify__input::after {
    content: none;
}
.tagify--select::after {
    content: ">";
    opacity: 0.5;
    position: absolute;
    top: 50%;
    inset-inline-end: 0;
    bottom: 0;
    font: 16px monospace;
    line-height: 8px;
    height: 8px;
    pointer-events: none;
    transform: translate(-150%, -50%) scaleX(1.2) rotate(90deg);
    transition: 0.2s ease-in-out;
}
.tagify--select[aria-expanded="true"]::after {
    transform: translate(-150%, -50%) rotate(270deg) scaleY(1.2);
}
.tagify--select .tagify__tag {
    position: absolute;
    top: 0;
    inset-inline-end: 1.8em;
    bottom: 0;
}
.tagify--select .tagify__tag div {
    display: none;
}
.tagify--select .tagify__input {
    width: 100%;
}
.tagify--invalid {
    --tags-border-color: #d39494;
}
.tagify__dropdown {
    position: absolute;
    z-index: 9999;
    transform: translateY(1px);
    overflow: hidden;
}
.tagify__dropdown[placement="top"] {
    margin-top: 0;
    transform: translateY(-100%);
}
.tagify__dropdown[placement="top"] .tagify__dropdown__wrapper {
    border-top-width: 1px;
    border-bottom-width: 0;
}
.tagify__dropdown[position="text"] {
    box-shadow: 0 0 0 3px rgba(var(--tagify-dd-color-primary), 0.1);
    font-size: 0.9em;
}
.tagify__dropdown[position="text"] .tagify__dropdown__wrapper {
    border-width: 1px;
}
.tagify__dropdown__wrapper {
    max-height: 300px;
    overflow: hidden;
    background: #fff;
    background: var(--tagify-dd-bg-color);
    border: 1px solid #3595f6;
    border-color: var(--tagify-dd-color-primary);
    border-top-width: 0;
    box-shadow: 0 2px 4px -2px rgba(0, 0, 0, 0.2);
    transition: 0.25s cubic-bezier(0, 1, 0.5, 1);
}
.tagify__dropdown__wrapper:hover {
    overflow: auto;
}
.tagify__dropdown--initial .tagify__dropdown__wrapper {
    max-height: 20px;
    transform: translateY(-1em);
}
.tagify__dropdown--initial[placement="top"] .tagify__dropdown__wrapper {
    transform: translateY(2em);
}
.tagify__dropdown__item {
    box-sizing: inherit;
    padding: 0.3em 0.5em;
    margin: 1px;
    cursor: pointer;
    border-radius: 2px;
    position: relative;
    outline: 0;
}
.tagify__dropdown__item--active {
    background: #3595f6;
    background: var(--tagify-dd-color-primary);
    color: #fff;
}
.tagify__dropdown__item:active {
    filter: brightness(105%);
}
.fc-icon,
.fc-unselectable {
    -moz-user-select: none;
    -ms-user-select: none;
}
.fc .fc-button,
.fc-icon {
    text-transform: none;
    text-align: center;
}
.fc-not-allowed,
.fc-not-allowed .fc-event {
    cursor: not-allowed;
}
.fc .fc-button:not(:disabled),
.fc a[data-navlink],
.fc-event.fc-event-draggable,
.fc-event[href] {
    cursor: pointer;
}
.fc-unselectable {
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}
.fc {
    display: flex;
    flex-direction: column;
    font-size: 1em;
}
.fc .fc-button,
.fc-icon {
    display: inline-block;
    font-weight: 400;
}
.fc,
.fc *,
.fc :after,
.fc :before {
    box-sizing: border-box;
}
.fc table {
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em;
}
.fc th {
    text-align: center;
}
.fc td,
.fc th {
    vertical-align: top;
    padding: 0;
}
.fc .fc-button,
.fc .fc-button .fc-icon,
.fc .fc-button-group,
.fc .fc-timegrid-slot-label {
    vertical-align: middle;
}
.fc a[data-navlink]:hover {
    text-decoration: underline;
}
.fc .fc-button:hover,
.fc .fc-list-event-title a,
a.fc-event,
a.fc-event:hover {
    text-decoration: none;
}
.fc-direction-ltr {
    direction: ltr;
    text-align: start;
}
.fc-direction-rtl {
    direction: rtl;
    text-align: end;
}
.fc-theme-standard td,
.fc-theme-standard th {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd);
}
.fc-liquid-hack td,
.fc-liquid-hack th {
    position: relative;
}
@font-face {
    font-family: fcicons;
    src: url("data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwT1MvMg8SBfAAAAC8AAAAYGNtYXAXVtKNAAABHAAAAFRnYXNwAAAAEAAAAXAAAAAIZ2x5ZgYydxIAAAF4AAAFNGhlYWQUJ7cIAAAGrAAAADZoaGVhB20DzAAABuQAAAAkaG10eCIABhQAAAcIAAAALGxvY2ED4AU6AAAHNAAAABhtYXhwAA8AjAAAB0wAAAAgbmFtZXsr690AAAdsAAABhnBvc3QAAwAAAAAI9AAAACAAAwPAAZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAAAAAAAABAAADpBgPA/8AAQAPAAEAAAAABAAAAAAAAAAAAAAAgAAAAAAADAAAAAwAAABwAAQADAAAAHAADAAEAAAAcAAQAOAAAAAoACAACAAIAAQAg6Qb//f//AAAAAAAg6QD//f//AAH/4xcEAAMAAQAAAAAAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAQAAAAAAAAAAAAIAADc5AQAAAAABAWIAjQKeAskAEwAAJSc3NjQnJiIHAQYUFwEWMjc2NCcCnuLiDQ0MJAz/AA0NAQAMJAwNDcni4gwjDQwM/wANIwz/AA0NDCMNAAAAAQFiAI0CngLJABMAACUBNjQnASYiBwYUHwEHBhQXFjI3AZ4BAA0N/wAMJAwNDeLiDQ0MJAyNAQAMIw0BAAwMDSMM4uINIwwNDQAAAAIA4gC3Ax4CngATACcAACUnNzY0JyYiDwEGFB8BFjI3NjQnISc3NjQnJiIPAQYUHwEWMjc2NCcB87e3DQ0MIw3VDQ3VDSMMDQ0BK7e3DQ0MJAzVDQ3VDCQMDQ3zuLcMJAwNDdUNIwzWDAwNIwy4twwkDA0N1Q0jDNYMDA0jDAAAAgDiALcDHgKeABMAJwAAJTc2NC8BJiIHBhQfAQcGFBcWMjchNzY0LwEmIgcGFB8BBwYUFxYyNwJJ1Q0N1Q0jDA0Nt7cNDQwjDf7V1Q0N1QwkDA0Nt7cNDQwkDLfWDCMN1Q0NDCQMt7gMIw0MDNYMIw3VDQ0MJAy3uAwjDQwMAAADAFUAAAOrA1UAMwBoAHcAABMiBgcOAQcOAQcOARURFBYXHgEXHgEXHgEzITI2Nz4BNz4BNz4BNRE0JicuAScuAScuASMFITIWFx4BFx4BFx4BFREUBgcOAQcOAQcOASMhIiYnLgEnLgEnLgE1ETQ2Nz4BNz4BNz4BMxMhMjY1NCYjISIGFRQWM9UNGAwLFQkJDgUFBQUFBQ4JCRULDBgNAlYNGAwLFQkJDgUFBQUFBQ4JCRULDBgN/aoCVgQIBAQHAwMFAQIBAQIBBQMDBwQECAT9qgQIBAQHAwMFAQIBAQIBBQMDBwQECASAAVYRGRkR/qoRGRkRA1UFBAUOCQkVDAsZDf2rDRkLDBUJCA4FBQUFBQUOCQgVDAsZDQJVDRkLDBUJCQ4FBAVVAgECBQMCBwQECAX9qwQJAwQHAwMFAQICAgIBBQMDBwQDCQQCVQUIBAQHAgMFAgEC/oAZEhEZGRESGQAAAAADAFUAAAOrA1UAMwBoAIkAABMiBgcOAQcOAQcOARURFBYXHgEXHgEXHgEzITI2Nz4BNz4BNz4BNRE0JicuAScuAScuASMFITIWFx4BFx4BFx4BFREUBgcOAQcOAQcOASMhIiYnLgEnLgEnLgE1ETQ2Nz4BNz4BNz4BMxMzFRQWMzI2PQEzMjY1NCYrATU0JiMiBh0BIyIGFRQWM9UNGAwLFQkJDgUFBQUFBQ4JCRULDBgNAlYNGAwLFQkJDgUFBQUFBQ4JCRULDBgN/aoCVgQIBAQHAwMFAQIBAQIBBQMDBwQECAT9qgQIBAQHAwMFAQIBAQIBBQMDBwQECASAgBkSEhmAERkZEYAZEhIZgBEZGREDVQUEBQ4JCRUMCxkN/asNGQsMFQkIDgUFBQUFBQ4JCBUMCxkNAlUNGQsMFQkJDgUEBVUCAQIFAwIHBAQIBf2rBAkDBAcDAwUBAgICAgEFAwMHBAMJBAJVBQgEBAcCAwUCAQL+gIASGRkSgBkSERmAEhkZEoAZERIZAAABAOIAjQMeAskAIAAAExcHBhQXFjI/ARcWMjc2NC8BNzY0JyYiDwEnJiIHBhQX4uLiDQ0MJAzi4gwkDA0N4uINDQwkDOLiDCQMDQ0CjeLiDSMMDQ3h4Q0NDCMN4uIMIw0MDOLiDAwNIwwAAAABAAAAAQAAa5n0y18PPPUACwQAAAAAANivOVsAAAAA2K85WwAAAAADqwNVAAAACAACAAAAAAAAAAEAAAPA/8AAAAQAAAAAAAOrAAEAAAAAAAAAAAAAAAAAAAALBAAAAAAAAAAAAAAAAgAAAAQAAWIEAAFiBAAA4gQAAOIEAABVBAAAVQQAAOIAAAAAAAoAFAAeAEQAagCqAOoBngJkApoAAQAAAAsAigADAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAA4ArgABAAAAAAABAAcAAAABAAAAAAACAAcAYAABAAAAAAADAAcANgABAAAAAAAEAAcAdQABAAAAAAAFAAsAFQABAAAAAAAGAAcASwABAAAAAAAKABoAigADAAEECQABAA4ABwADAAEECQACAA4AZwADAAEECQADAA4APQADAAEECQAEAA4AfAADAAEECQAFABYAIAADAAEECQAGAA4AUgADAAEECQAKADQApGZjaWNvbnMAZgBjAGkAYwBvAG4Ac1ZlcnNpb24gMS4wAFYAZQByAHMAaQBvAG4AIAAxAC4AMGZjaWNvbnMAZgBjAGkAYwBvAG4Ac2ZjaWNvbnMAZgBjAGkAYwBvAG4Ac1JlZ3VsYXIAUgBlAGcAdQBsAGEAcmZjaWNvbnMAZgBjAGkAYwBvAG4Ac0ZvbnQgZ2VuZXJhdGVkIGJ5IEljb01vb24uAEYAbwBuAHQAIABnAGUAbgBlAHIAYQB0AGUAZAAgAGIAeQAgAEkAYwBvAE0AbwBvAG4ALgAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
        format("truetype");
    font-weight: 400;
    font-style: normal;
}
.fc-icon {
    width: 1em;
    height: 1em;
    -webkit-user-select: none;
    user-select: none;
    font-family: fcicons !important;
    speak: none;
    font-style: normal;
    font-variant: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.fc-icon-chevron-left:before {
    content: "\e900";
}
.fc-icon-chevron-right:before {
    content: "\e901";
}
.fc-icon-chevrons-left:before {
    content: "\e902";
}
.fc-icon-chevrons-right:before {
    content: "\e903";
}
.fc-icon-minus-square:before {
    content: "\e904";
}
.fc-icon-plus-square:before {
    content: "\e905";
}
.fc-icon-x:before {
    content: "\e906";
}
.fc .fc-button {
    overflow: visible;
    text-transform: none;
    margin: 0;
    font-family: inherit;
}
.fc .fc-button::-moz-focus-inner {
    padding: 0;
    border-style: none;
}
.fc .fc-button {
    -webkit-appearance: button;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.4em 0.65em;
    font-size: 1em;
    line-height: 1.5;
    border-radius: 0.25em;
}
.fc .fc-button:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}
.fc .fc-button-primary:focus,
.fc .fc-button-primary:not(:disabled).fc-button-active:focus,
.fc .fc-button-primary:not(:disabled):active:focus {
    box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);
}
.fc .fc-button:disabled {
    opacity: 0.65;
}
.fc .fc-button-primary {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #2c3e50;
    background-color: var(--fc-button-bg-color, #2c3e50);
    border-color: #2c3e50;
    border-color: var(--fc-button-border-color, #2c3e50);
}
.fc .fc-button-primary:hover {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #1e2b37;
    background-color: var(--fc-button-hover-bg-color, #1e2b37);
    border-color: #1a252f;
    border-color: var(--fc-button-hover-border-color, #1a252f);
}
.fc .fc-button-primary:disabled {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #2c3e50;
    background-color: var(--fc-button-bg-color, #2c3e50);
    border-color: #2c3e50;
    border-color: var(--fc-button-border-color, #2c3e50);
}
.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #1a252f;
    background-color: var(--fc-button-active-bg-color, #1a252f);
    border-color: #151e27;
    border-color: var(--fc-button-active-border-color, #151e27);
}
.fc .fc-button .fc-icon {
    font-size: 1.5em;
}
.fc .fc-button-group {
    position: relative;
    display: inline-flex;
}
.fc .fc-button-group > .fc-button {
    position: relative;
    flex: 1 1 auto;
}
.fc .fc-button-group > .fc-button.fc-button-active,
.fc .fc-button-group > .fc-button:active,
.fc .fc-button-group > .fc-button:focus,
.fc .fc-button-group > .fc-button:hover {
    z-index: 1;
}
.fc-direction-ltr .fc-button-group > .fc-button:not(:first-child) {
    margin-inline-start: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.fc-direction-ltr .fc-button-group > .fc-button:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.fc-direction-rtl .fc-button-group > .fc-button:not(:first-child) {
    margin-inline-end: -1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.fc-direction-rtl .fc-button-group > .fc-button:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.fc .fc-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 1.5em;
}
.fc .fc-toolbar.fc-footer-toolbar {
    margin-top: 1.5em;
}
.fc .fc-toolbar-title {
    font-size: 1.75em;
    margin: 0;
}
.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
    margin-inline-start: 0.75em;
}
.fc-direction-rtl .fc-toolbar > * > :not(:first-child) {
    margin-inline-end: 0.75em;
}
.fc-direction-rtl .fc-toolbar-ltr {
    flex-direction: row-reverse;
}
.fc .fc-scroller {
    -webkit-overflow-scrolling: touch;
    position: relative;
}
.fc .fc-scroller-liquid {
    height: 100%;
}
.fc .fc-scroller-liquid-absolute {
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    inset-inline-start: 0;
    bottom: 0;
}
.fc .fc-scroller-harness {
    position: relative;
    overflow: hidden;
    direction: ltr;
}
.fc .fc-scroller-harness-liquid {
    height: 100%;
}
.fc-direction-rtl .fc-scroller-harness > .fc-scroller {
    direction: rtl;
}
.fc-theme-standard .fc-scrollgrid {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd);
}
.fc .fc-scrollgrid,
.fc .fc-scrollgrid-section-footer > *,
.fc .fc-scrollgrid-section-header > * {
    border-bottom-width: 0;
}
.fc .fc-scrollgrid,
.fc .fc-scrollgrid table {
    width: 100%;
    table-layout: fixed;
}
.fc .fc-scrollgrid table {
    border-top-style: hidden;
    border-inline-start-style: hidden;
    border-inline-end-style: hidden;
}
.fc .fc-scrollgrid {
    border-collapse: separate;
    border-inline-end-width: 0;
}
.fc .fc-scrollgrid-liquid {
    height: 100%;
}
.fc .fc-scrollgrid-section,
.fc .fc-scrollgrid-section table,
.fc .fc-scrollgrid-section > td {
    height: 1px;
}
.fc .fc-scrollgrid-section-liquid {
    height: auto;
}
.fc .fc-scrollgrid-section-liquid > td {
    height: 100%;
}
.fc .fc-scrollgrid-section > * {
    border-top-width: 0;
    border-inline-start-width: 0;
}
.fc .fc-scrollgrid-section-body table,
.fc .fc-scrollgrid-section-footer table {
    border-bottom-style: hidden;
}
.fc .fc-scrollgrid-section-sticky > * {
    background: var(--fc-page-bg-color, #fff);
    position: -webkit-sticky;
    position: sticky;
    z-index: 2;
}
.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {
    top: 0;
}
.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky > * {
    bottom: 0;
}
.fc .fc-scrollgrid-sticky-shim {
    height: 1px;
    margin-bottom: -1px;
}
.fc-sticky {
    position: -webkit-sticky;
    position: sticky;
}
.fc .fc-view-harness {
    flex-grow: 1;
    position: relative;
}
.fc .fc-bg-event,
.fc .fc-highlight,
.fc .fc-non-business,
.fc .fc-view-harness-active > .fc-view {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    bottom: 0;
}
.fc .fc-col-header-cell-cushion {
    display: inline-block;
    padding: 2px 4px;
}
.fc .fc-non-business {
    background: rgba(215, 215, 215, 0.3);
    background: var(--fc-non-business-color, rgba(215, 215, 215, 0.3));
}
.fc .fc-bg-event {
    background: var(--fc-bg-event-color, #8fdf82);
    opacity: 0.3;
    opacity: var(--fc-bg-event-opacity, 0.3);
}
.fc .fc-bg-event .fc-event-title {
    margin: 0.5em;
    font-size: 0.85em;
    font-size: var(--fc-small-font-size, 0.85em);
    font-style: italic;
}
.fc .fc-highlight {
    background: rgba(188, 232, 241, 0.3);
    background: var(--fc-highlight-color, rgba(188, 232, 241, 0.3));
}
.fc .fc-cell-shaded,
.fc .fc-day-disabled {
    background: rgba(208, 208, 208, 0.3);
    background: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));
}
.fc-event .fc-event-main {
    position: relative;
    z-index: 2;
}
.fc-event-dragging:not(.fc-event-selected) {
    opacity: 0.75;
}
.fc-event-dragging.fc-event-selected {
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
}
.fc-event .fc-event-resizer {
    display: none;
    position: absolute;
    z-index: 4;
}
.fc-event-selected .fc-event-resizer,
.fc-event:hover .fc-event-resizer,
.fc-h-event {
    display: block;
}
.fc-event-selected .fc-event-resizer {
    border-radius: 4px;
    border-radius: calc(var(--fc-event-resizer-dot-total-width, 8px) / 2);
    border-width: 1px;
    border-width: var(--fc-event-resizer-dot-border-width, 1px);
    width: 8px;
    width: var(--fc-event-resizer-dot-total-width, 8px);
    height: 8px;
    height: var(--fc-event-resizer-dot-total-width, 8px);
    border-style: solid;
    border-color: inherit;
    background: var(--fc-page-bg-color, #fff);
}
.fc-event-selected .fc-event-resizer:before {
    content: "";
    position: absolute;
    top: -20px;
    inset-inline-start: -20px;
    inset-inline-end: -20px;
    bottom: -20px;
}
.fc-event-selected {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.fc-event-selected:before {
    content: "";
    position: absolute;
    z-index: 3;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    bottom: 0;
}
.fc-event-selected:after {
    content: "";
    background: rgba(0, 0, 0, 0.25);
    background: var(--fc-event-selected-overlay-color, rgba(0, 0, 0, 0.25));
    position: absolute;
    z-index: 1;
    top: -1px;
    inset-inline-start: -1px;
    inset-inline-end: -1px;
    bottom: -1px;
}
.fc-h-event {
    border: 1px solid #3788d8;
    border: 1px solid var(--fc-event-border-color, #3788d8);
    background-color: #3788d8;
    background-color: var(--fc-event-bg-color, #3788d8);
}
.fc-h-event .fc-event-main {
    color: #fff;
    color: var(--fc-event-text-color, #fff);
}
.fc-h-event .fc-event-main-frame {
    display: flex;
}
.fc-h-event .fc-event-time {
    max-width: 100%;
    overflow: hidden;
}
.fc-h-event .fc-event-title-container {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
}
.fc-h-event .fc-event-title {
    display: inline-block;
    vertical-align: top;
    inset-inline-start: 0;
    inset-inline-end: 0;
    max-width: 100%;
    overflow: hidden;
}
.fc-h-event.fc-event-selected:before {
    top: -10px;
    bottom: -10px;
}
.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-inline-start-width: 0;
}
.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-inline-end-width: 0;
}
.fc-h-event:not(.fc-event-selected) .fc-event-resizer {
    top: 0;
    bottom: 0;
    width: 8px;
    width: var(--fc-event-resizer-thickness, 8px);
}
.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
    cursor: w-resize;
    inset-inline-start: -4px;
    inset-inline-start: calc(var(--fc-event-resizer-thickness, 8px) / -2);
}
.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
    cursor: e-resize;
    inset-inline-end: -4px;
    inset-inline-end: calc(var(--fc-event-resizer-thickness, 8px) / -2);
}
.fc-h-event.fc-event-selected .fc-event-resizer {
    top: 50%;
    margin-top: -4px;
    margin-top: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);
}
.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end {
    inset-inline-start: -4px;
    inset-inline-start: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);
}
.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start {
    inset-inline-end: -4px;
    inset-inline-end: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);
}
:root {
    --fc-daygrid-event-dot-width: 8px;
    --fc-list-event-dot-width: 10px;
    --fc-list-event-hover-bg-color: #f5f5f5;
}
.fc .fc-popover {
    position: fixed;
    top: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}
.fc .fc-popover-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 3px 4px;
}
.fc .fc-popover-title {
    margin: 0 2px;
}
.fc .fc-popover-close {
    cursor: pointer;
    opacity: 0.65;
    font-size: 1.1em;
}
.fc-theme-standard .fc-popover {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd);
    background: var(--fc-page-bg-color, #fff);
}
.fc-theme-standard .fc-popover-header {
    background: rgba(208, 208, 208, 0.3);
    background: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));
}
.fc-daygrid-day-events:after,
.fc-daygrid-day-events:before,
.fc-daygrid-day-frame:after,
.fc-daygrid-day-frame:before,
.fc-daygrid-event-harness:after,
.fc-daygrid-event-harness:before {
    content: "";
    clear: both;
    display: table;
}
.fc .fc-daygrid-body {
    position: relative;
    z-index: 1;
}
.fc .fc-daygrid-day.fc-day-today {
    background-color: rgba(255, 220, 40, 0.15);
    background-color: var(--fc-today-bg-color, rgba(255, 220, 40, 0.15));
}
.fc .fc-daygrid-day-frame {
    position: relative;
    min-height: 100%;
}
.fc .fc-daygrid-day-top {
    display: flex;
    flex-direction: row-reverse;
}
.fc .fc-day-other .fc-daygrid-day-top {
    opacity: 0.3;
}
.fc .fc-daygrid-day-number {
    position: relative;
    z-index: 4;
    padding: 4px;
}
.fc .fc-daygrid-day-events {
    margin-top: 1px;
}
.fc .fc-daygrid-body-balanced .fc-daygrid-day-events {
    position: absolute;
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
    position: relative;
    min-height: 2em;
}
.fc .fc-daygrid-body-natural .fc-daygrid-day-events {
    margin-bottom: 1em;
}
.fc .fc-daygrid-event-harness {
    position: relative;
}
.fc .fc-daygrid-event-harness-abs {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc .fc-daygrid-bg-harness {
    position: absolute;
    top: 0;
    bottom: 0;
}
.fc .fc-daygrid-day-bg .fc-non-business {
    z-index: 1;
}
.fc .fc-daygrid-day-bg .fc-bg-event {
    z-index: 2;
}
.fc .fc-daygrid-day-bg .fc-highlight {
    z-index: 3;
}
.fc .fc-daygrid-event {
    z-index: 6;
    margin-top: 1px;
}
.fc .fc-daygrid-event.fc-event-mirror {
    z-index: 7;
}
.fc .fc-daygrid-day-bottom {
    font-size: 0.85em;
    margin: 2px 3px 0;
}
.fc .fc-daygrid-more-link {
    position: relative;
    z-index: 4;
    cursor: pointer;
}
.fc .fc-daygrid-week-number {
    position: absolute;
    z-index: 5;
    top: 0;
    padding: 2px;
    min-width: 1.5em;
    text-align: center;
    background-color: rgba(208, 208, 208, 0.3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));
    color: grey;
    color: var(--fc-neutral-text-color, grey);
}
.fc .fc-more-popover {
    z-index: 8;
}
.fc .fc-more-popover .fc-popover-body {
    min-width: 220px;
    padding: 10px;
}
.fc-direction-ltr .fc-daygrid-event.fc-event-start,
.fc-direction-rtl .fc-daygrid-event.fc-event-end {
    margin-inline-start: 2px;
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end,
.fc-direction-rtl .fc-daygrid-event.fc-event-start {
    margin-inline-end: 2px;
}
.fc-direction-ltr .fc-daygrid-week-number {
    inset-inline-start: 0;
    border-radius: 0 0 3px;
}
.fc-direction-rtl .fc-daygrid-week-number {
    inset-inline-end: 0;
    border-radius: 0 0 0 3px;
}
.fc-liquid-hack .fc-daygrid-day-frame {
    position: static;
}
.fc-daygrid-event {
    position: relative;
    white-space: nowrap;
    border-radius: 3px;
    font-size: 0.85em;
    font-size: var(--fc-small-font-size, 0.85em);
}
.fc-daygrid-block-event .fc-event-time {
    font-weight: 700;
}
.fc-daygrid-block-event .fc-event-time,
.fc-daygrid-block-event .fc-event-title {
    padding: 1px;
}
.fc-daygrid-dot-event {
    display: flex;
    align-items: center;
    padding: 2px 0;
}
.fc-daygrid-dot-event .fc-event-title {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
    font-weight: 700;
}
.fc-daygrid-dot-event.fc-event-mirror,
.fc-daygrid-dot-event:hover {
    background: rgba(0, 0, 0, 0.1);
}
.fc-daygrid-dot-event.fc-event-selected:before {
    top: -10px;
    bottom: -10px;
}
.fc-daygrid-event-dot {
    margin: 0 4px;
    box-sizing: content-box;
    width: 0;
    height: 0;
    border: 4px solid #3788d8;
    border: calc(var(--fc-daygrid-event-dot-width, 8px) / 2) solid
        var(--fc-event-border-color, #3788d8);
    border-radius: 4px;
    border-radius: calc(var(--fc-daygrid-event-dot-width, 8px) / 2);
}
.fc-direction-ltr .fc-daygrid-event .fc-event-time {
    margin-inline-end: 3px;
}
.fc-direction-rtl .fc-daygrid-event .fc-event-time {
    margin-inline-start: 3px;
}
.fc-v-event {
    display: block;
    border: 1px solid #3788d8;
    border: 1px solid var(--fc-event-border-color, #3788d8);
    background-color: #3788d8;
    background-color: var(--fc-event-bg-color, #3788d8);
}
.fc-v-event .fc-event-main {
    color: #fff;
    color: var(--fc-event-text-color, #fff);
    height: 100%;
}
.fc-v-event .fc-event-main-frame {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.fc-v-event .fc-event-time {
    flex-grow: 0;
    flex-shrink: 0;
    max-height: 100%;
    overflow: hidden;
}
.fc-v-event .fc-event-title-container {
    flex-grow: 1;
    flex-shrink: 1;
    min-height: 0;
}
.fc-v-event .fc-event-title {
    top: 0;
    bottom: 0;
    max-height: 100%;
    overflow: hidden;
}
.fc-v-event:not(.fc-event-start) {
    border-top-width: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.fc-v-event:not(.fc-event-end) {
    border-bottom-width: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.fc-v-event.fc-event-selected:before {
    inset-inline-start: -10px;
    inset-inline-end: -10px;
}
.fc-v-event .fc-event-resizer-start {
    cursor: n-resize;
}
.fc-v-event .fc-event-resizer-end {
    cursor: s-resize;
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer {
    height: 8px;
    height: var(--fc-event-resizer-thickness, 8px);
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start {
    top: -4px;
    top: calc(var(--fc-event-resizer-thickness, 8px) / -2);
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end {
    bottom: -4px;
    bottom: calc(var(--fc-event-resizer-thickness, 8px) / -2);
}
.fc-v-event.fc-event-selected .fc-event-resizer {
    inset-inline-start: 50%;
    margin-inline-start: -4px;
    margin-inline-start: calc(
        var(--fc-event-resizer-dot-total-width, 8px) / -2
    );
}
.fc-v-event.fc-event-selected .fc-event-resizer-start {
    top: -4px;
    top: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);
}
.fc-v-event.fc-event-selected .fc-event-resizer-end {
    bottom: -4px;
    bottom: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);
}
.fc .fc-timegrid .fc-daygrid-body {
    z-index: 2;
}
.fc .fc-timegrid-axis-chunk > table,
.fc .fc-timegrid-body,
.fc .fc-timegrid-slots {
    position: relative;
    z-index: 1;
}
.fc .fc-timegrid-divider {
    padding: 0 0 2px;
}
.fc .fc-timegrid-body {
    min-height: 100%;
}
.fc .fc-timegrid-axis-chunk {
    position: relative;
}
.fc .fc-timegrid-slot {
    height: 1.5em;
    border-bottom: 0;
}
.fc .fc-timegrid-slot:empty:before {
    content: "\00a0";
}
.fc .fc-timegrid-slot-minor {
    border-top-style: dotted;
}
.fc .fc-timegrid-slot-label-cushion {
    display: inline-block;
    white-space: nowrap;
}
.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
    padding: 0 4px;
}
.fc .fc-timegrid-axis-frame-liquid {
    height: 100%;
}
.fc .fc-timegrid-axis-frame {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.fc .fc-timegrid-axis-cushion {
    max-width: 60px;
    flex-shrink: 0;
}
.fc-direction-ltr .fc-timegrid-slot-label-frame {
    text-align: end;
}
.fc-direction-rtl .fc-timegrid-slot-label-frame {
    text-align: start;
}
.fc-liquid-hack .fc-timegrid-axis-frame-liquid {
    height: auto;
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
}
.fc .fc-timegrid-col.fc-day-today {
    background-color: rgba(255, 220, 40, 0.15);
    background-color: var(--fc-today-bg-color, rgba(255, 220, 40, 0.15));
}
.fc .fc-timegrid-col-frame {
    min-height: 100%;
    position: relative;
}
.fc-liquid-hack .fc-timegrid-col-frame {
    height: auto;
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
}
.fc-media-screen .fc-timegrid-cols {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    bottom: 0;
}
.fc-media-screen .fc-timegrid-cols > table {
    height: 100%;
}
.fc-media-screen .fc-timegrid-col-bg,
.fc-media-screen .fc-timegrid-col-events,
.fc-media-screen .fc-timegrid-now-indicator-container {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc-media-screen .fc-timegrid-event-harness {
    position: absolute;
}
.fc .fc-timegrid-col-bg {
    z-index: 2;
}
.fc .fc-timegrid-col-bg .fc-non-business {
    z-index: 1;
}
.fc .fc-timegrid-col-bg .fc-bg-event {
    z-index: 2;
}
.fc .fc-timegrid-col-bg .fc-highlight,
.fc .fc-timegrid-col-events {
    z-index: 3;
}
.fc .fc-timegrid-bg-harness {
    position: absolute;
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc .fc-timegrid-now-indicator-container {
    bottom: 0;
    overflow: hidden;
}
.fc-direction-ltr .fc-timegrid-col-events {
    margin: 0 2.5% 0 2px;
}
.fc-direction-rtl .fc-timegrid-col-events {
    margin: 0 2px 0 2.5%;
}
.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror {
    box-shadow: 0 0 0 1px #fff;
    box-shadow: 0 0 0 1px var(--fc-page-bg-color, #fff);
}
.fc-timegrid-event {
    font-size: 0.85em;
    font-size: var(--fc-small-font-size, 0.85em);
    border-radius: 3px;
}
.fc-timegrid-event .fc-event-main {
    padding: 1px 1px 0;
}
.fc-timegrid-event .fc-event-time {
    white-space: nowrap;
    font-size: 0.85em;
    font-size: var(--fc-small-font-size, 0.85em);
    margin-bottom: 1px;
}
.fc-timegrid-event-condensed .fc-event-main-frame {
    flex-direction: row;
    overflow: hidden;
}
.fc-timegrid-event-condensed .fc-event-time:after {
    content: "\00a0-\00a0";
}
.fc-timegrid-event-condensed .fc-event-title {
    font-size: 0.85em;
    font-size: var(--fc-small-font-size, 0.85em);
}
.fc-media-screen .fc-timegrid-event {
    position: absolute;
    top: 0;
    bottom: 1px;
    inset-inline-start: 0;
    inset-inline-end: 0;
}
.fc .fc-timegrid-now-indicator-line {
    position: absolute;
    z-index: 4;
    inset-inline-start: 0;
    inset-inline-end: 0;
    border-style: solid;
    border-color: red;
    border-color: var(--fc-now-indicator-color, red);
    border-width: 1px 0 0;
}
.fc .fc-timegrid-now-indicator-arrow {
    position: absolute;
    z-index: 4;
    margin-top: -5px;
    border-style: solid;
    border-color: red;
    border-color: var(--fc-now-indicator-color, red);
}
.fc-direction-ltr .fc-timegrid-now-indicator-arrow {
    inset-inline-start: 0;
    border-width: 5px 0 5px 6px;
    border-top-color: transparent;
    border-bottom-color: transparent;
}
.fc-direction-rtl .fc-timegrid-now-indicator-arrow {
    inset-inline-end: 0;
    border-width: 5px 6px 5px 0;
    border-top-color: transparent;
    border-bottom-color: transparent;
}
.fc-theme-standard .fc-list {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd);
}
.fc .fc-list-empty {
    background-color: rgba(208, 208, 208, 0.3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.fc .fc-list-empty-cushion {
    margin: 5em 0;
}
.fc .fc-list-table {
    width: 100%;
    border-style: hidden;
}
.fc .fc-list-table tr > * {
    border-inline-start: 0;
    border-inline-end: 0;
}
.fc .fc-list-sticky .fc-list-day > * {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    background: var(--fc-page-bg-color, #fff);
}
.fc .fc-list-table th {
    padding: 0;
}
.fc .fc-list-day-cushion,
.fc .fc-list-table td {
    padding: 8px 14px;
}
.fc .fc-list-day-cushion:after {
    content: "";
    clear: both;
    display: table;
}
.fc-theme-standard .fc-list-day-cushion {
    background-color: rgba(208, 208, 208, 0.3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));
}
.fc-direction-ltr .fc-list-day-text,
.fc-direction-rtl .fc-list-day-side-text {
    float: left;
}
.fc-direction-ltr .fc-list-day-side-text,
.fc-direction-rtl .fc-list-day-text {
    float: right;
}
.fc-direction-ltr .fc-list-table .fc-list-event-graphic {
    padding-inline-end: 0;
}
.fc-direction-rtl .fc-list-table .fc-list-event-graphic {
    padding-inline-start: 0;
}
.fc .fc-list-event.fc-event-forced-url {
    cursor: pointer;
}
.fc .fc-list-event:hover td {
    background-color: #f5f5f5;
    background-color: var(--fc-list-event-hover-bg-color, #f5f5f5);
}
.fc .fc-list-event-graphic,
.fc .fc-list-event-time {
    white-space: nowrap;
    width: 1px;
}
.fc .fc-list-event-dot {
    display: inline-block;
    box-sizing: content-box;
    width: 0;
    height: 0;
    border: 5px solid #3788d8;
    border: calc(var(--fc-list-event-dot-width, 10px) / 2) solid
        var(--fc-event-border-color, #3788d8);
    border-radius: 5px;
    border-radius: calc(var(--fc-list-event-dot-width, 10px) / 2);
}
.fc .fc-list-event-title a {
    color: inherit;
}
.fc .fc-list-event.fc-event-forced-url:hover a {
    text-decoration: underline;
}
.fc-theme-bootstrap a:not([href]) {
    color: inherit;
}
.flatpickr-calendar {
    background: 0 0;
    opacity: 0;
    display: none;
    text-align: center;
    visibility: hidden;
    padding: 0;
    -webkit-animation: none;
    animation: none;
    direction: ltr;
    border: 0;
    font-size: 14px;
    line-height: 24px;
    border-radius: 5px;
    position: absolute;
    width: 307.875px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    background: #fff;
    -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6,
        0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
    box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6,
        0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
}
.flatpickr-calendar.inline,
.flatpickr-calendar.open {
    opacity: 1;
    max-height: 640px;
    visibility: visible;
}
.flatpickr-calendar.open {
    display: inline-block;
    z-index: 99999;
}
.flatpickr-calendar.animate.open {
    -webkit-animation: fpFadeInDown 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    animation: fpFadeInDown 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.inline {
    display: block;
    position: relative;
    top: 2px;
}
.flatpickr-calendar.static {
    position: absolute;
    top: calc(100% + 2px);
}
.flatpickr-calendar.static.open {
    z-index: 999;
    display: block;
}
.flatpickr-calendar.multiMonth
    .flatpickr-days
    .dayContainer:nth-child(n + 1)
    .flatpickr-day.inRange:nth-child(7n + 7) {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}
.flatpickr-calendar.multiMonth
    .flatpickr-days
    .dayContainer:nth-child(n + 2)
    .flatpickr-day.inRange:nth-child(7n + 1) {
    -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-calendar .hasTime .dayContainer,
.flatpickr-calendar .hasWeeks .dayContainer {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.flatpickr-calendar .hasWeeks .dayContainer {
    border-inline-start: 0;
}
.flatpickr-calendar.hasTime .flatpickr-time {
    height: 40px;
    border-top: 1px solid #e6e6e6;
}
.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
    height: auto;
}
.flatpickr-calendar:after,
.flatpickr-calendar:before {
    position: absolute;
    display: block;
    pointer-events: none;
    border: solid transparent;
    content: "";
    height: 0;
    width: 0;
    inset-inline-start: 22px;
}
.flatpickr-calendar.arrowRight:after,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.rightMost:before {
    inset-inline-start: auto;
    inset-inline-end: 22px;
}
.flatpickr-calendar.arrowCenter:after,
.flatpickr-calendar.arrowCenter:before {
    inset-inline-start: 50%;
    inset-inline-end: 50%;
}
.flatpickr-calendar:before {
    border-width: 5px;
    margin: 0 -5px;
}
.flatpickr-calendar:after {
    border-width: 4px;
    margin: 0 -4px;
}
.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
    bottom: 100%;
}
.flatpickr-calendar.arrowTop:before {
    border-bottom-color: #e6e6e6;
}
.flatpickr-calendar.arrowTop:after {
    border-bottom-color: #fff;
}
.flatpickr-calendar.arrowBottom:after,
.flatpickr-calendar.arrowBottom:before {
    top: 100%;
}
.flatpickr-calendar.arrowBottom:before {
    border-top-color: #e6e6e6;
}
.flatpickr-calendar.arrowBottom:after {
    border-top-color: #fff;
}
.flatpickr-calendar:focus {
    outline: 0;
}
.flatpickr-wrapper {
    position: relative;
    display: inline-block;
}
.flatpickr-months {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.flatpickr-months .flatpickr-month {
    background: 0 0;
    color: rgba(0, 0, 0, 0.9);
    fill: rgba(0, 0, 0, 0.9);
    height: 34px;
    line-height: 1;
    text-align: center;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    text-decoration: none;
    cursor: pointer;
    position: absolute;
    top: 0;
    height: 34px;
    padding: 10px;
    z-index: 3;
    color: rgba(0, 0, 0, 0.9);
    fill: rgba(0, 0, 0, 0.9);
}
.flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
    display: none;
}
.flatpickr-months .flatpickr-next-month i,
.flatpickr-months .flatpickr-prev-month i {
    position: relative;
}
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
    inset-inline-start: 0;
}
.flatpickr-months .flatpickr-next-month.flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month {
    inset-inline-end: 0;
}
.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:hover {
    color: #959ea9;
}
.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
    fill: #f64747;
}
.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month svg {
    width: 14px;
    height: 14px;
}
.flatpickr-months .flatpickr-next-month svg path,
.flatpickr-months .flatpickr-prev-month svg path {
    -webkit-transition: fill 0.1s;
    transition: fill 0.1s;
    fill: inherit;
}
.numInputWrapper {
    position: relative;
    height: auto;
}
.numInputWrapper input,
.numInputWrapper span {
    display: inline-block;
}
.numInputWrapper input {
    width: 100%;
}
.numInputWrapper input::-ms-clear {
    display: none;
}
.numInputWrapper input::-webkit-inner-spin-button,
.numInputWrapper input::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
}
.numInputWrapper span {
    position: absolute;
    inset-inline-end: 0;
    width: 14px;
    padding: 0 4px 0 2px;
    height: 50%;
    line-height: 50%;
    opacity: 0;
    cursor: pointer;
    border: 1px solid rgba(57, 57, 57, 0.15);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.numInputWrapper span:hover {
    background: rgba(0, 0, 0, 0.1);
}
.numInputWrapper span:active {
    background: rgba(0, 0, 0, 0.2);
}
.numInputWrapper span:after {
    display: block;
    content: "";
    position: absolute;
}
.numInputWrapper span.arrowUp {
    top: 0;
    border-bottom: 0;
}
.numInputWrapper span.arrowUp:after {
    border-inline-start: 4px solid transparent;
    border-inline-end: 4px solid transparent;
    border-bottom: 4px solid rgba(57, 57, 57, 0.6);
    top: 26%;
}
.numInputWrapper span.arrowDown {
    top: 50%;
}
.numInputWrapper span.arrowDown:after {
    border-inline-start: 4px solid transparent;
    border-inline-end: 4px solid transparent;
    border-top: 4px solid rgba(57, 57, 57, 0.6);
    top: 40%;
}
.numInputWrapper span svg {
    width: inherit;
    height: auto;
}
.numInputWrapper span svg path {
    fill: rgba(0, 0, 0, 0.5);
}
.numInputWrapper:hover {
    background: rgba(0, 0, 0, 0.05);
}
.numInputWrapper:hover span {
    opacity: 1;
}
.flatpickr-current-month {
    font-size: 135%;
    line-height: inherit;
    font-weight: 300;
    color: inherit;
    position: absolute;
    width: 75%;
    inset-inline-start: 12.5%;
    padding: 7.48px 0 0 0;
    line-height: 1;
    height: 34px;
    display: inline-block;
    text-align: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
.flatpickr-current-month span.cur-month {
    font-family: inherit;
    font-weight: 700;
    color: inherit;
    display: inline-block;
    margin-inline-start: 0.5ch;
    padding: 0;
}
.flatpickr-current-month span.cur-month:hover {
    background: rgba(0, 0, 0, 0.05);
}
.flatpickr-current-month .numInputWrapper {
    width: 6ch;
    display: inline-block;
}
.flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month input.cur-year {
    background: 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    cursor: text;
    padding: 0 0 0 0.5ch;
    margin: 0;
    display: inline-block;
    font-size: inherit;
    font-family: inherit;
    font-weight: 300;
    line-height: inherit;
    height: auto;
    border: 0;
    border-radius: 0;
    vertical-align: initial;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
}
.flatpickr-current-month input.cur-year:focus {
    outline: 0;
}
.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
    font-size: 100%;
    color: rgba(0, 0, 0, 0.5);
    background: 0 0;
    pointer-events: none;
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
    appearance: menulist;
    background: 0 0;
    border: none;
    border-radius: 0;
    box-sizing: border-box;
    color: inherit;
    cursor: pointer;
    font-size: inherit;
    font-family: inherit;
    font-weight: 300;
    height: auto;
    line-height: inherit;
    margin: -1px 0 0 0;
    outline: 0;
    padding: 0 0 0 0.5ch;
    position: relative;
    vertical-align: initial;
    -webkit-box-sizing: border-box;
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    width: auto;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:active,
.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
    outline: 0;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background: rgba(0, 0, 0, 0.05);
}
.flatpickr-current-month
    .flatpickr-monthDropdown-months
    .flatpickr-monthDropdown-month {
    background-color: transparent;
    outline: 0;
    padding: 0;
}
.flatpickr-weekdays {
    background: 0 0;
    text-align: center;
    overflow: hidden;
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    height: 28px;
}
.flatpickr-weekdays .flatpickr-weekdaycontainer {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
span.flatpickr-weekday {
    cursor: default;
    font-size: 90%;
    background: 0 0;
    color: rgba(0, 0, 0, 0.54);
    line-height: 1;
    margin: 0;
    text-align: center;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-weight: bolder;
}
.dayContainer,
.flatpickr-weeks {
    padding: 1px 0 0 0;
}
.flatpickr-days {
    position: relative;
    overflow: hidden;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    width: 307.875px;
}
.flatpickr-days:focus {
    outline: 0;
}
.dayContainer {
    padding: 0;
    outline: 0;
    text-align: start;
    width: 307.875px;
    min-width: 307.875px;
    max-width: 307.875px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    display: -ms-flexbox;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-around;
    justify-content: space-around;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
.dayContainer + .dayContainer {
    -webkit-box-shadow: -1px 0 0 #e6e6e6;
    box-shadow: -1px 0 0 #e6e6e6;
}
.flatpickr-day {
    background: 0 0;
    border: 1px solid transparent;
    border-radius: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #393939;
    cursor: pointer;
    font-weight: 400;
    width: 14.2857143%;
    -webkit-flex-basis: 14.2857143%;
    -ms-flex-preferred-size: 14.2857143%;
    flex-basis: 14.2857143%;
    max-width: 39px;
    height: 39px;
    line-height: 39px;
    margin: 0;
    display: inline-block;
    position: relative;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
}
.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
    cursor: pointer;
    outline: 0;
    background: #e6e6e6;
    border-color: #e6e6e6;
}
.flatpickr-day.today {
    border-color: #959ea9;
}
.flatpickr-day.today:focus,
.flatpickr-day.today:hover {
    border-color: #959ea9;
    background: #959ea9;
    color: #fff;
}
.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: #569ff7;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #fff;
    border-color: #569ff7;
}
.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
    border-radius: 50px 0 0 50px;
}
.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
    border-radius: 0 50px 50px 0;
}
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)) {
    -webkit-box-shadow: -10px 0 0 #569ff7;
    box-shadow: -10px 0 0 #569ff7;
}
.flatpickr-day.endRange.startRange.endRange,
.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange {
    border-radius: 50px;
}
.flatpickr-day.inRange {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
    color: rgba(57, 57, 57, 0.3);
    background: 0 0;
    border-color: transparent;
    cursor: default;
}
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    cursor: not-allowed;
    color: rgba(57, 57, 57, 0.1);
}
.flatpickr-day.week.selected {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
    box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
}
.flatpickr-day.hidden {
    visibility: hidden;
}
.rangeMode .flatpickr-day {
    margin-top: 1px;
}
.flatpickr-weekwrapper {
    float: left;
}
.flatpickr-weekwrapper .flatpickr-weeks {
    padding: 0 12px;
    -webkit-box-shadow: 1px 0 0 #e6e6e6;
    box-shadow: 1px 0 0 #e6e6e6;
}
.flatpickr-weekwrapper .flatpickr-weekday {
    float: none;
    width: 100%;
    line-height: 28px;
}
.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
    display: block;
    width: 100%;
    max-width: none;
    color: rgba(57, 57, 57, 0.3);
    background: 0 0;
    cursor: default;
    border: none;
}
.flatpickr-innerContainer {
    display: block;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
}
.flatpickr-rContainer {
    display: inline-block;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.flatpickr-time {
    text-align: center;
    outline: 0;
    display: block;
    height: 0;
    line-height: 40px;
    max-height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.flatpickr-time:after {
    content: "";
    display: table;
    clear: both;
}
.flatpickr-time .numInputWrapper {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    width: 40%;
    height: 40px;
    float: left;
}
.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-bottom-color: #393939;
}
.flatpickr-time .numInputWrapper span.arrowDown:after {
    border-top-color: #393939;
}
.flatpickr-time.hasSeconds .numInputWrapper {
    width: 26%;
}
.flatpickr-time.time24hr .numInputWrapper {
    width: 49%;
}
.flatpickr-time input {
    background: 0 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 0;
    border-radius: 0;
    text-align: center;
    margin: 0;
    padding: 0;
    height: inherit;
    line-height: inherit;
    color: #393939;
    font-size: 14px;
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
}
.flatpickr-time input.flatpickr-hour {
    font-weight: 700;
}
.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
    font-weight: 400;
}
.flatpickr-time input:focus {
    outline: 0;
    border: 0;
}
.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator {
    height: inherit;
    float: left;
    line-height: inherit;
    color: #393939;
    font-weight: 700;
    width: 2%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-align-self: center;
    -ms-flex-item-align: center;
    align-self: center;
}
.flatpickr-time .flatpickr-am-pm {
    outline: 0;
    width: 18%;
    cursor: pointer;
    text-align: center;
    font-weight: 400;
}
.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time input:hover {
    background: #eee;
}
.flatpickr-input[readonly] {
    cursor: pointer;
}
@-webkit-keyframes fpFadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@keyframes fpFadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
.daterangepicker {
    position: absolute;
    color: inherit;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 100px;
    inset-inline-start: 20px;
    z-index: 3001;
    display: none;
    font-family: arial;
    font-size: 15px;
    line-height: 1em;
}
.daterangepicker:after,
.daterangepicker:before {
    position: absolute;
    display: inline-block;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    content: "";
}
.daterangepicker:before {
    top: -7px;
    border-inline-end: 7px solid transparent;
    border-inline-start: 7px solid transparent;
    border-bottom: 7px solid #ccc;
}
.daterangepicker:after {
    top: -6px;
    border-inline-end: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-inline-start: 6px solid transparent;
}
.daterangepicker.opensleft:before {
    inset-inline-end: 9px;
}
.daterangepicker.opensleft:after {
    inset-inline-end: 10px;
}
.daterangepicker.openscenter:before {
    inset-inline-start: 0;
    inset-inline-end: 0;
    width: 0;
    margin-inline-start: auto;
    margin-inline-end: auto;
}
.daterangepicker.openscenter:after {
    inset-inline-start: 0;
    inset-inline-end: 0;
    width: 0;
    margin-inline-start: auto;
    margin-inline-end: auto;
}
.daterangepicker.opensright:before {
    inset-inline-start: 9px;
}
.daterangepicker.opensright:after {
    inset-inline-start: 10px;
}
.daterangepicker.drop-up {
    margin-top: -7px;
}
.daterangepicker.drop-up:before {
    top: initial;
    bottom: -7px;
    border-bottom: initial;
    border-top: 7px solid #ccc;
}
.daterangepicker.drop-up:after {
    top: initial;
    bottom: -6px;
    border-bottom: initial;
    border-top: 6px solid #fff;
}
.daterangepicker.single .daterangepicker .ranges,
.daterangepicker.single .drp-calendar {
    float: none;
}
.daterangepicker.single .drp-selected {
    display: none;
}
.daterangepicker.show-calendar .drp-calendar {
    display: block;
}
.daterangepicker.show-calendar .drp-buttons {
    display: block;
}
.daterangepicker.auto-apply .drp-buttons {
    display: none;
}
.daterangepicker .drp-calendar {
    display: none;
    max-width: 270px;
}
.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 8px;
}
.daterangepicker .drp-calendar.right {
    padding: 8px;
}
.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}
.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid #000;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}
.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}
.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}
.daterangepicker .calendar-table td,
.daterangepicker .calendar-table th {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}
.daterangepicker .calendar-table {
    border: 1px solid #fff;
    border-radius: 4px;
    background-color: #fff;
}
.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}
.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
    background-color: #eee;
    border-color: transparent;
    color: inherit;
}
.daterangepicker td.week,
.daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}
.daterangepicker td.off,
.daterangepicker td.off.end-date,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}
.daterangepicker td.in-range {
    background-color: #ebf4f8;
    border-color: transparent;
    color: #000;
    border-radius: 0;
}
.daterangepicker td.start-date {
    border-radius: 4px 0 0 4px;
}
.daterangepicker td.end-date {
    border-radius: 0 4px 4px 0;
}
.daterangepicker td.start-date.end-date {
    border-radius: 4px;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
    background-color: #357ebd;
    border-color: transparent;
    color: #fff;
}
.daterangepicker th.month {
    width: auto;
}
.daterangepicker option.disabled,
.daterangepicker td.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}
.daterangepicker select.monthselect,
.daterangepicker select.yearselect {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}
.daterangepicker select.monthselect {
    margin-inline-end: 2%;
    width: 56%;
}
.daterangepicker select.yearselect {
    width: 40%;
}
.daterangepicker select.ampmselect,
.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.secondselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}
.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}
.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}
.daterangepicker .drp-buttons {
    clear: both;
    text-align: end;
    padding: 8px;
    border-top: 1px solid #ddd;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}
.daterangepicker .drp-selected {
    display: inline-block;
    font-size: 12px;
    padding-inline-end: 8px;
}
.daterangepicker .drp-buttons .btn {
    margin-inline-start: 8px;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
}
.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-inline-end: 1px solid #ddd;
}
.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-inline-start: 1px solid #ddd;
}
.daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-inline-end: 1px solid #ddd;
}
.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-inline-start: 1px solid #ddd;
}
.daterangepicker .ranges {
    float: none;
    text-align: start;
    margin: 0;
}
.daterangepicker.show-calendar .ranges {
    margin-top: 8px;
}
.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}
.daterangepicker .ranges li {
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
}
.daterangepicker .ranges li:hover {
    background-color: #eee;
}
.daterangepicker .ranges li.active {
    background-color: #08c;
    color: #fff;
}
@media (min-width: 564px) {
    .daterangepicker {
        width: auto;
    }
    .daterangepicker .ranges ul {
        width: 140px;
    }
    .daterangepicker.single .ranges ul {
        width: 100%;
    }
    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }
    .daterangepicker.single .drp-calendar,
    .daterangepicker.single .ranges {
        float: left;
    }
    .daterangepicker {
        direction: ltr;
        text-align: start;
    }
    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-inline-end: 0;
    }
    .daterangepicker .drp-calendar.left .calendar-table {
        border-inline-end: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    .daterangepicker .drp-calendar.right {
        margin-inline-start: 0;
    }
    .daterangepicker .drp-calendar.right .calendar-table {
        border-inline-start: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    .daterangepicker .drp-calendar.left .calendar-table {
        padding-inline-end: 8px;
    }
    .daterangepicker .drp-calendar,
    .daterangepicker .ranges {
        float: left;
    }
}
@media (min-width: 730px) {
    .daterangepicker .ranges {
        width: auto;
    }
    .daterangepicker .ranges {
        float: left;
    }
    .daterangepicker.rtl .ranges {
        float: right;
    }
    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}
@keyframes chartjs-render-animation {
    from {
        opacity: 0.99;
    }
    to {
        opacity: 1;
    }
}
.chartjs-render-monitor {
    animation: chartjs-render-animation 1ms;
}
.chartjs-size-monitor,
.chartjs-size-monitor-expand,
.chartjs-size-monitor-shrink {
    position: absolute;
    direction: ltr;
    inset-inline-start: 0;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    visibility: hidden;
    z-index: -1;
}
.chartjs-size-monitor-expand > div {
    position: absolute;
    width: 1000000px;
    height: 1000000px;
    inset-inline-start: 0;
    top: 0;
}
.chartjs-size-monitor-shrink > div {
    position: absolute;
    width: 200%;
    height: 200%;
    inset-inline-start: 0;
    top: 0;
}
/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */
.ql-container {
    box-sizing: border-box;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 13px;
    height: 100%;
    margin: 0;
    position: relative;
}
.ql-container.ql-disabled .ql-tooltip {
    visibility: hidden;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
    pointer-events: none;
}
.ql-clipboard {
    inset-inline-start: -100000px;
    height: 1px;
    overflow-y: hidden;
    position: absolute;
    top: 50%;
}
.ql-clipboard p {
    margin: 0;
    padding: 0;
}
.ql-editor {
    box-sizing: border-box;
    line-height: 1.42;
    height: 100%;
    outline: 0;
    overflow-y: auto;
    padding: 12px 15px;
    tab-size: 4;
    -moz-tab-size: 4;
    text-align: start;
    white-space: pre-wrap;
    word-wrap: break-word;
}
.ql-editor > * {
    cursor: text;
}
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor ol,
.ql-editor p,
.ql-editor pre,
.ql-editor ul {
    margin: 0;
    padding: 0;
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8
        list-9;
}
.ql-editor ol,
.ql-editor ul {
    padding-inline-start: 1.5em;
}
.ql-editor ol > li,
.ql-editor ul > li {
    list-style-type: none;
}
.ql-editor ul > li::before {
    content: "\2022";
}
.ql-editor ul[data-checked="false"],
.ql-editor ul[data-checked="true"] {
    pointer-events: none;
}
.ql-editor ul[data-checked="false"] > li *,
.ql-editor ul[data-checked="true"] > li * {
    pointer-events: all;
}
.ql-editor ul[data-checked="false"] > li::before,
.ql-editor ul[data-checked="true"] > li::before {
    color: #777;
    cursor: pointer;
    pointer-events: all;
}
.ql-editor ul[data-checked="true"] > li::before {
    content: "\2611";
}
.ql-editor ul[data-checked="false"] > li::before {
    content: "\2610";
}
.ql-editor li::before {
    display: inline-block;
    white-space: nowrap;
    width: 1.2em;
}
.ql-editor li:not(.ql-direction-rtl)::before {
    margin-inline-start: -1.5em;
    margin-inline-end: 0.3em;
    text-align: end;
}
.ql-editor li.ql-direction-rtl::before {
    margin-inline-start: 0.3em;
    margin-inline-end: -1.5em;
}
.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
    padding-inline-start: 1.5em;
}
.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
    padding-inline-end: 1.5em;
}
.ql-editor ol li {
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8
        list-9;
    counter-increment: list-0;
}
.ql-editor ol li:before {
    content: counter(list-0, decimal) ". ";
}
.ql-editor ol li.ql-indent-1 {
    counter-increment: list-1;
}
.ql-editor ol li.ql-indent-1:before {
    content: counter(list-1, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-1 {
    counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2 {
    counter-increment: list-2;
}
.ql-editor ol li.ql-indent-2:before {
    content: counter(list-2, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-2 {
    counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3 {
    counter-increment: list-3;
}
.ql-editor ol li.ql-indent-3:before {
    content: counter(list-3, decimal) ". ";
}
.ql-editor ol li.ql-indent-3 {
    counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4 {
    counter-increment: list-4;
}
.ql-editor ol li.ql-indent-4:before {
    content: counter(list-4, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-4 {
    counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5 {
    counter-increment: list-5;
}
.ql-editor ol li.ql-indent-5:before {
    content: counter(list-5, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-5 {
    counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6 {
    counter-increment: list-6;
}
.ql-editor ol li.ql-indent-6:before {
    content: counter(list-6, decimal) ". ";
}
.ql-editor ol li.ql-indent-6 {
    counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-7 {
    counter-increment: list-7;
}
.ql-editor ol li.ql-indent-7:before {
    content: counter(list-7, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-7 {
    counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-8 {
    counter-increment: list-8;
}
.ql-editor ol li.ql-indent-8:before {
    content: counter(list-8, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-8 {
    counter-reset: list-9;
}
.ql-editor ol li.ql-indent-9 {
    counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
    content: counter(list-9, decimal) ". ";
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
    padding-inline-start: 3em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
    padding-inline-start: 4.5em;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-inline-end: 3em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-inline-end: 4.5em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
    padding-inline-start: 6em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
    padding-inline-start: 7.5em;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-inline-end: 6em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-inline-end: 7.5em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
    padding-inline-start: 9em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
    padding-inline-start: 10.5em;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-inline-end: 9em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-inline-end: 10.5em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
    padding-inline-start: 12em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
    padding-inline-start: 13.5em;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-inline-end: 12em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-inline-end: 13.5em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
    padding-inline-start: 15em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
    padding-inline-start: 16.5em;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-inline-end: 15em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-inline-end: 16.5em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
    padding-inline-start: 18em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
    padding-inline-start: 19.5em;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-inline-end: 18em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-inline-end: 19.5em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
    padding-inline-start: 21em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
    padding-inline-start: 22.5em;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-inline-end: 21em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-inline-end: 22.5em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
    padding-inline-start: 24em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
    padding-inline-start: 25.5em;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-inline-end: 24em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-inline-end: 25.5em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
    padding-inline-start: 27em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
    padding-inline-start: 28.5em;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-inline-end: 27em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-inline-end: 28.5em;
}
.ql-editor .ql-video {
    display: block;
    max-width: 100%;
}
.ql-editor .ql-video.ql-align-center {
    margin: 0 auto;
}
.ql-editor .ql-video.ql-align-right {
    margin: 0 0 0 auto;
}
.ql-editor .ql-bg-black {
    background-color: #000;
}
.ql-editor .ql-bg-red {
    background-color: #e60000;
}
.ql-editor .ql-bg-orange {
    background-color: #f90;
}
.ql-editor .ql-bg-yellow {
    background-color: #ff0;
}
.ql-editor .ql-bg-green {
    background-color: #008a00;
}
.ql-editor .ql-bg-blue {
    background-color: #06c;
}
.ql-editor .ql-bg-purple {
    background-color: #93f;
}
.ql-editor .ql-color-white {
    color: #fff;
}
.ql-editor .ql-color-red {
    color: #e60000;
}
.ql-editor .ql-color-orange {
    color: #f90;
}
.ql-editor .ql-color-yellow {
    color: #ff0;
}
.ql-editor .ql-color-green {
    color: #008a00;
}
.ql-editor .ql-color-blue {
    color: #06c;
}
.ql-editor .ql-color-purple {
    color: #93f;
}
.ql-editor .ql-font-serif {
    font-family: Georgia, Times New Roman, serif;
}
.ql-editor .ql-font-monospace {
    font-family: Monaco, Courier New, monospace;
}
.ql-editor .ql-size-small {
    font-size: 0.75em;
}
.ql-editor .ql-size-large {
    font-size: 1.5em;
}
.ql-editor .ql-size-huge {
    font-size: 2.5em;
}
.ql-editor .ql-direction-rtl {
    direction: rtl;
    text-align: inherit;
}
.ql-editor .ql-align-center {
    text-align: center;
}
.ql-editor .ql-align-justify {
    text-align: justify;
}
.ql-editor .ql-align-right {
    text-align: end;
}
.ql-editor.ql-blank::before {
    color: rgba(0, 0, 0, 0.6);
    content: attr(data-placeholder);
    font-style: italic;
    inset-inline-start: 15px;
    pointer-events: none;
    position: absolute;
    inset-inline-end: 15px;
}
.ql-snow .ql-toolbar:after,
.ql-snow.ql-toolbar:after {
    clear: both;
    content: "";
    display: table;
}
.ql-snow .ql-toolbar button,
.ql-snow.ql-toolbar button {
    background: 0 0;
    border: none;
    cursor: pointer;
    display: inline-block;
    float: left;
    height: 24px;
    padding: 3px 5px;
    width: 28px;
}
.ql-snow .ql-toolbar button svg,
.ql-snow.ql-toolbar button svg {
    float: left;
    height: 100%;
}
.ql-snow .ql-toolbar button:active:hover,
.ql-snow.ql-toolbar button:active:hover {
    outline: 0;
}
.ql-snow .ql-toolbar input.ql-image[type="file"],
.ql-snow.ql-toolbar input.ql-image[type="file"] {
    display: none;
}
.ql-snow .ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button:focus,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow.ql-toolbar button:focus,
.ql-snow.ql-toolbar button:hover {
    color: #06c;
}
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill {
    fill: #06c;
}
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter {
    stroke: #06c;
}
@media (pointer: coarse) {
    .ql-snow .ql-toolbar button:hover:not(.ql-active),
    .ql-snow.ql-toolbar button:hover:not(.ql-active) {
        color: #444;
    }
    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,
    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,
    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,
    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
        fill: #444;
    }
    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,
    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,
    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,
    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
        stroke: #444;
    }
}
.ql-snow {
    box-sizing: border-box;
}
.ql-snow * {
    box-sizing: border-box;
}
.ql-snow .ql-hidden {
    display: none;
}
.ql-snow .ql-out-bottom,
.ql-snow .ql-out-top {
    visibility: hidden;
}
.ql-snow .ql-tooltip {
    position: absolute;
    transform: translateY(10px);
}
.ql-snow .ql-tooltip a {
    cursor: pointer;
    text-decoration: none;
}
.ql-snow .ql-tooltip.ql-flip {
    transform: translateY(-10px);
}
.ql-snow .ql-formats {
    display: inline-block;
    vertical-align: middle;
}
.ql-snow .ql-formats:after {
    clear: both;
    content: "";
    display: table;
}
.ql-snow .ql-stroke {
    fill: none;
    stroke: #444;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 2;
}
.ql-snow .ql-stroke-miter {
    fill: none;
    stroke: #444;
    stroke-miterlimit: 10;
    stroke-width: 2;
}
.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill {
    fill: #444;
}
.ql-snow .ql-empty {
    fill: none;
}
.ql-snow .ql-even {
    fill-rule: evenodd;
}
.ql-snow .ql-stroke.ql-thin,
.ql-snow .ql-thin {
    stroke-width: 1;
}
.ql-snow .ql-transparent {
    opacity: 0.4;
}
.ql-snow .ql-direction svg:last-child {
    display: none;
}
.ql-snow .ql-direction.ql-active svg:last-child {
    display: inline;
}
.ql-snow .ql-direction.ql-active svg:first-child {
    display: none;
}
.ql-snow .ql-editor h1 {
    font-size: 2em;
}
.ql-snow .ql-editor h2 {
    font-size: 1.5em;
}
.ql-snow .ql-editor h3 {
    font-size: 1.17em;
}
.ql-snow .ql-editor h4 {
    font-size: 1em;
}
.ql-snow .ql-editor h5 {
    font-size: 0.83em;
}
.ql-snow .ql-editor h6 {
    font-size: 0.67em;
}
.ql-snow .ql-editor a {
    text-decoration: underline;
}
.ql-snow .ql-editor blockquote {
    border-inline-start: 4px solid #ccc;
    margin-bottom: 5px;
    margin-top: 5px;
    padding-inline-start: 16px;
}
.ql-snow .ql-editor code,
.ql-snow .ql-editor pre {
    background-color: #f0f0f0;
    border-radius: 3px;
}
.ql-snow .ql-editor pre {
    white-space: pre-wrap;
    margin-bottom: 5px;
    margin-top: 5px;
    padding: 5px 10px;
}
.ql-snow .ql-editor code {
    font-size: 85%;
    padding: 2px 4px;
}
.ql-snow .ql-editor pre.ql-syntax {
    background-color: #23241f;
    color: #f8f8f2;
    overflow: visible;
}
.ql-snow .ql-editor img {
    max-width: 100%;
}
.ql-snow .ql-picker {
    color: #444;
    display: inline-block;
    float: left;
    font-size: 14px;
    font-weight: 500;
    height: 24px;
    position: relative;
    vertical-align: middle;
}
.ql-snow .ql-picker-label {
    cursor: pointer;
    display: inline-block;
    height: 100%;
    padding-inline-start: 8px;
    padding-inline-end: 2px;
    position: relative;
    width: 100%;
}
.ql-snow .ql-picker-label::before {
    display: inline-block;
    line-height: 22px;
}
.ql-snow .ql-picker-options {
    background-color: #fff;
    display: none;
    min-width: 100%;
    padding: 4px 8px;
    position: absolute;
    white-space: nowrap;
}
.ql-snow .ql-picker-options .ql-picker-item {
    cursor: pointer;
    display: block;
    padding-bottom: 5px;
    padding-top: 5px;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    color: #ccc;
    z-index: 2;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
    fill: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
    stroke: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-options {
    display: block;
    margin-top: -1px;
    top: 100%;
    z-index: 1;
}
.ql-snow .ql-color-picker,
.ql-snow .ql-icon-picker {
    width: 28px;
}
.ql-snow .ql-color-picker .ql-picker-label,
.ql-snow .ql-icon-picker .ql-picker-label {
    padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-label svg,
.ql-snow .ql-icon-picker .ql-picker-label svg {
    inset-inline-end: 4px;
}
.ql-snow .ql-icon-picker .ql-picker-options {
    padding: 4px 0;
}
.ql-snow .ql-icon-picker .ql-picker-item {
    height: 24px;
    width: 24px;
    padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-options {
    padding: 3px 5px;
    width: 152px;
}
.ql-snow .ql-color-picker .ql-picker-item {
    border: 1px solid transparent;
    float: left;
    height: 16px;
    margin: 2px;
    padding: 0;
    width: 16px;
}
.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
    position: absolute;
    margin-top: -9px;
    inset-inline-end: 0;
    top: 50%;
    width: 18px;
}
.ql-snow
    .ql-picker.ql-font
    .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-snow
    .ql-picker.ql-font
    .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-snow
    .ql-picker.ql-header
    .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-snow
    .ql-picker.ql-header
    .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-snow
    .ql-picker.ql-size
    .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-snow
    .ql-picker.ql-size
    .ql-picker-label[data-label]:not([data-label=""])::before {
    content: attr(data-label);
}
.ql-snow .ql-picker.ql-header {
    width: 98px;
}
.ql-snow .ql-picker.ql-header .ql-picker-item::before,
.ql-snow .ql-picker.ql-header .ql-picker-label::before {
    content: "Normal";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before {
    content: "Heading 1";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before {
    content: "Heading 2";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before {
    content: "Heading 3";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before {
    content: "Heading 4";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before {
    content: "Heading 5";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before {
    content: "Heading 6";
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    font-size: 2em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    font-size: 1.5em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    font-size: 1.17em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    font-size: 1em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    font-size: 0.83em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    font-size: 0.67em;
}
.ql-snow .ql-picker.ql-font {
    width: 108px;
}
.ql-snow .ql-picker.ql-font .ql-picker-item::before,
.ql-snow .ql-picker.ql-font .ql-picker-label::before {
    content: "Sans Serif";
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before {
    content: "Serif";
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before {
    content: "Monospace";
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
    font-family: Georgia, Times New Roman, serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
    font-family: Monaco, Courier New, monospace;
}
.ql-snow .ql-picker.ql-size {
    width: 98px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item::before,
.ql-snow .ql-picker.ql-size .ql-picker-label::before {
    content: "Normal";
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before {
    content: "Small";
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before {
    content: "Large";
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before {
    content: "Huge";
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
    font-size: 10px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
    font-size: 18px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
    font-size: 32px;
}
.ql-snow .ql-color-picker.ql-background .ql-picker-item {
    background-color: #fff;
}
.ql-snow .ql-color-picker.ql-color .ql-picker-item {
    background-color: #000;
}
.ql-toolbar.ql-snow {
    border: 1px solid #ccc;
    box-sizing: border-box;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    padding: 8px;
}
.ql-toolbar.ql-snow .ql-formats {
    margin-inline-end: 15px;
}
.ql-toolbar.ql-snow .ql-picker-label {
    border: 1px solid transparent;
}
.ql-toolbar.ql-snow .ql-picker-options {
    border: 1px solid transparent;
    box-shadow: rgba(0, 0, 0, 0.2) 0 2px 8px;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
    border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {
    border-color: #000;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 0;
}
.ql-snow .ql-tooltip {
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 0 5px #ddd;
    color: #444;
    padding: 5px 12px;
    white-space: nowrap;
}
.ql-snow .ql-tooltip::before {
    content: "Visit URL:";
    line-height: 26px;
    margin-inline-end: 8px;
}
.ql-snow .ql-tooltip input[type="text"] {
    display: none;
    border: 1px solid #ccc;
    font-size: 13px;
    height: 26px;
    margin: 0;
    padding: 3px 5px;
    width: 170px;
}
.ql-snow .ql-tooltip a.ql-preview {
    display: inline-block;
    max-width: 200px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
}
.ql-snow .ql-tooltip a.ql-action::after {
    border-inline-end: 1px solid #ccc;
    content: "Edit";
    margin-inline-start: 16px;
    padding-inline-end: 8px;
}
.ql-snow .ql-tooltip a.ql-remove::before {
    content: "Remove";
    margin-inline-start: 8px;
}
.ql-snow .ql-tooltip a {
    line-height: 26px;
}
.ql-snow .ql-tooltip.ql-editing a.ql-preview,
.ql-snow .ql-tooltip.ql-editing a.ql-remove {
    display: none;
}
.ql-snow .ql-tooltip.ql-editing input[type="text"] {
    display: inline-block;
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-inline-end: 0;
    content: "Save";
    padding-inline-end: 0;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
    content: "Enter link:";
}
.ql-snow .ql-tooltip[data-mode="formula"]::before {
    content: "Enter formula:";
}
.ql-snow .ql-tooltip[data-mode="video"]::before {
    content: "Enter video:";
}
.ql-snow a {
    color: #06c;
}
.ql-container.ql-snow {
    border: 1px solid #ccc;
}
.leaflet-image-layer,
.leaflet-layer,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-pane,
.leaflet-pane > canvas,
.leaflet-pane > svg,
.leaflet-tile,
.leaflet-tile-container,
.leaflet-zoom-box {
    position: absolute;
    inset-inline-start: 0;
    top: 0;
}
.leaflet-container {
    overflow: hidden;
}
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-tile {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
}
.leaflet-tile::selection {
    background: 0 0;
}
.leaflet-safari .leaflet-tile {
    image-rendering: -webkit-optimize-contrast;
}
.leaflet-safari .leaflet-tile-container {
    width: 1600px;
    height: 1600px;
    -webkit-transform-origin: 0 0;
}
.leaflet-marker-icon,
.leaflet-marker-shadow {
    display: block;
}
.leaflet-container .leaflet-marker-pane img,
.leaflet-container .leaflet-overlay-pane svg,
.leaflet-container .leaflet-shadow-pane img,
.leaflet-container .leaflet-tile,
.leaflet-container .leaflet-tile-pane img,
.leaflet-container img.leaflet-image-layer {
    max-width: none !important;
    max-height: none !important;
}
.leaflet-container.leaflet-touch-zoom {
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
}
.leaflet-container.leaflet-touch-drag {
    -ms-touch-action: pinch-zoom;
    touch-action: none;
    touch-action: pinch-zoom;
}
.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
    -ms-touch-action: none;
    touch-action: none;
}
.leaflet-container {
    -webkit-tap-highlight-color: transparent;
}
.leaflet-container a {
    -webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);
}
.leaflet-tile {
    filter: inherit;
    visibility: hidden;
}
.leaflet-tile-loaded {
    visibility: inherit;
}
.leaflet-zoom-box {
    width: 0;
    height: 0;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    z-index: 800;
}
.leaflet-overlay-pane svg {
    -moz-user-select: none;
}
.leaflet-pane {
    z-index: 400;
}
.leaflet-tile-pane {
    z-index: 200;
}
.leaflet-overlay-pane {
    z-index: 400;
}
.leaflet-shadow-pane {
    z-index: 500;
}
.leaflet-marker-pane {
    z-index: 600;
}
.leaflet-tooltip-pane {
    z-index: 650;
}
.leaflet-popup-pane {
    z-index: 700;
}
.leaflet-map-pane canvas {
    z-index: 100;
}
.leaflet-map-pane svg {
    z-index: 200;
}
.leaflet-vml-shape {
    width: 1px;
    height: 1px;
}
.lvml {
    behavior: url(#default#VML);
    display: inline-block;
    position: absolute;
}
.leaflet-control {
    position: relative;
    z-index: 800;
    pointer-events: visiblePainted;
    pointer-events: auto;
}
.leaflet-bottom,
.leaflet-top {
    position: absolute;
    z-index: 1000;
    pointer-events: none;
}
.leaflet-top {
    top: 0;
}
.leaflet-right {
    inset-inline-end: 0;
}
.leaflet-bottom {
    bottom: 0;
}
.leaflet-left {
    inset-inline-start: 0;
}
.leaflet-control {
    float: left;
    clear: both;
}
.leaflet-right .leaflet-control {
    float: right;
}
.leaflet-top .leaflet-control {
    margin-top: 10px;
}
.leaflet-bottom .leaflet-control {
    margin-bottom: 10px;
}
.leaflet-left .leaflet-control {
    margin-inline-start: 10px;
}
.leaflet-right .leaflet-control {
    margin-inline-end: 10px;
}
.leaflet-fade-anim .leaflet-tile {
    will-change: opacity;
}
.leaflet-fade-anim .leaflet-popup {
    opacity: 0;
    -webkit-transition: opacity 0.2s linear;
    -moz-transition: opacity 0.2s linear;
    transition: opacity 0.2s linear;
}
.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
    opacity: 1;
}
.leaflet-zoom-animated {
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
}
.leaflet-zoom-anim .leaflet-zoom-animated {
    will-change: transform;
}
.leaflet-zoom-anim .leaflet-zoom-animated {
    -webkit-transition: -webkit-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
    -moz-transition: -moz-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
    transition: transform 0.25s cubic-bezier(0, 0, 0.25, 1);
}
.leaflet-pan-anim .leaflet-tile,
.leaflet-zoom-anim .leaflet-tile {
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
}
.leaflet-zoom-anim .leaflet-zoom-hide {
    visibility: hidden;
}
.leaflet-interactive {
    cursor: pointer;
}
.leaflet-grab {
    cursor: -webkit-grab;
    cursor: -moz-grab;
    cursor: grab;
}
.leaflet-crosshair,
.leaflet-crosshair .leaflet-interactive {
    cursor: crosshair;
}
.leaflet-control,
.leaflet-popup-pane {
    cursor: auto;
}
.leaflet-dragging .leaflet-grab,
.leaflet-dragging .leaflet-grab .leaflet-interactive,
.leaflet-dragging .leaflet-marker-draggable {
    cursor: move;
    cursor: -webkit-grabbing;
    cursor: -moz-grabbing;
    cursor: grabbing;
}
.leaflet-image-layer,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-pane > svg path,
.leaflet-tile-container {
    pointer-events: none;
}
.leaflet-image-layer.leaflet-interactive,
.leaflet-marker-icon.leaflet-interactive,
.leaflet-pane > svg path.leaflet-interactive,
svg.leaflet-image-layer.leaflet-interactive path {
    pointer-events: visiblePainted;
    pointer-events: auto;
}
.leaflet-container {
    background: #ddd;
    outline: 0;
}
.leaflet-container a {
    color: #0078a8;
}
.leaflet-container a.leaflet-active {
    outline: 2px solid orange;
}
.leaflet-zoom-box {
    border: 2px dotted #38f;
    background: rgba(255, 255, 255, 0.5);
}
.leaflet-container {
    font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;
}
.leaflet-bar {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
    border-radius: 4px;
}
.leaflet-bar a,
.leaflet-bar a:hover {
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    width: 26px;
    height: 26px;
    line-height: 26px;
    display: block;
    text-align: center;
    text-decoration: none;
    color: #000;
}
.leaflet-bar a,
.leaflet-control-layers-toggle {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    display: block;
}
.leaflet-bar a:hover {
    background-color: #f4f4f4;
}
.leaflet-bar a:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.leaflet-bar a:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: none;
}
.leaflet-bar a.leaflet-disabled {
    cursor: default;
    background-color: #f4f4f4;
    color: #bbb;
}
.leaflet-touch .leaflet-bar a {
    width: 30px;
    height: 30px;
    line-height: 30px;
}
.leaflet-touch .leaflet-bar a:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}
.leaflet-touch .leaflet-bar a:last-child {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
}
.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
    font: bold 18px "Lucida Console", Monaco, monospace;
    text-indent: 1px;
}
.leaflet-touch .leaflet-control-zoom-in,
.leaflet-touch .leaflet-control-zoom-out {
    font-size: 22px;
}
.leaflet-control-layers {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    background: #fff;
    border-radius: 5px;
}
.leaflet-control-layers-toggle {
    background-image: url(images/layers.html);
    width: 36px;
    height: 36px;
}
.leaflet-retina .leaflet-control-layers-toggle {
    background-image: url(images/layers-2x.html);
    background-size: 26px 26px;
}
.leaflet-touch .leaflet-control-layers-toggle {
    width: 44px;
    height: 44px;
}
.leaflet-control-layers .leaflet-control-layers-list,
.leaflet-control-layers-expanded .leaflet-control-layers-toggle {
    display: none;
}
.leaflet-control-layers-expanded .leaflet-control-layers-list {
    display: block;
    position: relative;
}
.leaflet-control-layers-expanded {
    padding: 6px 10px 6px 6px;
    color: #333;
    background: #fff;
}
.leaflet-control-layers-scrollbar {
    overflow-y: scroll;
    overflow-x: hidden;
    padding-inline-end: 5px;
}
.leaflet-control-layers-selector {
    margin-top: 2px;
    position: relative;
    top: 1px;
}
.leaflet-control-layers label {
    display: block;
}
.leaflet-control-layers-separator {
    height: 0;
    border-top: 1px solid #ddd;
    margin: 5px -10px 5px -6px;
}
.leaflet-default-icon-path {
    background-image: url(images/marker-icon.html);
}
.leaflet-container .leaflet-control-attribution {
    background: #fff;
    background: rgba(255, 255, 255, 0.7);
    margin: 0;
}
.leaflet-control-attribution,
.leaflet-control-scale-line {
    padding: 0 5px;
    color: #333;
}
.leaflet-control-attribution a {
    text-decoration: none;
}
.leaflet-control-attribution a:hover {
    text-decoration: underline;
}
.leaflet-container .leaflet-control-attribution,
.leaflet-container .leaflet-control-scale {
    font-size: 11px;
}
.leaflet-left .leaflet-control-scale {
    margin-inline-start: 5px;
}
.leaflet-bottom .leaflet-control-scale {
    margin-bottom: 5px;
}
.leaflet-control-scale-line {
    border: 2px solid #777;
    border-top: none;
    line-height: 1.1;
    padding: 2px 5px 1px;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    background: rgba(255, 255, 255, 0.5);
}
.leaflet-control-scale-line:not(:first-child) {
    border-top: 2px solid #777;
    border-bottom: none;
    margin-top: -2px;
}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
    border-bottom: 2px solid #777;
}
.leaflet-touch .leaflet-bar,
.leaflet-touch .leaflet-control-attribution,
.leaflet-touch .leaflet-control-layers {
    box-shadow: none;
}
.leaflet-touch .leaflet-bar,
.leaflet-touch .leaflet-control-layers {
    border: 2px solid rgba(0, 0, 0, 0.2);
    background-clip: padding-box;
}
.leaflet-popup {
    position: absolute;
    text-align: center;
    margin-bottom: 20px;
}
.leaflet-popup-content-wrapper {
    padding: 1px;
    text-align: start;
    border-radius: 12px;
}
.leaflet-popup-content {
    margin: 13px 19px;
    line-height: 1.4;
}
.leaflet-popup-content p {
    margin: 18px 0;
}
.leaflet-popup-tip-container {
    width: 40px;
    height: 20px;
    position: absolute;
    inset-inline-start: 50%;
    margin-inline-start: -20px;
    overflow: hidden;
    pointer-events: none;
}
.leaflet-popup-tip {
    width: 17px;
    height: 17px;
    padding: 1px;
    margin: -10px auto 0;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
    background: #fff;
    color: #333;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4);
}
.leaflet-container a.leaflet-popup-close-button {
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    padding: 4px 4px 0 0;
    border: none;
    text-align: center;
    width: 18px;
    height: 14px;
    font: 16px/14px Tahoma, Verdana, sans-serif;
    color: #c3c3c3;
    text-decoration: none;
    font-weight: 700;
    background: 0 0;
}
.leaflet-container a.leaflet-popup-close-button:hover {
    color: #999;
}
.leaflet-popup-scrolled {
    overflow: auto;
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
}
.leaflet-oldie .leaflet-popup-content-wrapper {
    -ms-zoom: 1;
}
.leaflet-oldie .leaflet-popup-tip {
    width: 24px;
    margin: 0 auto;
}
.leaflet-oldie .leaflet-popup-tip-container {
    margin-top: -1px;
}
.leaflet-oldie .leaflet-control-layers,
.leaflet-oldie .leaflet-control-zoom,
.leaflet-oldie .leaflet-popup-content-wrapper,
.leaflet-oldie .leaflet-popup-tip {
    border: 1px solid #999;
}
.leaflet-div-icon {
    background: #fff;
    border: 1px solid #666;
}
.leaflet-tooltip {
    position: absolute;
    padding: 6px;
    background-color: #fff;
    border: 1px solid #fff;
    border-radius: 3px;
    color: #222;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}
.leaflet-tooltip.leaflet-clickable {
    cursor: pointer;
    pointer-events: auto;
}
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before,
.leaflet-tooltip-top:before {
    position: absolute;
    pointer-events: none;
    border: 6px solid transparent;
    background: 0 0;
    content: "";
}
.leaflet-tooltip-bottom {
    margin-top: 6px;
}
.leaflet-tooltip-top {
    margin-top: -6px;
}
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-top:before {
    inset-inline-start: 50%;
    margin-inline-start: -6px;
}
.leaflet-tooltip-top:before {
    bottom: 0;
    margin-bottom: -12px;
    border-top-color: #fff;
}
.leaflet-tooltip-bottom:before {
    top: 0;
    margin-top: -12px;
    margin-inline-start: -6px;
    border-bottom-color: #fff;
}
.leaflet-tooltip-left {
    margin-inline-start: -6px;
}
.leaflet-tooltip-right {
    margin-inline-start: 6px;
}
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
    top: 50%;
    margin-top: -6px;
}
.leaflet-tooltip-left:before {
    inset-inline-end: 0;
    margin-inline-end: -12px;
    border-inline-start-color: #fff;
}
.leaflet-tooltip-right:before {
    inset-inline-start: 0;
    margin-inline-start: -12px;
    border-inline-end-color: #fff;
}
body.compensate-for-scrollbar {
    overflow: hidden;
}
.fancybox-active {
    height: auto;
}
.fancybox-is-hidden {
    inset-inline-start: -9999px;
    margin: 0;
    position: absolute !important;
    top: -9999px;
    visibility: hidden;
}
.fancybox-container {
    -webkit-backface-visibility: hidden;
    height: 100%;
    inset-inline-start: 0;
    outline: 0;
    position: fixed;
    -webkit-tap-highlight-color: transparent;
    top: 0;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    transform: translateZ(0);
    width: 100%;
    z-index: 99992;
}
.fancybox-container * {
    box-sizing: border-box;
}
.fancybox-bg,
.fancybox-inner,
.fancybox-outer,
.fancybox-stage {
    bottom: 0;
    inset-inline-start: 0;
    position: absolute;
    inset-inline-end: 0;
    top: 0;
}
.fancybox-outer {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
}
.fancybox-bg {
    background: #1e1e1e;
    opacity: 0;
    transition-duration: inherit;
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.47, 0, 0.74, 0.71);
}
.fancybox-is-open .fancybox-bg {
    opacity: 0.9;
    transition-timing-function: cubic-bezier(0.22, 0.61, 0.36, 1);
}
.fancybox-caption,
.fancybox-infobar,
.fancybox-navigation .fancybox-button,
.fancybox-toolbar {
    direction: ltr;
    opacity: 0;
    position: absolute;
    transition: opacity 0.25s ease, visibility 0s ease 0.25s;
    visibility: hidden;
    z-index: 99997;
}
.fancybox-show-caption .fancybox-caption,
.fancybox-show-infobar .fancybox-infobar,
.fancybox-show-nav .fancybox-navigation .fancybox-button,
.fancybox-show-toolbar .fancybox-toolbar {
    opacity: 1;
    transition: opacity 0.25s ease 0s, visibility 0s ease 0s;
    visibility: visible;
}
.fancybox-infobar {
    color: #ccc;
    font-size: 13px;
    -webkit-font-smoothing: subpixel-antialiased;
    height: 44px;
    inset-inline-start: 0;
    line-height: 44px;
    min-width: 44px;
    mix-blend-mode: difference;
    padding: 0 10px;
    pointer-events: none;
    top: 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.fancybox-toolbar {
    inset-inline-end: 0;
    top: 0;
}
.fancybox-stage {
    direction: ltr;
    overflow: visible;
    transform: translateZ(0);
    z-index: 99994;
}
.fancybox-is-open .fancybox-stage {
    overflow: hidden;
}
.fancybox-slide {
    -webkit-backface-visibility: hidden;
    display: none;
    height: 100%;
    inset-inline-start: 0;
    outline: 0;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding: 44px;
    position: absolute;
    text-align: center;
    top: 0;
    transition-property: transform, opacity;
    white-space: normal;
    width: 100%;
    z-index: 99994;
}
.fancybox-slide:before {
    content: "";
    display: inline-block;
    font-size: 0;
    height: 100%;
    vertical-align: middle;
    width: 0;
}
.fancybox-is-sliding .fancybox-slide,
.fancybox-slide--current,
.fancybox-slide--next,
.fancybox-slide--previous {
    display: block;
}
.fancybox-slide--image {
    overflow: hidden;
    padding: 44px 0;
}
.fancybox-slide--image:before {
    display: none;
}
.fancybox-slide--html {
    padding: 6px;
}
.fancybox-content {
    background: #fff;
    display: inline-block;
    margin: 0;
    max-width: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding: 44px;
    position: relative;
    text-align: start;
    vertical-align: middle;
}
.fancybox-slide--image .fancybox-content {
    animation-timing-function: cubic-bezier(0.5, 0, 0.14, 1);
    -webkit-backface-visibility: hidden;
    background: 0 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    inset-inline-start: 0;
    max-width: none;
    overflow: visible;
    padding: 0;
    position: absolute;
    top: 0;
    transform-origin: top left;
    transition-property: transform, opacity;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    z-index: 99995;
}
.fancybox-can-zoomOut .fancybox-content {
    cursor: zoom-out;
}
.fancybox-can-zoomIn .fancybox-content {
    cursor: zoom-in;
}
.fancybox-can-pan .fancybox-content,
.fancybox-can-swipe .fancybox-content {
    cursor: grab;
}
.fancybox-is-grabbing .fancybox-content {
    cursor: grabbing;
}
.fancybox-container [data-selectable="true"] {
    cursor: text;
}
.fancybox-image,
.fancybox-spaceball {
    background: 0 0;
    border: 0;
    height: 100%;
    inset-inline-start: 0;
    margin: 0;
    max-height: none;
    max-width: none;
    padding: 0;
    position: absolute;
    top: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 100%;
}
.fancybox-spaceball {
    z-index: 1;
}
.fancybox-slide--iframe .fancybox-content,
.fancybox-slide--map .fancybox-content,
.fancybox-slide--pdf .fancybox-content,
.fancybox-slide--video .fancybox-content {
    height: 100%;
    overflow: visible;
    padding: 0;
    width: 100%;
}
.fancybox-slide--video .fancybox-content {
    background: #000;
}
.fancybox-slide--map .fancybox-content {
    background: #e5e3df;
}
.fancybox-slide--iframe .fancybox-content {
    background: #fff;
}
.fancybox-iframe,
.fancybox-video {
    background: 0 0;
    border: 0;
    display: block;
    height: 100%;
    margin: 0;
    overflow: hidden;
    padding: 0;
    width: 100%;
}
.fancybox-iframe {
    inset-inline-start: 0;
    position: absolute;
    top: 0;
}
.fancybox-error {
    background: #fff;
    cursor: default;
    max-width: 400px;
    padding: 40px;
    width: 100%;
}
.fancybox-error p {
    color: #444;
    font-size: 16px;
    line-height: 20px;
    margin: 0;
    padding: 0;
}
.fancybox-button {
    background: rgba(30, 30, 30, 0.6);
    border: 0;
    border-radius: 0;
    box-shadow: none;
    cursor: pointer;
    display: inline-block;
    height: 44px;
    margin: 0;
    padding: 10px;
    position: relative;
    transition: color 0.2s;
    vertical-align: top;
    visibility: inherit;
    width: 44px;
}
.fancybox-button,
.fancybox-button:link,
.fancybox-button:visited {
    color: #ccc;
}
.fancybox-button:hover {
    color: #fff;
}
.fancybox-button:focus {
    outline: 0;
}
.fancybox-button.fancybox-focus {
    outline: 1px dotted;
}
.fancybox-button[disabled],
.fancybox-button[disabled]:hover {
    color: #888;
    cursor: default;
    outline: 0;
}
.fancybox-button div {
    height: 100%;
}
.fancybox-button svg {
    display: block;
    height: 100%;
    overflow: visible;
    position: relative;
    width: 100%;
}
.fancybox-button svg path {
    fill: currentColor;
    stroke-width: 0;
}
.fancybox-button--fsenter svg:nth-child(2),
.fancybox-button--fsexit svg:first-child,
.fancybox-button--pause svg:first-child,
.fancybox-button--play svg:nth-child(2) {
    display: none;
}
.fancybox-progress {
    background: #ff5268;
    height: 2px;
    inset-inline-start: 0;
    position: absolute;
    inset-inline-end: 0;
    top: 0;
    transform: scaleX(0);
    transform-origin: 0;
    transition-property: transform;
    transition-timing-function: linear;
    z-index: 99998;
}
.fancybox-close-small {
    background: 0 0;
    border: 0;
    border-radius: 0;
    color: #ccc;
    cursor: pointer;
    opacity: 0.8;
    padding: 8px;
    position: absolute;
    inset-inline-end: -12px;
    top: -44px;
    z-index: 401;
}
.fancybox-close-small:hover {
    color: #fff;
    opacity: 1;
}
.fancybox-slide--html .fancybox-close-small {
    color: currentColor;
    padding: 10px;
    inset-inline-end: 0;
    top: 0;
}
.fancybox-slide--image.fancybox-is-scaling .fancybox-content {
    overflow: hidden;
}
.fancybox-is-scaling .fancybox-close-small,
.fancybox-is-zoomable.fancybox-can-pan .fancybox-close-small {
    display: none;
}
.fancybox-navigation .fancybox-button {
    background-clip: content-box;
    height: 100px;
    opacity: 0;
    position: absolute;
    top: calc(50% - 50px);
    width: 70px;
}
.fancybox-navigation .fancybox-button div {
    padding: 7px;
}
.fancybox-navigation .fancybox-button--arrow_left {
    inset-inline-start: 0;
    inset-inline-start: env(safe-area-inset-left);
    padding: 31px 26px 31px 6px;
}
.fancybox-navigation .fancybox-button--arrow_right {
    padding: 31px 6px 31px 26px;
    inset-inline-end: 0;
    inset-inline-end: env(safe-area-inset-right);
}
.fancybox-caption {
    background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.85) 0,
        rgba(0, 0, 0, 0.3) 50%,
        rgba(0, 0, 0, 0.15) 65%,
        rgba(0, 0, 0, 0.075) 75.5%,
        rgba(0, 0, 0, 0.037) 82.85%,
        rgba(0, 0, 0, 0.019) 88%,
        transparent
    );
    bottom: 0;
    color: #eee;
    font-size: 14px;
    font-weight: 400;
    inset-inline-start: 0;
    line-height: 1.5;
    padding: 75px 44px 25px;
    pointer-events: none;
    inset-inline-end: 0;
    text-align: center;
    z-index: 99996;
}
@supports (padding: max(0px)) {
    .fancybox-caption {
        padding: 75px max(44px, env(safe-area-inset-right))
            max(25px, env(safe-area-inset-bottom))
            max(44px, env(safe-area-inset-left));
    }
}
.fancybox-caption--separate {
    margin-top: -50px;
}
.fancybox-caption__body {
    max-height: 50vh;
    overflow: auto;
    pointer-events: all;
}
.fancybox-caption a,
.fancybox-caption a:link,
.fancybox-caption a:visited {
    color: #ccc;
    text-decoration: none;
}
.fancybox-caption a:hover {
    color: #fff;
    text-decoration: underline;
}
.fancybox-loading {
    animation: a 1s linear infinite;
    background: 0 0;
    border: 4px solid #888;
    border-bottom-color: #fff;
    border-radius: 50%;
    height: 50px;
    inset-inline-start: 50%;
    margin: -25px 0 0 -25px;
    opacity: 0.7;
    padding: 0;
    position: absolute;
    top: 50%;
    width: 50px;
    z-index: 99999;
}
@keyframes a {
    to {
        transform: rotate(1turn);
    }
}
.fancybox-animated {
    transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
}
.fancybox-fx-slide.fancybox-slide--previous {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
}
.fancybox-fx-slide.fancybox-slide--next {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
}
.fancybox-fx-slide.fancybox-slide--current {
    opacity: 1;
    transform: translateZ(0);
}
.fancybox-fx-fade.fancybox-slide--next,
.fancybox-fx-fade.fancybox-slide--previous {
    opacity: 0;
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}
.fancybox-fx-fade.fancybox-slide--current {
    opacity: 1;
}
.fancybox-fx-zoom-in-out.fancybox-slide--previous {
    opacity: 0;
    transform: scale3d(1.5, 1.5, 1.5);
}
.fancybox-fx-zoom-in-out.fancybox-slide--next {
    opacity: 0;
    transform: scale3d(0.5, 0.5, 0.5);
}
.fancybox-fx-zoom-in-out.fancybox-slide--current {
    opacity: 1;
    transform: scaleX(1);
}
.fancybox-fx-rotate.fancybox-slide--previous {
    opacity: 0;
    transform: rotate(-1turn);
}
.fancybox-fx-rotate.fancybox-slide--next {
    opacity: 0;
    transform: rotate(1turn);
}
.fancybox-fx-rotate.fancybox-slide--current {
    opacity: 1;
    transform: rotate(0);
}
.fancybox-fx-circular.fancybox-slide--previous {
    opacity: 0;
    transform: scale3d(0, 0, 0) translate3d(-100%, 0, 0);
}
.fancybox-fx-circular.fancybox-slide--next {
    opacity: 0;
    transform: scale3d(0, 0, 0) translate3d(100%, 0, 0);
}
.fancybox-fx-circular.fancybox-slide--current {
    opacity: 1;
    transform: scaleX(1) translateZ(0);
}
.fancybox-fx-tube.fancybox-slide--previous {
    transform: translate3d(-100%, 0, 0) scale(0.1) skew(-10deg);
}
.fancybox-fx-tube.fancybox-slide--next {
    transform: translate3d(100%, 0, 0) scale(0.1) skew(10deg);
}
.fancybox-fx-tube.fancybox-slide--current {
    transform: translateZ(0) scale(1);
}
@media (max-height: 576px) {
    .fancybox-slide {
        padding-inline-start: 6px;
        padding-inline-end: 6px;
    }
    .fancybox-slide--image {
        padding: 6px 0;
    }
    .fancybox-close-small {
        inset-inline-end: -6px;
    }
    .fancybox-slide--image .fancybox-close-small {
        background: #4e4e4e;
        color: #f2f4f6;
        height: 36px;
        opacity: 1;
        padding: 6px;
        inset-inline-end: 0;
        top: 0;
        width: 36px;
    }
    .fancybox-caption {
        padding-inline-start: 12px;
        padding-inline-end: 12px;
    }
    @supports (padding: max(0px)) {
        .fancybox-caption {
            padding-inline-start: max(12px, env(safe-area-inset-left));
            padding-inline-end: max(12px, env(safe-area-inset-right));
        }
    }
}
.fancybox-share {
    background: #f4f4f4;
    border-radius: 3px;
    max-width: 90%;
    padding: 30px;
    text-align: center;
}
.fancybox-share h1 {
    color: #222;
    font-size: 35px;
    font-weight: 700;
    margin: 0 0 20px;
}
.fancybox-share p {
    margin: 0;
    padding: 0;
}
.fancybox-share__button {
    border: 0;
    border-radius: 3px;
    display: inline-block;
    font-size: 14px;
    font-weight: 700;
    line-height: 40px;
    margin: 0 5px 10px;
    min-width: 130px;
    padding: 0 15px;
    text-decoration: none;
    transition: all 0.2s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
}
.fancybox-share__button:link,
.fancybox-share__button:visited {
    color: #fff;
}
.fancybox-share__button:hover {
    text-decoration: none;
}
.fancybox-share__button--fb {
    background: #3b5998;
}
.fancybox-share__button--fb:hover {
    background: #344e86;
}
.fancybox-share__button--pt {
    background: #bd081d;
}
.fancybox-share__button--pt:hover {
    background: #aa0719;
}
.fancybox-share__button--tw {
    background: #1da1f2;
}
.fancybox-share__button--tw:hover {
    background: #0d95e8;
}
.fancybox-share__button svg {
    height: 25px;
    margin-inline-end: 7px;
    position: relative;
    top: -1px;
    vertical-align: middle;
    width: 25px;
}
.fancybox-share__button svg path {
    fill: #fff;
}
.fancybox-share__input {
    background: 0 0;
    border: 0;
    border-bottom: 1px solid #d7d7d7;
    border-radius: 0;
    color: #5d5b5b;
    font-size: 14px;
    margin: 10px 0 0;
    outline: 0;
    padding: 10px 15px;
    width: 100%;
}
.fancybox-thumbs {
    background: #ddd;
    bottom: 0;
    display: none;
    margin: 0;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    padding: 2px 2px 4px;
    position: absolute;
    inset-inline-end: 0;
    -webkit-tap-highlight-color: transparent;
    top: 0;
    width: 212px;
    z-index: 99995;
}
.fancybox-thumbs-x {
    overflow-x: auto;
    overflow-y: hidden;
}
.fancybox-show-thumbs .fancybox-thumbs {
    display: block;
}
.fancybox-show-thumbs .fancybox-inner {
    inset-inline-end: 212px;
}
.fancybox-thumbs__list {
    font-size: 0;
    height: 100%;
    list-style: none;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0;
    position: absolute;
    position: relative;
    white-space: nowrap;
    width: 100%;
}
.fancybox-thumbs-x .fancybox-thumbs__list {
    overflow: hidden;
}
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar {
    width: 7px;
}
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.fancybox-thumbs-y .fancybox-thumbs__list::-webkit-scrollbar-thumb {
    background: #2a2a2a;
    border-radius: 10px;
}
.fancybox-thumbs__list a {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background-color: rgba(0, 0, 0, 0.1);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    cursor: pointer;
    float: left;
    height: 75px;
    margin: 2px;
    max-height: calc(100% - 8px);
    max-width: calc(50% - 4px);
    outline: 0;
    overflow: hidden;
    padding: 0;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    width: 100px;
}
.fancybox-thumbs__list a:before {
    border: 6px solid #ff5268;
    bottom: 0;
    content: "";
    inset-inline-start: 0;
    opacity: 0;
    position: absolute;
    inset-inline-end: 0;
    top: 0;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 99991;
}
.fancybox-thumbs__list a:focus:before {
    opacity: 0.5;
}
.fancybox-thumbs__list a.fancybox-thumbs-active:before {
    opacity: 1;
}
@media (max-width: 576px) {
    .fancybox-thumbs {
        width: 110px;
    }
    .fancybox-show-thumbs .fancybox-inner {
        inset-inline-end: 110px;
    }
    .fancybox-thumbs__list a {
        max-width: calc(100% - 10px);
    }
}
table.DTFC_Cloned tfoot,
table.DTFC_Cloned thead {
    background-color: #fff;
}
div.DTFC_Blocker {
    background-color: #fff;
}
div.DTFC_LeftWrapper table.dataTable,
div.DTFC_RightWrapper table.dataTable {
    margin-bottom: 0;
    z-index: 2;
}
div.DTFC_LeftWrapper table.dataTable.no-footer,
div.DTFC_RightWrapper table.dataTable.no-footer {
    border-bottom: none;
}
table.dataTable.display tbody tr.DTFC_NoData {
    background-color: transparent;
}
.irs {
    position: relative;
    display: block;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-size: 12px;
    font-family: Arial, sans-serif;
}
.irs-line {
    position: relative;
    display: block;
    overflow: hidden;
    outline: 0 !important;
}
.irs-bar {
    position: absolute;
    display: block;
    inset-inline-start: 0;
    width: 0;
}
.irs-shadow {
    position: absolute;
    display: none;
    inset-inline-start: 0;
    width: 0;
}
.irs-handle {
    position: absolute;
    display: block;
    box-sizing: border-box;
    cursor: default;
    z-index: 1;
}
.irs-handle.type_last {
    z-index: 2;
}
.irs-max,
.irs-min {
    position: absolute;
    display: block;
    cursor: default;
}
.irs-min {
    inset-inline-start: 0;
}
.irs-max {
    inset-inline-end: 0;
}
.irs-from,
.irs-single,
.irs-to {
    position: absolute;
    display: block;
    top: 0;
    inset-inline-start: 0;
    cursor: default;
    white-space: nowrap;
}
.irs-grid {
    position: absolute;
    display: none;
    bottom: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 20px;
}
.irs-with-grid .irs-grid {
    display: block;
}
.irs-grid-pol {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 1px;
    height: 8px;
    background: #000;
}
.irs-grid-pol.small {
    height: 4px;
}
.irs-grid-text {
    position: absolute;
    bottom: 0;
    inset-inline-start: 0;
    white-space: nowrap;
    text-align: center;
    font-size: 9px;
    line-height: 9px;
    padding: 0 3px;
    color: #000;
}
.irs-disable-mask {
    position: absolute;
    display: block;
    top: 0;
    inset-inline-start: -1%;
    width: 102%;
    height: 100%;
    cursor: default;
    background: rgba(0, 0, 0, 0);
    z-index: 2;
}
.lt-ie9 .irs-disable-mask {
    background: #000;
    cursor: not-allowed;
}
.irs-disabled {
    opacity: 0.4;
}
.irs-hidden-input {
    position: absolute !important;
    display: block !important;
    top: 0 !important;
    inset-inline-start: 0 !important;
    width: 0 !important;
    height: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    outline: 0 !important;
    z-index: -9999 !important;
    background: 0 0 !important;
    border-style: solid !important;
    border-color: transparent !important;
}
.irs--flat {
    height: 40px;
}
.irs--flat.irs-with-grid {
    height: 60px;
}
.irs--flat .irs-line {
    top: 25px;
    height: 12px;
    background-color: #e1e4e9;
    border-radius: 4px;
}
.irs--flat .irs-bar {
    top: 25px;
    height: 12px;
    background-color: #ed5565;
}
.irs--flat .irs-bar--single {
    border-radius: 4px 0 0 4px;
}
.irs--flat .irs-shadow {
    height: 1px;
    bottom: 16px;
    background-color: #e1e4e9;
}
.irs--flat .irs-handle {
    top: 22px;
    width: 16px;
    height: 18px;
    background-color: transparent;
}
.irs--flat .irs-handle > i:first-child {
    position: absolute;
    display: block;
    top: 0;
    inset-inline-start: 50%;
    width: 2px;
    height: 100%;
    margin-inline-start: -1px;
    background-color: #da4453;
}
.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child {
    background-color: #a43540;
}
.irs--flat .irs-max,
.irs--flat .irs-min {
    top: 0;
    padding: 1px 3px;
    color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    background-color: #e1e4e9;
    border-radius: 4px;
}
.irs--flat .irs-from,
.irs--flat .irs-single,
.irs--flat .irs-to {
    color: #fff;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background-color: #ed5565;
    border-radius: 4px;
}
.irs--flat .irs-from:before,
.irs--flat .irs-single:before,
.irs--flat .irs-to:before {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    inset-inline-start: 50%;
    width: 0;
    height: 0;
    margin-inline-start: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #ed5565;
}
.irs--flat .irs-grid-pol {
    background-color: #e1e4e9;
}
.irs--flat .irs-grid-text {
    color: #999;
}
.irs--big {
    height: 55px;
}
.irs--big.irs-with-grid {
    height: 70px;
}
.irs--big .irs-line {
    top: 33px;
    height: 12px;
    background-color: #fff;
    background: linear-gradient(to bottom, #ddd -50%, #fff 150%);
    border: 1px solid #ccc;
    border-radius: 12px;
}
.irs--big .irs-bar {
    top: 33px;
    height: 12px;
    background-color: #92bce0;
    border: 1px solid #428bca;
    background: linear-gradient(to bottom, #fff 0, #428bca 30%, #b9d4ec 100%);
    box-shadow: inset 0 0 1px 1px rgba(255, 255, 255, 0.5);
}
.irs--big .irs-bar--single {
    border-radius: 12px 0 0 12px;
}
.irs--big .irs-shadow {
    height: 1px;
    bottom: 16px;
    background-color: rgba(66, 139, 202, 0.5);
}
.irs--big .irs-handle {
    top: 25px;
    width: 30px;
    height: 30px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    background-color: #cbcfd5;
    background: linear-gradient(to bottom, #fff 0, #b4b9be 30%, #fff 100%);
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2), inset 0 0 3px 1px #fff;
    border-radius: 30px;
}
.irs--big .irs-handle.state_hover,
.irs--big .irs-handle:hover {
    border-color: rgba(0, 0, 0, 0.45);
    background-color: #939ba7;
    background: linear-gradient(to bottom, #fff 0, #919ba5 30%, #fff 100%);
}
.irs--big .irs-max,
.irs--big .irs-min {
    top: 0;
    padding: 1px 5px;
    color: #fff;
    text-shadow: none;
    background-color: #9f9f9f;
    border-radius: 3px;
}
.irs--big .irs-from,
.irs--big .irs-single,
.irs--big .irs-to {
    color: #fff;
    text-shadow: none;
    padding: 1px 5px;
    background-color: #428bca;
    background: linear-gradient(to bottom, #428bca 0, #3071a9 100%);
    border-radius: 3px;
}
.irs--big .irs-grid-pol {
    background-color: #428bca;
}
.irs--big .irs-grid-text {
    color: #428bca;
}
.irs--modern {
    height: 55px;
}
.irs--modern.irs-with-grid {
    height: 55px;
}
.irs--modern .irs-line {
    top: 25px;
    height: 5px;
    background-color: #d1d6e0;
    background: linear-gradient(to bottom, #e0e4ea 0, #d1d6e0 100%);
    border: 1px solid #a3adc1;
    border-bottom-width: 0;
    border-radius: 5px;
}
.irs--modern .irs-bar {
    top: 25px;
    height: 5px;
    background: #20b426;
    background: linear-gradient(to bottom, #20b426 0, #18891d 100%);
}
.irs--modern .irs-bar--single {
    border-radius: 5px 0 0 5px;
}
.irs--modern .irs-shadow {
    height: 1px;
    bottom: 21px;
    background-color: rgba(209, 214, 224, 0.5);
}
.irs--modern .irs-handle {
    top: 37px;
    width: 12px;
    height: 13px;
    border: 1px solid #a3adc1;
    border-top-width: 0;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 3px 3px;
}
.irs--modern .irs-handle > i:nth-child(1) {
    position: absolute;
    display: block;
    top: -4px;
    inset-inline-start: 1px;
    width: 6px;
    height: 6px;
    border: 1px solid #a3adc1;
    background: #fff;
    transform: rotate(45deg);
}
.irs--modern .irs-handle > i:nth-child(2) {
    position: absolute;
    display: block;
    box-sizing: border-box;
    top: 0;
    inset-inline-start: 0;
    width: 10px;
    height: 12px;
    background: #e9e6e6;
    background: linear-gradient(to bottom, #fff 0, #e9e6e6 100%);
    border-radius: 0 0 3px 3px;
}
.irs--modern .irs-handle > i:nth-child(3) {
    position: absolute;
    display: block;
    box-sizing: border-box;
    top: 3px;
    inset-inline-start: 3px;
    width: 4px;
    height: 5px;
    border-inline-start: 1px solid #a3adc1;
    border-inline-end: 1px solid #a3adc1;
}
.irs--modern .irs-handle.state_hover,
.irs--modern .irs-handle:hover {
    border-color: #7685a2;
    background: #c3c7cd;
    background: linear-gradient(to bottom, #fff 0, #919ba5 30%, #fff 100%);
}
.irs--modern .irs-handle.state_hover > i:nth-child(1),
.irs--modern .irs-handle:hover > i:nth-child(1) {
    border-color: #7685a2;
}
.irs--modern .irs-handle.state_hover > i:nth-child(3),
.irs--modern .irs-handle:hover > i:nth-child(3) {
    border-color: #48536a;
}
.irs--modern .irs-max,
.irs--modern .irs-min {
    top: 0;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    color: #fff;
    background-color: #d1d6e0;
    border-radius: 5px;
}
.irs--modern .irs-from,
.irs--modern .irs-single,
.irs--modern .irs-to {
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background-color: #20b426;
    color: #fff;
    border-radius: 5px;
}
.irs--modern .irs-from:before,
.irs--modern .irs-single:before,
.irs--modern .irs-to:before {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    inset-inline-start: 50%;
    width: 0;
    height: 0;
    margin-inline-start: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #20b426;
}
.irs--modern .irs-grid {
    height: 25px;
}
.irs--modern .irs-grid-pol {
    background-color: #dedede;
}
.irs--modern .irs-grid-text {
    color: silver;
    font-size: 13px;
}
.irs--sharp {
    height: 50px;
    font-size: 12px;
    line-height: 1;
}
.irs--sharp.irs-with-grid {
    height: 57px;
}
.irs--sharp .irs-line {
    top: 30px;
    height: 2px;
    background-color: #000;
    border-radius: 2px;
}
.irs--sharp .irs-bar {
    top: 30px;
    height: 2px;
    background-color: #ee22fa;
}
.irs--sharp .irs-bar--single {
    border-radius: 2px 0 0 2px;
}
.irs--sharp .irs-shadow {
    height: 1px;
    bottom: 21px;
    background-color: rgba(0, 0, 0, 0.5);
}
.irs--sharp .irs-handle {
    top: 25px;
    width: 10px;
    height: 10px;
    background-color: #a804b2;
}
.irs--sharp .irs-handle > i:first-child {
    position: absolute;
    display: block;
    top: 100%;
    inset-inline-start: 0;
    width: 0;
    height: 0;
    border: 5px solid transparent;
    border-top-color: #a804b2;
}
.irs--sharp .irs-handle.state_hover,
.irs--sharp .irs-handle:hover {
    background-color: #000;
}
.irs--sharp .irs-handle.state_hover > i:first-child,
.irs--sharp .irs-handle:hover > i:first-child {
    border-top-color: #000;
}
.irs--sharp .irs-max,
.irs--sharp .irs-min {
    color: #fff;
    font-size: 14px;
    line-height: 1;
    top: 0;
    padding: 3px 4px;
    opacity: 0.4;
    background-color: #a804b2;
    border-radius: 2px;
}
.irs--sharp .irs-from,
.irs--sharp .irs-single,
.irs--sharp .irs-to {
    font-size: 14px;
    line-height: 1;
    text-shadow: none;
    padding: 3px 4px;
    background-color: #a804b2;
    color: #fff;
    border-radius: 2px;
}
.irs--sharp .irs-from:before,
.irs--sharp .irs-single:before,
.irs--sharp .irs-to:before {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    inset-inline-start: 50%;
    width: 0;
    height: 0;
    margin-inline-start: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #a804b2;
}
.irs--sharp .irs-grid {
    height: 25px;
}
.irs--sharp .irs-grid-pol {
    background-color: #dedede;
}
.irs--sharp .irs-grid-text {
    color: silver;
    font-size: 13px;
}
.irs--round {
    height: 50px;
}
.irs--round.irs-with-grid {
    height: 65px;
}
.irs--round .irs-line {
    top: 36px;
    height: 4px;
    background-color: #dee4ec;
    border-radius: 4px;
}
.irs--round .irs-bar {
    top: 36px;
    height: 4px;
    background-color: #006cfa;
}
.irs--round .irs-bar--single {
    border-radius: 4px 0 0 4px;
}
.irs--round .irs-shadow {
    height: 4px;
    bottom: 21px;
    background-color: rgba(222, 228, 236, 0.5);
}
.irs--round .irs-handle {
    top: 26px;
    width: 24px;
    height: 24px;
    border: 4px solid #006cfa;
    background-color: #fff;
    border-radius: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 255, 0.3);
}
.irs--round .irs-handle.state_hover,
.irs--round .irs-handle:hover {
    background-color: #f0f6ff;
}
.irs--round .irs-max,
.irs--round .irs-min {
    color: #333;
    font-size: 14px;
    line-height: 1;
    top: 0;
    padding: 3px 5px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}
.irs--round .irs-from,
.irs--round .irs-single,
.irs--round .irs-to {
    font-size: 14px;
    line-height: 1;
    text-shadow: none;
    padding: 3px 5px;
    background-color: #006cfa;
    color: #fff;
    border-radius: 4px;
}
.irs--round .irs-from:before,
.irs--round .irs-single:before,
.irs--round .irs-to:before {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    inset-inline-start: 50%;
    width: 0;
    height: 0;
    margin-inline-start: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #006cfa;
}
.irs--round .irs-grid {
    height: 25px;
}
.irs--round .irs-grid-pol {
    background-color: #dedede;
}
.irs--round .irs-grid-text {
    color: silver;
    font-size: 13px;
}
.irs--square {
    height: 50px;
}
.irs--square.irs-with-grid {
    height: 60px;
}
.irs--square .irs-line {
    top: 31px;
    height: 4px;
    background-color: #dedede;
}
.irs--square .irs-bar {
    top: 31px;
    height: 4px;
    background-color: #000;
}
.irs--square .irs-shadow {
    height: 2px;
    bottom: 21px;
    background-color: #dedede;
}
.irs--square .irs-handle {
    top: 25px;
    width: 16px;
    height: 16px;
    border: 3px solid #000;
    background-color: #fff;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
.irs--square .irs-handle.state_hover,
.irs--square .irs-handle:hover {
    background-color: #f0f6ff;
}
.irs--square .irs-max,
.irs--square .irs-min {
    color: #333;
    font-size: 14px;
    line-height: 1;
    top: 0;
    padding: 3px 5px;
    background-color: rgba(0, 0, 0, 0.1);
}
.irs--square .irs-from,
.irs--square .irs-single,
.irs--square .irs-to {
    font-size: 14px;
    line-height: 1;
    text-shadow: none;
    padding: 3px 5px;
    background-color: #000;
    color: #fff;
}
.irs--square .irs-grid {
    height: 25px;
}
.irs--square .irs-grid-pol {
    background-color: #dedede;
}
.irs--square .irs-grid-text {
    color: silver;
    font-size: 11px;
}
thead.sticky-header-original-thead tr td,
thead.sticky-header-original-thead tr th {
    border-top: none;
    border-bottom: none;
    padding-top: 0;
    padding-bottom: 0;
}
.sticky-header-original-th-inner-wrapper {
    height: 0;
    line-height: 0;
    visibility: hidden;
}
.sticky-header-cloned-wrapper {
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    z-index: 2;
    overflow-x: hidden;
}
.white-space-nowrap {
    white-space: nowrap !important;
}
.nav--tabs,
.table thead th {
    text-transform: capitalize !important;
}
.min-w-140 {
    min-width: 140px;
}
.min-w-120 {
    min-width: 120px;
}
.table-align-middle thead th {
    vertical-align: middle !important;
}
.rating {
    color: #00c9db !important;
}
