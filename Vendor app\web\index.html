<!DOCTYPE html>
<html>
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A multi-vendor multiple e-commerce web app.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="eFood Multivendor">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>6amMart Vendor</title>
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
  <!-- This script installs service_worker.js to provide PWA functionality to
       application. For more information, see:
       https://developers.google.com/web/fundamentals/primers/service-workers -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('flutter-first-frame', function () {
        navigator.serviceWorker.register('flutter_service_worker.js');
        navigator.serviceWorker.register("/firebase-messaging-sw.js");
      });
    }
  </script>

  <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-messaging.js"></script>

  <div class="center">
    <img src="logo.png" alt = "logo" height="250" width="250px" />
    <br>
    <div class="loader" style="width:250px;text-align: center;"><div class="classic-10"></div></div>
  </div>


  <script>
    var firebaseConfig = {
      apiKey: "AIzaSyDFN-73p8zKVZbA0i5DtO215XzAb-xuGSE",
      authDomain: "ammart-8885e.firebaseapp.com",
      projectId: "ammart-8885e",
      storageBucket: "ammart-8885e.appspot.com",
      messagingSenderId: "1000163153346",
      appId: "1:1000163153346:web:4f702a4b5adbd5c906b25b",
      measurementId: "G-L1GNL2YV61"
    };
    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>

  <script src="main.dart.js" type="application/javascript"></script>
</body>
</html>
