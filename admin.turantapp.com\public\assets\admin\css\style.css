@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600;700&display=swap");
:root {
    /* --primary-clr: #24bac3; */
    --primary-clr: #107980;
    --secondary-clr: #99a7ba;
    --primary: #006161;
    --title-clr: #334257;
    --dark-clr: #005555;
    --base-clr: #2aa26a;
    --base-clr-2: #5affba;
    --border-clr: #99a7ba45;
    --warning-clr: #ff7500;
    --danger-clr: #ff6d6d;
    --success-clr: #00aa6d;
    --info-clr: #0096ff;
    --theameColor: #0661CB;

    /*new color add*/
    --cus-warning-light: #F0AF34;
    --cus-warning-clr: #CC952C;

}

.scroll-bar {
    max-height: calc(100vh - 100px);
    overflow-y: auto !important;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 1px #cfcfcf;
    /*border-radius: 5px;*/
}

::-webkit-scrollbar {
    width: 3px !important;
    height: 3px !important;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    /*border-radius: 5px;*/
}

::-webkit-scrollbar-thumb:hover {
    background: #003638;
}

@media only screen and (max-width: 768px) {
    /* For mobile phones: */
    .map-warper {
        height: 250px;
        padding-bottom: 10px;
    }
}
.deco-none {
    color: inherit;
    text-decoration: inherit;
}

.qcont {
    text-transform: lowercase;
}

.qcont:first-letter {
    text-transform: capitalize;
}

.navbar-vertical .nav-link {
    color: #e5e5e5;
}

.navbar .nav-link .text-truncate {
    transition: all ease 0.3s;
}

.navbar .nav-link:hover .text-truncate {
    color: #5affba;
}

.navbar .active > .nav-link,
.navbar .nav-link.active,
.navbar .nav-link.show,
.navbar .show > .nav-link {
    color: #5affba;
}

.navbar-vertical .active .nav-indicator-icon,
.navbar-vertical .nav-link:hover .nav-indicator-icon,
.navbar-vertical .show > .nav-link > .nav-indicator-icon {
    color: #5affba;
}

.nav-subtitle {
    display: block;
    color: #6d8c7e;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03125rem;
}

.navbar-vertical .navbar-nav.nav-tabs .active .nav-link,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link {
    border-inline-start-color: #5affba;
}

.cursor-pointer {
    cursor: pointer;
}

.z-2 {
    z-index: 2;
}
.z-999{
    z-index: 999;
}

.floating-menu {
    border-radius: 100px;
    z-index: 999;
    padding-top: 10px;
    padding-bottom: 10px;
    inset-inline-end: 0;
    position: fixed;
    display: inline-block;
    top: 50%;
    transform: translateY(-50%);
}

.main-menu {
    margin: 0;
    padding-inline-start: 0;
    list-style: none;
}

.main-menu li a:hover {
    background: rgba(244, 244, 244, 0.3);
}

.menu-bg {
    background-color: #005555;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50px;
    z-index: -1;
    top: 0;
    inset-inline-start: 0;
    transition: 0.1s;
}

.ripple {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.ripple:after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    inset-inline-start: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #000 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 1s;
}

.ripple:active:after {
    transform: scale(0, 0);
    opacity: 0.2;
    transition: 0s;
}

.navbar-brand img {
    height: 48px !important;
    width: 100% !important;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: left center;
    object-position: left center;
    border: none !important;
    border-radius: 0 !important;
}

.navbar-brand-wrapper {
    overflow: hidden;
}

.navbar-vertical-aside-mini-mode .navbar-brand {
    display: none;
}

.navbar-dropdown-account-wrapper:focus,
.navbar-dropdown-account-wrapper:hover {
    background: transparent;
}

.cmn--media {
    font-size: 12px;
}

.cmn--media .card-title {
    font-size: 14px;
    font-weight: 700;
    color: #005555;
}

.right-dropdown-icon {
    position: relative;
}

.right-dropdown-icon::before {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    border: 4px solid #005555;
    border-top-color: transparent;
    border-inline-start-color: transparent;
    transform: rotate(45deg);
    inset-inline-start: -17px;
    top: 2px;
}

.navbar-vertical-aside-mini-mode .navbar-brand-wrapper {
    justify-content: center !important;
}

.navbar-vertical-aside-mini-mode aside .navbar-brand {
    display: none !important;
}

/*Design Panel Updated CSS*/
.navbar__brand-wrapper {
    padding: 5px 20px !important;
}

.cmn--media {
    font-size: 12px;
}

.cmn--media .card-title {
    font-size: 14px;
    font-weight: 700;
    color: #005555;
}

.right-dropdown-icon {
    position: relative;
}

.right-dropdown-icon::before {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    border: 4px solid #005555;
    border-top-color: transparent;
    border-inline-start-color: transparent;
    transform: rotate(45deg);
    inset-inline-start: -17px;
    top: 2px;
}

.navbar-vertical-aside-mini-mode .navbar-brand-wrapper {
    justify-content: center !important;
}

.navbar-vertical-aside-mini-mode aside .navbar-brand {
    display: none !important;
}

.d--xl-none {
    display: none !important;
}

@media screen and (max-width: 1199.99px) {
    .d--xl-none {
        display: block !important;
    }
}
.page-header-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    margin: 0;
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .page-header-title {
        font-size: 18px;
    }
}
.page-header-title i {
    margin-inline-end: 10px;
    font-size: 26px;
    line-height: 1.2;
}

.shadow--card {
    background: #ffffff;
    border: 1px solid #f4f4f4;
    box-shadow: 0px 5px 5px rgba(51, 66, 87, 0.05);
    border-radius: 7px;
    padding: 20px;
}

a[class*="btn--"],
button[class*="btn--"] {
    border-radius: 5px;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    padding: 9px 24px;
    color: #fff;
    text-transform: capitalize;
    border: 1px solid;
}

@media screen and (min-width: 1450px) {
    a[class*="btn--"]:not(.btn-sm),
    button[class*="btn--"]:not(.btn-sm) {
        padding: 12px 24px;
    }
}
a[class*="btn--"].font--sm,
button[class*="btn--"].font--sm {
    font-size: 0.75rem;
}

a[class*="btn--"]:hover,
button[class*="btn--"]:hover {
    filter: brightness(0.9);
}

a[class*="btn--"]:hover {
    color: #fff;
}

.btn--secondary {
    color: #fff !important;
    background: var(--secondary-clr);
    border-color: var(--secondary-clr) !important;
}

.btn--reset {
    color: var(--title-clr) !important;
    background: #f3f4f5 !important;
    border-color: #f3f4f5 !important;
}

.h--37px {
    height: 37px !important;
    line-height: 37px;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.btn--primary {
    box-shadow: none !important;
    background: var(--primary-clr);
    border-color: var(--primary-clr) !important;
}

.btn--primary:hover {
    color: #fff !important;
}

.btn-outline-primary:not(:hover) {
    background: transparent !important;
    color: var(--primary-clr);
}

.btn-outline-primary:hover {
    background: var(--primary-clr) !important;
    color: #fff !important;
}

.btn-outline-secondary:not(:hover) {
    background: transparent !important;
}

.btn-outline-secondary:hover {
    background: var(--secondary-clr);
    color: #fff !important;
}

.btn--warning {
    background: var(--warning-clr) !important;
    border-color: var(--warning-clr) !important;
}

.btn-outline-warning:not(:hover) {
    background: rgba(255, 134, 32, 0.1) !important;
    color: var(--warning-clr) !important;
}

.btn-outline-warning:hover {
    background: var(--warning-clr) !important;
    color: #fff !important;
}

.btn--danger {
    background: var(--danger-clr) !important;
    border-color: var(--danger-clr) !important;
}

.btn-outline-danger:not(:hover) {
    background: transparent !important;
    color: var(--danger-clr);
}

.btn-outline-danger:hover {
    background: var(--danger-clr) !important;
    color: #fff !important;
}

.btn--varify {
    --success-clr: #77c668;
    box-shadow: none !important;
    background: var(--success-clr);
    border-color: var(--success-clr) !important;
}

.btn-outline-varify:not(:hover) {
    --success-clr: #77c668;
    background: transparent !important;
    color: var(--success-clr);
}

.btn-outline-varify:hover {
    --success-clr: #77c668;
    background: var(--success-clr);
    color: #fff !important;
    stroke: #fff !important;
}

.btn--primary-light {
    background: rgba(16, 121, 128, 0.07) !important;
    border-color: transparent !important;
    color: var(--primary-clr) !important;
}

.btn--warning-light {
    background: rgba(244, 133, 73, 0.1) !important;
    border-color: transparent !important;
    color: var(--warning-clr) !important;
}

.export-btn {
    display: flex;
    align-items: center;
    height: 35px;
    padding: 0 12px !important;
}

.export-btn span {
    margin: 0 5px;
    display: inline-block;
}

.export-btn i {
    font-size: 0.9rem !important;
}

.action-btn {
    width: 30px;
    height: 30px;
    transition: all ease 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em !important;
    padding: 0 !important;
}

@media screen and (min-width: 1650px) {
    .action-btn {
        width: 32px;
        height: 32px;
    }
}
.action-btn.btn-outline-primary:not(:hover) {
    background: rgba(0, 134, 143, 0.0392156863) !important;
}

.btn--container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.table {
    width: 100%;
}

.table .btn--container {
    flex-wrap: nowrap;
}

.table .btn--container {
    gap: 10px;
}

.zone-setup-top {
    font-size: 0.7rem;
    line-height: 1rem;
}

.zone-setup-top .subtitle {
    font-weight: 600;
    color: var(--base-clr);
    font-size: 1rem;
}

@media screen and (min-width: 1450px) {
    .zone-setup-top {
        font-size: 0.75rem;
        line-height: 1.2rem;
    }
}
.zone-setup-item {
    display: flex;
    flex-wrap: wrap;
    max-width: 389px;
    margin-bottom: 10px;
}

.zone-setup-item .zone-setup-icon {
    width: 30px;
    height: 30px;
    border: 1.5px solid var(--title-clr);
    color: var(--title-clr);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.95rem;
}

.zone-setup-item .info {
    width: calc(100% - 30px);
    padding-inline-start: 8px;
    align-self: center;
    font-size: 0.7rem;
    line-height: 15px;
}

@media screen and (min-width: 1650px) {
    .zone-setup-item {
        min-width: 420px;
    }
    .zone-setup-item .zone-setup-icon {
        width: 35px;
        height: 35px;
        font-size: 0.95rem;
    }
    .zone-setup-item .info {
        width: calc(100% - 45px);
        font-size: 0.76rem;
        line-height: 1.2rem;
    }
}
.zone-setup .input-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--title-clr);
}

.zone-setup .form-group {
    margin-bottom: 25px;
}

.instructions-image img {
    width: 100%;
    max-width: 389px;
}

@media screen and (min-width: 1450px) {
    .instructions-image img {
        width: unset;
        max-width: 100%;
    }
}
@media screen and (max-width: 767px) {
    .instructions-image {
        margin-bottom: 40px;
    }
}
.map-warper {
    height: 233px;
    overflow: hidden;
}

@media screen and (min-width: 1450px) {
    .map-warper {
        height: 306px;
        margin-top: 32px;
    }
}
@media screen and (min-width: 1650px) {
    .map-warper {
        height: 350px;
    }
}
.map-warper #map-canvas {
    height: 100%;
    margin: 0px;
    padding: 0px;
    position: relative;
    overflow: hidden;
}

.map-warper #pac-input {
    height: 3em;
    width: -moz-fit-content;
    width: fit-content;
}

.card .card-header h5 {
    font-size: 16px;
    font-weight: 600;
}

.table th {
    padding: 1rem;
}

.input--group .form-control {
    height: 40px;
    padding: 0 10px;
    font-size: 0.875rem;
    border-top: 0.530587px solid var(--border-clr);
    border-bottom: 0.530587px solid var(--border-clr);
}

.input--group .form-control:first-child {
    border-inline-start: 0.530587px solid var(--border-clr);
    border-radius: 5px 0 0 5px !important;
}

.input--group .form-control:last-child {
    border-inline-end: 0.530587px solid var(--border-clr);
    border-radius: 0 5px 5px 0 !important;
}

.input--group .btn--primary,
.input--group .btn--secondary {
    min-width: 40px;
    text-align: center;
    color: #fff;
    padding: 0 5px !important;
    border-radius: 0 5px 5px 0 !important;
    border-top: 1px solid var(--border-clr) !important;
    border-bottom: 1px solid var(--border-clr) !important;
    border-inline-end: 1px solid var(--border-clr) !important;
    border-inline-start: none !important;
    margin-inline-start: -1px !important;
}

@media screen and (min-width: 1200px) {
    .input--group .export-btn,
    .input--group .form-control,
    .input--group .btn--primary,
    .input--group .btn--secondary {
        height: 40px;
    }
}
@media screen and (min-width: 1200px) {
    .export-btn {
        height: 40px;
    }
}
.move-left {
    margin-inline-start: -22px;
}

.nav-subtitle {
    color: #99a7ba !important;
    font-size: 0.75rem;
}

.navbar-vertical aside .nav-link {
    color: #e9f3ff !important;
}

.form--control {
    background: rgba(162, 200, 200, 0.1);
    border: 1px solid #a2c8c8;
    border-radius: 5px;
    color: #99a7ba !important;
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.1098039216);
}

.form--control::-moz-placeholder {
    color: #a2c8c8;
}

.form--control::placeholder {
    color: #a2c8c8;
}

.form--control:focus {
    color: #fff !important;
    border-color: var(--base-clr);
    background: rgba(153, 167, 186, 0.2);
}

.form--control:-webkit-autofill,
.form--control:-webkit-autofill:hover,
.form--control:-webkit-autofill:focus {
    border: 1px solid var(--base-clr) !important;
    -webkit-text-fill-color: #fff !important;
    box-shadow: 0 0 0px 1000px rgba(153, 167, 186, 0.2) inset !important;
    -webkit-box-shadow: 0 0 0px 1000px rgba(153, 167, 186, 0.2) inset !important;
    -webkit-transition: background-color 5000s ease-in-out 0s !important;
    transition: background-color 5000s ease-in-out 0s !important;
}

.sidebar--search-form {
    padding: 20px 10px 16px;
}

.sidebar--search-form .form--control {
    padding-inline-start: 40px;
}

.sidebar--search-form .search--form-group {
    position: relative;
    transition: all ease 0.3s;
}

.sidebar--search-form .search--form-group .btn {
    position: absolute;
    inset-inline-start: 5px;
    top: 3px;
    color: #99a7ba !important;
    padding-inline-start: 12px !important;
    padding-inline-end: 12px !important;
}

aside .nav-item .nav-link {
    color: #e9f3ff !important;
}

aside .navbar-vertical-aside-has-menu .nav-sub .nav-link {
    font-size: 0.8rem;
}

aside .navbar-nav > .nav-item:not(:first-child) {
    margin-top: 15px !important;
}

aside .nav-link {
    color: #e9f3ff !important;
}

.navbar-vertical-aside-mini-mode
    .navbar-vertical:not([class*="container"])
    .navbar-nav
    .nav-link {
    padding-inline-start: 1.75rem !important;
}

.navbar-vertical-aside-mini-mode .search--form-group {
    display: none;
}

.swal2-popup .swal2-styled.swal2-cancel {
    color: #fff !important;
    background: var(--secondary-clr) !important;
}

.swal2-popup .swal2-styled:focus,
.swal2-popup .swal2-styled.swal2-confirm {
    box-shadow: none !important;
}

.swal2-popup {
    border-radius: 10px !important;
    padding: 30px !important;
    width: 100% !important;
    max-width: 500px !important;
    font-size: 1em !important;
    font-family: "Inter", sans-serif;
}

.swal2-popup .swal2-icon {
    margin-top: 0 !important;
    margin-bottom: 1.5em !important;
}

.swal2-popup .swal2-styled {
    min-width: 170px;
    text-align: center;
}

@media screen and (min-width: 575px) {
    .swal2-popup .swal2-styled {
        margin: 0.3125em 0.8em !important;
    }
}
.swal2-popup .swal2-title {
    font-family: "Public Sans", sans-serif;
    font-size: 1.5em !important;
}

.swal2-popup .swal2-actions {
    margin-top: 1.5em !important;
}

.swal2-popup .swal2-close {
    color: var(--title-clr) !important;
    inset-inline-end: 15px !important;
    top: 15px !important;
}

#map {
    /* height: 272px !important; */
    height: 272px;
    border-radius: 5px;
}

.card-header-icon i {
    font-size: 19px;
}

.btn {
    font-family: "Inter", sans-serif;
}

.card-title {
    display: flex;
    text-transform: capitalize;
}

.card-title i:first-child {
    margin-inline-start: 4px;
}

.h--45px {
    height: 45px !important;
}

.h--40px {
    height: 40px !important;
}

.btn.h--40px {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn--primary.h--45px {
    font-size: 16px;
    font-weight: 600;
}

.page-header-select-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -7px;
    justify-content: flex-end;
}

.page-header-select-wrapper .select-item {
    padding: 7px;
    flex-grow: 1;
}

@media screen and (min-width: 576px) {
    .page-header-select-wrapper .select-item {
        max-width: 211px;
    }
    .page-header-select-wrapper .select-item .form-control {
        height: 46px;
    }
    .page-header-select-wrapper .select-item .select2-selection {
        height: 46px !important;
        line-height: 28px;
    }
    .page-header-select-wrapper .select-item .w--unset {
        width: unset !important;
    }
}
@media screen and (max-width: 575px) {
    .page-header-select-wrapper {
        margin-top: 10px;
    }
}
@media screen and (min-width: 576px) {
    .w--sm-unset {
        width: unset !important;
    }
}
.resturant-card {
    position: relative;
    border-radius: 10px;
    padding: 21px 50px 21px 28px;
}

.resturant-card .title {
    font-size: 1.375rem;
    font-weight: 700;
    margin: 0;
    margin-bottom: 5px;
    color: #111;
}

.resturant-card .subtitle {
    margin: 0;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    color: #111;
}

@media screen and (max-width: 1400px) and (min-width: 1200px) {
    .resturant-card .subtitle {
        font-size: 12px;
    }
}
.resturant-card .resturant-icon {
    position: absolute;
    inset-inline-end: 15px;
    top: 15px;
    -o-object-fit: contain;
    object-fit: contain;
    height: 40px;
    max-width: 40px;
}

.resturant-card .resturant-icon img {
    max-width: 100%;
}

@media screen and (min-width: 1450px) {
    .resturant-card {
        padding: 30px 55px 25px 30px;
    }
    .resturant-card .resturant-icon {
        inset-inline-end: 20px;
        top: 25px;
    }
}
.resturant-card.dashboard--card {
    min-height: 135px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.resturant-card.dashboard--card .subtitle {
    color: #111;
}

.bg--1 {
    background: rgba(0, 150, 255, 0.15) !important;
}

.bg--2 {
    background: rgba(0, 170, 109, 0.15) !important;
}

.bg--3 {
    background: rgba(248, 59, 59, 0.1) !important;
}

.bg--4 {
    background: rgba(255, 245, 0, 0.2) !important;
}

.bg--5 {
    background: rgba(248, 146, 59, 0.1) !important;
}

.bg--6 {
    background: rgba(248, 59, 218, 0.11) !important;
}

.bg--7 {
    background: rgba(255, 46, 83, 0.15) !important;
}

.bg--8 {
    background: rgba(0, 170, 17, 0.15) !important;
}

.bg--9 {
    background: rgba(59, 191, 248, 0.1) !important;
}

.bg--10 {
    background: rgba(0, 170, 109, 0.15) !important;
}

.bg--11 {
    background: rgba(248, 146, 59, 0.1) !important;
}

.bg--12 {
    background: rgba(247, 25, 25, 0.11) !important;
}

.bg--13 {
    background: #cceaff !important;
}

.bg--14 {
    background: rgba(255, 0, 0, 0.2) !important;
}

.bg--primary {
    background: var(--primary-clr) !important;
}

.resturant-card-2 .resturant-icon {
    max-width: 24px;
    height: 24px;
}

.bg--title {
    background: var(--title-clr) !important;
}

.g-1 {
    margin: -0.35rem;
}

.g-1 > *[class*="col"] {
    padding: 0.35rem !important;
}

.g-3 {
    margin: -0.625rem;
}

.g-3 > *[class*="col"] {
    padding: 0.625rem !important;
}

.g-4 {
    margin: -0.875rem;
}

.g-4 > *[class*="col"] {
    padding: 0.875rem !important;
}

@media screen and (1200px <= width < 1201px) {
    .navbar-vertical-aside-show-xl .navbar:not(.navbar-vertical) {
        margin-inline-start: 0 !important;
    }
}
@media screen and (max-width: 575px) {
    .cmn--media.right-dropdown-icon::before {
        display: none;
    }
    .cmn--media.right-dropdown-icon .media-body {
        display: none !important;
    }
}
.nav-tabs .nav-link {
    border: none;
}

.nav-icon {
    opacity: 1 !important;
}

.navbar-vertical .navbar-nav.nav-tabs .active > .nav-link,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link {
    position: relative;
    min-height: 36px;
    padding-inline-start: 28px;
    padding-inline-end: 22px;
}

.navbar-vertical .navbar-nav.nav-tabs .active > .nav-link .text-truncate,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link .text-truncate {
    color: var(--base-clr-2) !important;
    opacity: 1;
}

.navbar-vertical .navbar-nav.nav-tabs .active > .nav-link i,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link i {
    color: #5affba;
}

.navbar-vertical .navbar-nav.nav-tabs .active > .nav-link::before,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link::before {
    content: "";
    position: absolute;
    inset: 0 10px;
    background: rgba(239, 246, 255, 0.1);
    border-radius: 5px;
}

.navbar-vertical
    .navbar-nav.nav-tabs
    .navbar-vertical-aside-has-menu
    li.active
    > .nav-link {
    padding-inline-end: 28px;
    padding-inline-start: 52px;
}

.statistics--title-area {
    padding: 14px 0 11px;
}

.navbar-vertical:not([class*="container"]) .navbar-nav.navbar-nav-lg .nav-link {
    margin-top: 0;
    margin-bottom: 0;
}

.order--card {
    background: rgba(110, 137, 175, 0.0509803922);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 18px 15px 15px 20px;
    min-height: 65px;
}

.order--card .card-title {
    font-weight: 700;
    font-size: 18px;
    line-height: 25px;
    margin-inline-start: 10px;
}

.order--card .card-subtitle {
    color: var(--title-clr);
    text-transform: capitalize;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
}

.order--card .card-subtitle img {
    width: 25px;
    height: 25px;
    -o-object-fit: contain;
    object-fit: contain;
    margin-inline-end: 10px;
}

.fz--11 {
    font-size: 12px !important;
    font-weight: 700;
    line-height: 13px;
}
.fz-11{
    font-size: 11px !important;
}

.fz--11 .legend-indicator {
    width: 12px;
    height: 12px;
}

@media screen and (min-width: 1200px) {
    .mt--xl--10 {
        margin-top: -10px;
    }
}
.chart--extension {
    transform: translateY(-40px) rotate(-90deg);
    margin-inline-start: -30px;
    margin-inline-end: 0px;
    font-weight: 700;
    font-size: 11px;
    font-family: "Poppins", sans-serif;
    color: var(--title-clr);
}

.chart--label {
    display: flex;
    padding: 0 15px 10px;
}

.chart--label .indicator {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: inline-block;
}

#fund-statistics-board .chart--label .indicator {
    width: 10px;
    height: 10px;
    margin-top: 3px;
}

.chart--label .info {
    width: calc(100% - 15px);
    padding-inline-start: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    align-self: center;
}

#fund-statistics-board .chart--label .info {
    font-size: 10px;
    white-space: nowrap;
}

.card-header-title img.card-header-icon {
    max-width: 22px;
    max-height: 22px;
    -o-object-fit: contain;
    object-fit: contain;
    margin-inline-end: 4px;
}

.card {
    border-color: rgba(231, 234, 243, 0.5019607843);
}

.total--users {
    position: absolute;
    inset-inline-start: 50%;
    top: 51%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    font-family: "Poppins", sans-serif;
    text-align: center;
}

.total--users h3 {
    margin: 0;
    font-weight: 700 !important;
    font-size: 22px;
    line-height: 27px;
}

@media screen and (max-width: 575px) {
    .total--users span {
        font-size: 9px;
        display: block;
        line-height: 9px;
        margin-bottom: -5px;
    }
    .total--users h3 {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 5px;
    }
}
.most-popular {
    padding: 0;
    margin: 0;
}

.most-popular li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    list-style: none;
}

.most-popular li:not(:last-child) {
    margin-bottom: 14.74px;
}

.most-popular li .img-container {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 13.75px;
    line-height: 17px;
    color: var(--title-clr);
}

.most-popular li .img-container img {
    width: 41.28px;
    height: 41.28px;
    border-radius: 6px;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid rgba(0, 134, 143, 0.3);
    box-shadow: 0px 3.9297px 3.9297px -1.96485px rgba(0, 0, 0, 0.1);
}

.most-popular li .count {
    color: #ff6d6d;
    font-weight: 700;
    font-size: 18px;
    line-height: 25px;
}

.top--deliveryman {
    min-height: 131px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
    border: none !important;
    background: #ffffff;
    box-shadow: 0px 4px 4px rgba(51, 66, 87, 0.05) !important;
    border-radius: 5px;
    height: 100%;
}

.top--deliveryman img {
    border: 1px solid rgba(240, 111, 0, 0.3);
}

.top--deliveryman .name {
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    /* identical to box height */
    text-align: center;
    letter-spacing: -0.02em;
    color: #005555;
}

.top--deliveryman .info {
    font-weight: 500;
    font-size: 10px;
    line-height: 15px;
    /* identical to box height */
    text-align: center;
    letter-spacing: -0.02em;
    color: #005555;
}

.top--deliveryman .info span {
    font-size: 16px;
}

.top--rated-food {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
    border: none !important;
    background: #ffffff;
    box-shadow: 0px 4px 4px rgba(51, 66, 87, 0.05) !important;
    border-radius: 5px;
    height: 100%;
    min-height: 140px;
}

.top--rated-food .rating {
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
    color: #f06f00;
    display: flex;
    align-items: center;
    justify-content: center;
}

.top--rated-food .rating span,
.top--rated-food .rating i {
    color: #f06f00;
}

.top--rated-food .rating .text--title {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    letter-spacing: -0.02em;
    color: #005555;
}

.text-title {
    color: #334257 !important;
}

.top--rated-food .rating .text-warning {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-inline-end: 5px;
}

.top--rated-food .name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.top--rated-food img {
    width: 90px;
    height: 78px;
    -o-object-fit: cover;
    object-fit: cover;
}

@media screen and (min-width: 1200px) and (max-width: 1399px) {
    .top--rated-food img {
        height: 86px;
    }
}
.top--resturant {
    margin: -6px -10px;
    display: flex;
    flex-wrap: wrap;
    padding: 0;
}

.top--resturant li {
    padding: 6px 10px;
    width: 100%;
    list-style: none;
}

@media screen and (min-width: 500px) {
    .top--resturant li {
        width: 50%;
    }
}
.top--resturant li .top--resturant-item {
    border: none !important;
    background: #ffffff;
    box-shadow: 0px 4px 4px rgba(51, 66, 87, 0.05) !important;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 10px 9px;
}

.top--resturant li .top--resturant-item img {
    width: 65px;
    height: 65px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
    border: 1px solid rgba(240, 111, 0, 0.3);
}

.top--resturant li .top--resturant-item .top--resturant-item-content {
    width: calc(100% - 65px);
    padding-inline-start: 10px;
}

.top--resturant li .top--resturant-item .top--resturant-item-content .name {
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.02em;
    color: #005555;
}

.top--resturant
    li
    .top--resturant-item
    .top--resturant-item-content
    .info
    span {
    font-size: 14px;
}

.sold--count-badge {
    position: absolute;
    inset-inline-end: 7px;
    inset-inline-start: 7px;
    bottom: 5px;
    text-align: center;
    background: var(--title-clr);
    color: #fff;
    border-radius: 5px;
    padding: 3px 5px;
    font-size: 12px;
}

.top-selling-food-card {
    cursor: pointer;
    padding-inline-end: 6px;
    padding-inline-start: 6px;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    text-align: center;
    color: #005555;
    border: none !important;
    background: #ffffff;
    box-shadow: 0px 4px 4px rgba(51, 66, 87, 0.05) !important;
    border-radius: 5px;
}

.top-selling-food-card img {
    border: 1px solid rgba(240, 111, 0, 0.3);
}

.label_1 {
    inset-inline-start: 30px;
    top: 30px;
}

.grid-card {
    border-radius: 10px;
    padding: 10px;
    height: 100%;
    border: none !important;
    box-shadow: 0 2px 6px rgba(0, 150, 255, 0.1);
}

.grid-card.style-2 .label_1 {
    inset-inline-start: 15px;
    top: 15px;
}

.grid-card .text-center span {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    font-weight: 700;
    color: var(--title-clr);
}

.grid-card .label_1 {
    position: absolute;
    font-size: 10px;
    background: #005555;
    color: #fff;
    min-width: 60px;
    padding: 3px 9px;
    font-weight: bold;
    border-radius: 6px;
}

.grid-card .mt-6 {
    margin-top: 1.55em !important;
}

.lang_form {
    animation: fadeIn 0.5s;
    -webkit-animation: fadeIn 0.5s;
    -moz-animation: fadeIn 0.5s;
}

.lang_link {
    border-bottom: 3px solid transparent !important;
    padding: 0 !important;
    padding-bottom: 10px !important;
}

.lang_link.active {
    border-color: var(--primary-clr) !important;
}

.nav-item:not(:last-child) .lang_link {
    margin-inline-end: 20px;
}

div.form-control,
input.form-control,
select.form-control {
    height: 45px;
}

.custom-select {
    height: 45px;
    line-height: 27px;
}
.custom-select.p {
    padding: 0.54688rem 0.875rem;
}

.custom-radio .custom-control-label {
    padding: 3px 0;
}

.custom-radio .custom-control-label::after,
.custom-radio .custom-control-label::before {
    top: 0.32rem;
}

.form-label {
    color: var(--title-clr);
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .module-logo-card {
        box-shadow: none;
        border: none;
    }
    .module-logo-card .card-body {
        padding: 0;
    }
}
.page-header {
    padding-bottom: 10px;
}

.main .content {
    padding-top: 1.2rem;
}

.page-header .btn--primary {
    margin-bottom: 8px;
}

.page-header-title {
    margin-bottom: 8px;
}

.page-header-title .page-header-icon img {
    width: 30px;
    height: 30px;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: left center;
    object-position: left center;
}

.page-header-title > span:not(:first-child) {
    margin-inline-start: 10px;
}

@media (max-width: 1199.98px) {
    aside .navbar-nav-wrap-content-left {
        display: none !important;
    }
}
.search--button-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 0px -7px !important;
    flex-grow: 1;
}

.search--button-wrapper > .btn,
.search--button-wrapper > form,
.search--button-wrapper > button,
.search--button-wrapper .card-title,
.search--button-wrapper .hs-unfold,
.search--button-wrapper > div {
    margin: 7px !important;
}

.search--button-wrapper > .card-title {
    margin-inline-end: auto !important;
}

.search--button-wrapper *[class*="btn--"] {
    padding: 10px 20px;
}

@media (min-width: 576px) {
    .search--button-wrapper .search-form {
        min-width: 260px;
    }
}
.search--button-wrapper {
    margin: 0 -4px;
}

@media screen and (max-width: 575px) {
    .search--button-wrapper .hs-unfold {
        flex-grow: 1;
    }
    .search--button-wrapper .hs-unfold .btn {
        justify-content: center;
    }
    .search--button-wrapper .btn--primary {
        margin-inline-end: 0 !important;
        flex-grow: 1;
    }
    .search--button-wrapper > .btn,
    .search--button-wrapper > form,
    .search--button-wrapper > button,
    .search--button-wrapper .card-title,
    .search--button-wrapper .hs-unfold,
    .search--button-wrapper > div {
        margin: 4px !important;
        flex-grow: 1;
    }
}
.js-hs-unfold-invoker {
    display: block;
}

.js-hs-unfold-invoker.dropdown-toggle,
.js-hs-unfold-invoker.btn-icon {
    display: flex !important;
}

.table .thead-light th {
    color: var(--title-clr);
    border-top: none !important;
}

.w--26 {
    width: 26px !important;
}

.w--22 {
    width: 22px !important;
}
.w--11 {
    width: 11px !important;
}

.h--90 {
    height: 90px !important;
}

.img--65,
.img--60,
.img--70,
.img--75,
.img--80,
.img--90,
.img--100,
.img--110,
.img--120,
.img--130,
.img--133,
.img--200 {
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
}

.img--75 {
    width: 75px;
}

.img--65 {
    width: 65px;
}

.img--130 {
    width: 130px;
}

.img--133 {
    width: 133px;
}

.img--120 {
    width: 120px !important;
}

.img--200 {
    width: 200px;
}

.img--110 {
    width: 110px;
}

.img--100 {
    width: 100px;
}

.img--90 {
    width: 90px;
}

.img--80 {
    width: 80px;
}

.img--70 {
    width: 70px;
}

.img--60 {
    width: 60px;
}

.min--200 {
    min-width: 200px;
}

.min--220 {
    min-width: 220px;
}

.min--240 {
    min-width: 240px;
}

.min--250 {
    min-width: 250px;
}

.min--260 {
    min-width: 250px;
}

.min--270 {
    min-width: 250px;
}

.min--280 {
    min-width: 250px;
}

.img--ratio-3 {
    aspect-ratio: 3;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
}

.text--title {
    color: var(--title-clr) !important;
}

img.circle {
    border-radius: 50% !important;
}

.w--120px {
    width: 120px !important;
}

.img--vertical {
    aspect-ratio: 3;
    width: 100%;
    max-width: 400px;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
}

.img--vertical-120 {
    max-width: 360px;
    width: 100%;
    height: 120px;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
}

.toggle-switch {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.85rem;
    text-transform: capitalize;
}

.toggle-switch span.pr-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 0;
    flex-grow: 1;
}

.h--50px {
    height: 50px !important;
}

.form-control ~ .select2 .custom-select:not(.select2-selection) {
    height: 45px;
}

.custom-file,
.custom-file-input {
    height: 45px;
}

.custom-file-input {
    opacity: 1;
    padding: 1px 0 1px 11px;
    padding-inline-start: 11px;
    line-height: 34px;
    border-color: var(--border-clr) !important;
    border-radius: 48px;
}

.custom-file-input:focus,
.custom-file-input:active,
.custom-file-input:hover,
.custom-file-input:focus-within {
    border-color: var(--border-clr) !important;
}

.custom-file-label {
    height: 45px;
    line-height: 27px;
    background: transparent;
    z-index: 9;
}

.custom-file-label::after {
    height: 43px;
    line-height: 25px;
    inset-inline-end: unset;
    inset-inline-start: 2px;
    content: "Choose File" !important;
    background: #fff;
    border: none !important;
    top: 0;
}

.custom-file-input[multiple] ~ .custom-file-label::after {
    width: 113px;
}

.table .badge {
    min-width: 80px;
}

.badge {
    padding: 0.4em 0.5em;
    line-height: 14px;
    font-size: 0.74rem;
}

.action-btn i {
    font-size: 1.14rem;
}

thead th {
    font-family: "Poppins", sans-serif;
}

.mw--85px {
    max-width: 85px !important;
}

.fz--14px {
    font-size: 0.875rem !important;
}

.fz--14px tr td strong {
    font-size: 0.75rem !important;
}

.fz--14px .badge {
    font-weight: 500;
}

textarea.form-control[readonly] {
    min-height: 45px;
}

.form-control[readonly] {
    background: #f8fafd !important;
}

@media screen and (min-width: 1200px) and (max-width: 1650px) {
    .zone-setup-instructions .instructions-image img {
        max-width: 88%;
    }
}
.w--20 {
    width: 20px !important;
}

.customer--name {
    font-weight: 600;
}

.export-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 25px;
    flex-grow: 1;
}

.export-steps.style-2 .export-steps-item {
    width: 33.3333333333%;
    max-width: 100%;
}

@media screen and (max-width: 767px) {
    .export-steps.style-2 .export-steps-item {
        width: 50%;
    }
}
@media screen and (max-width: 575px) {
    .export-steps.style-2 .export-steps-item {
        width: 100%;
    }
}
.export-steps-item {
    color: rgb(138, 146, 166);
    padding: 5px;
    width: 50%;
    max-width: 400px;
}

.export-steps-item h5 {
    color: rgb(138, 146, 166);
    margin: 0;
    margin-bottom: -5px;
    font-size: 18px;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.export-steps-item .inner {
    background: #f7f7f7;
    padding: 15px 15px;
    text-align: center;
    min-height: 57px;
    height: 100%;
}

.export-steps-item .inner p {
    font-size: 14px;
    margin: 0;
    margin-top: 10px;
}

@media screen and (max-width: 575px) {
    .export-steps-item {
        width: 100%;
    }
}
.export--template-btns .btn {
    width: 100%;
    max-width: 274px;
}

.export--template-title {
    letter-spacing: 0.05em;
}

.font-light {
    font-weight: 300 !important;
}

.font-regular {
    font-weight: 400 !important;
}

.font-medium {
    font-weight: 500 !important;
}

.font-bold {
    font-weight: 700 !important;
}

.font-semibold {
    font-weight: 600 !important;
}

.form--control-select {
    width: unset;
    min-width: 100px;
    height: 26px !important;
    padding: 0;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
}

.w--1 {
    width: 10%;
}

.w--05 {
    width: 5%;
}

.w--15 {
    width: 15%;
}

.w--25 {
    width: 25%;
}

.w--2 {
    width: 20%;
}

.w--3 {
    width: 30%;
}

.w--4 {
    width: 40%;
}

.w--5 {
    width: 50%;
}

.w--6 {
    width: 60%;
}

.w--7 {
    width: 70%;
}

.w--8 {
    width: 80%;
}

.w--9 {
    width: 90%;
}

.min-height-45 {
    min-height: 45px !important;
}

.min--height-150 {
    min-height: 150px;
}

.min--height-200 {
    min-height: 200px;
}

.spartan_remove_row {
    inset-inline-end: 3px;
    top: 15px;
    background: transparent;
    border-radius: 3px;
    width: 25px;
    height: 25px;
    line-height: 22px;
    text-align: center;
    text-decoration: none;
    color: rgb(255, 7, 0);
    position: absolute !important;
    right: 15px;
    cursor: pointer;
}

.spartan_remove_row.pen {
    position: absolute !important;
    right: 47px;
    top: 15px;
    background: transparent;
    border-radius: 3px;
    width: 25px;
    height: 25px;
    line-height: 22px;
    text-align: center;
    text-decoration: none;
    color: #4153b3;
    border: 1px solid #4153b3;
}

.spartan_item_wrapper .file_upload img.img_ {
    aspect-ratio: 1;
    -o-object-fit: contain;
    object-fit: contain;
}

.img--square {
    width: 100%;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
}

.food--media .avatar {
    -o-object-fit: cover;
    object-fit: cover;
}

@media screen and (min-width: 992px) and (max-width: 1550px) {
    .review--information-wrapper .food--media {
        flex-wrap: nowrap !important;
    }
    .review--information-wrapper .avatar-xxl {
        width: 110px;
        margin-inline-end: 15px !important;
    }
    .review--information-wrapper .rating--review .title {
        font-size: 36px;
    }
    .review--information-wrapper .rating--review .title .out-of {
        font-size: 28px;
    }
    .review--information-wrapper .rating--review .info {
        font-size: 12px;
        font-weight: 400;
    }
}
@media screen and (max-width: 500px) {
    .review--information-wrapper .food--media {
        flex-wrap: nowrap !important;
    }
    .review--information-wrapper .avatar-xxl {
        width: 80px;
        margin-inline-end: 15px !important;
    }
    .review--information-wrapper .rating--review .title {
        font-size: 30px;
    }
    .review--information-wrapper .rating--review .title .out-of {
        font-size: 24px;
    }
    .review--information-wrapper .rating--review .info {
        font-size: 12px;
        font-weight: 400;
    }
}
.rating--review .title {
    font-weight: 600;
    font-size: 20px;
    letter-spacing: 0.007em;
    color: var(--warning-clr);
    text-align: center;
}

.rating--review .title .out-of {
    font-size: 16px;
    color: #758590;
    font-weight: 500;
    text-align: center;
}

.rating--review .info {
    font-size: 12px;
    line-height: 19px;
    letter-spacing: 0.007em;
    color: #758590;
}

.rating--review i {
    color: var(--primary-clr);
    font-size: 20px;
}

@media screen and (max-width: 575px) {
    .rating--review {
        width: 100%;
        padding: 0 0 20px;
    }
    .rating--review .title {
        font-size: 40px;
        line-height: 40px;
        letter-spacing: 0.007em;
    }
    .rating--review .title .out-of {
        font-size: 28px;
        line-height: 35px;
    }
}
.rating--review-right {
    max-width: 940px;
}

.rating--review-right li:not(:last-child) {
    padding-bottom: 10px;
}

@media screen and (max-width: 1650px) and (min-width: 1350px) {
    .rating--review-right {
        max-width: 740px;
    }
}
.progress-name {
    width: 100px;
    margin-inline-end: 23px;
    color: var(--title-clr);
}

@media screen and (max-width: 767px) {
    .card .card-header .delivery--man-single-name {
        font-size: 14px;
        margin-bottom: 10px !important;
    }
}
.datatable-custom td {
    vertical-align: middle;
}

@media screen and (min-width: 1400px) {
    .food--media .avatar {
        width: 184px;
    }
    .food--media .avatar.w--290 {
        width: 290px;
    }
    .food--media .avatar.h--145 {
        height: 145px;
    }
}
@media screen and (max-width: 991px) and (min-width: 768px) {
    .food--media {
        flex-wrap: nowrap;
    }
}
@media screen and (max-width: 991px) {
    .food--media .avatar {
        width: 120px;
    }
}
@media screen and (max-width: 991px) {
    .food--media > .d-block {
        flex-grow: 1;
    }
}
@media screen and (max-width: 991px) and (min-width: 768px) {
    .food--media > .d-block {
        width: 0;
    }
}
@media screen and (max-width: 767px) {
    .food--media .avatar {
        width: 120px;
        margin-bottom: 20px;
    }
}
.resturant--information-single {
    text-align: center;
}

.resturant--information-single .avatar-img,
.resturant--information-single .avatar {
    width: 70px;
    height: 70px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 5px;
}

.resturant--information-single .avatar-img {
    border: 1px solid #f8923b;
}

.resturant--information-single .media-body {
    text-align: start;
    width: calc(100% - 70px);
    font-size: 12px;
    padding-inline-start: 23px;
}

.table strong {
    color: var(--title-clr);
}

.max-w--220px {
    max-width: 220px;
}

.spartan_item_wrapper {
    width: 50% !important;
    flex: 0 0 50% !important;
    margin-bottom: 0 !important;
    max-width: 130px !important;
}

.spartan_item_wrapper .file_upload {
    border: none !important;
    padding: 0 !important;
    height: unset !important;
    aspect-ratio: 1;
}

.spartan_item_wrapper .file_upload img {
    height: 100%;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
}

.spartan_item_wrapper > img {
    height: unset;
    width: 100%;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
}

.spartan_item_wrapper p {
    margin: 0;
}

@media (min-width: 768px) {
    .spartan_item_wrapper.size--md {
        max-width: 170px !important;
    }
}
.spartan_item_wrapper.size--sm {
    max-width: 120px !important;
}

.nav--tabs .nav-link {
    border-bottom: 3px solid transparent;
    font-weight: 600;
    padding-top: 5px;
    padding-bottom: 5px;
}

.nav--tabs .nav-link.active {
    border-color: var(--primary-clr);
    padding-inline-start: 0;
    padding-inline-end: 0;
}

.nav--tabs .nav-item:first-child .nav-link {
    padding-inline-start: 0;
}

.nav--tabs.dark .nav-link {
    color: var(--title-clr);
    font-weight: 400;
}
.nav--tabs.dark .nav-link.active {
    border-color: var(--title-clr);
    font-weight: 600;
}
.nav--tabs.dark .nav-item:not(:first-child) {
    padding-inline-start: 1.5rem;
}

.text--primary {
    color: var(--primary-clr);
}

.w--30 {
    width: 30px;
}

.report-card-inner {
    max-width: 790px;
    margin: 0 auto 30px;
}

@media (max-width: 575px) {
    .max-sm-flex-1 {
        flex-grow: 1;
    }
}
.toggle-switch-sm.px-4 {
    padding-inline-start: 1rem !important;
    padding-inline-end: 1rem !important;
    min-height: 45px;
}

.toggle-switch {
    border-color: #e7eaf3 !important;
}

.min-height-40 {
    min-height: 40px;
}

.min-height-40.btn {
    align-items: center;
    display: flex;
    justify-content: center;
}

.text--hover {
    color: var(--title-clr);
}

.text--hover:hover {
    color: var(--primary-clr);
}

.max-width-200px {
    max-width: 200px;
}

.word-break {
    white-space: pre-line;
}

.line--limit-1,
.line--limit-2,
.line--limit-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line--limit-1 {
    -webkit-line-clamp: 1;
}

.line--limit-2 {
    -webkit-line-clamp: 2;
}

.line--limit-3 {
    -webkit-line-clamp: 3;
}

.customer--information-single {
    align-content: inherit;
}

.customer--information-single .avatar-img,
.customer--information-single .avatar {
    width: 70px;
    height: 70px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
}

.customer--information-single .media-body {
    text-align: start;
    width: calc(100% - 70px);
    font-size: 13px;
    padding-inline-start: 23px;
}

.card--bg-1 {
    background: #eafbff !important;
    box-shadow: none !important;
}

.card--bg-1 .title {
    color: #16abcb;
}

.card--bg-2 {
    background: #fff7e7 !important;
    box-shadow: none !important;
}

.card--bg-2 .title {
    color: #feb019;
}

.card--bg-3 {
    background: #eafdf6 !important;
    box-shadow: none !important;
}

.card--bg-3 .title {
    color: #00aa6d;
}

.card--bg-4 {
    background: #fff2f2 !important;
    box-shadow: none !important;
}

.card--bg-4 .title {
    color: #ff6d6d;
}

[data-toggle="tooltip"] img {
    margin-inline-start: 4px;
    margin-inline-end: 2px;
    width: 14px;
    transform: translateY(-1px);
}

.select2-selection__rendered {
    transition: all ease 0.3s;
    -webkit-transition: all ease 0.3s;
    -moz-transition: all ease 0.3s;
}

/* .select2:not(.select2-container--open) .select2-search--inline {
    height: 2px;
} */

.toggle-switch-sm .toggle-switch-label {
    width: 44px;
    height: 26px;
    display: flex;
    margin: 0;
}

.toggle-switch-sm .toggle-switch-indicator {
    width: 18px;
    height: 18px;
}

.toggle-switch-sm
    .toggle-switch-input:checked
    + .toggle-switch-label
    .toggle-switch-indicator {
    transform: translate3d(100%, 50%, 0) !important;
}

.switch--label {
    width: 0;
    flex-grow: 1;
}

.initial-hidden {
    display: none;
}

.custom--switch {
    display: flex;
}

.custom--switch input[switch] {
    display: none;
}

.custom--switch input[switch] + label {
    font-size: 1em;
    line-height: 1;
    width: 56px;
    height: 24px;
    background-color: #ced4da;
    background-image: none;
    border-radius: 2rem;
    padding: 0.16667rem;
    cursor: pointer;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    position: relative;
    transition: all 0.1s ease-in-out;
    margin: 0;
}

.custom--switch input[switch] + label:before {
    color: #111;
    content: attr(data-off-label);
    display: block;
    font-family: inherit;
    font-weight: 500;
    font-size: 12px;
    line-height: 21px;
    position: absolute;
    inset-inline-end: 1px;
    margin: 3px;
    top: -2px;
    text-align: center;
    min-width: 1.66667rem;
    overflow: hidden;
    transition: all 0.1s ease-in-out;
    text-transform: uppercase;
}

.custom--switch input[switch] + label:after {
    content: "";
    position: absolute;
    inset-inline-start: 3px;
    background-color: #e9ecef;
    box-shadow: none;
    border-radius: 2rem;
    height: 20px;
    width: 20px;
    top: 2px;
    transition: all 0.1s ease-in-out;
    text-transform: uppercase;
}

.custom--switch input[switch]:checked + label {
    background-color: var(--primary-clr);
}

.custom--switch input[switch]:checked + label {
    background-color: var(--primary-clr);
}

.custom--switch input[switch]:checked + label:before {
    color: #fff;
    content: attr(data-on-label);
    inset-inline-end: auto;
    inset-inline-start: 3px;
}

.custom--switch input[switch]:checked + label:after {
    inset-inline-start: 33px;
    background-color: #e9ecef;
}

.custom--switch input[switch="primary"]:checked + label {
    background-color: var(--primary-clr);
}

.switch--custom-label-text {
    display: none;
    font-weight: 700;
    font-size: 0.75rem;
}

.switch--custom-label.checked .switch--custom-label-text.on {
    display: block;
}

.switch--custom-label:not(.checked) .switch--custom-label-text.off {
    display: block;
}

.switch--custom-label .toggle-switch-label {
    width: 44px;
    height: 26px;
    display: flex;
    margin: 0;
}

.switch--custom-label .toggle-switch-indicator {
    width: 18px;
    height: 18px;
}

.switch--custom-label
    .toggle-switch-input:checked
    + .toggle-switch-label
    .toggle-switch-indicator {
    transform: translate3d(18px, 50%, 0);
}

.text--semititle {
    color: var(--title-clr);
    font-weight: 600;
}

@media (max-width: 767px) {
    .landing-page-images .img--200 {
        width: 120px;
    }
}
.form-control-color {
    width: 80px !important;
    height: 80px !important;
    padding: 3px 4px !important;
    margin: 0 auto;
    display: flex;
    box-shadow: none;
}

.landing--page-btns .btn {
    flex-grow: 1;
}

.white--space {
    white-space: break-spaces;
}

.badge-soft-primary {
    color: #24bac3;
    background-color: rgba(0, 241, 255, 0.1);
    border: 1px solid rgba(0, 241, 255, 0.6);
}

a.badge-soft-primary:focus,
a.badge-soft-primary:hover {
    background: #24bac3;
}

.btn--folder {
    background: transparent !important;
    color: var(--title-clr) !important;
    border: none !important;
}

.btn--folder:hover,
.btn--folder:focus {
    background: transparent !important;
    filter: unset !important;
}

.btn--folder:hover .img-thumbnail,
.btn--folder:focus .img-thumbnail {
    opacity: 0.6;
}

.gallary-card {
    width: 140px;
    cursor: pointer;
}

.btn--folder {
    width: 110px;
    cursor: pointer;
}

.folder-btn-item {
    width: 140px;
}

.gallary-card img {
    width: 100%;
    height: 140px;
    -o-object-fit: cover;
    object-fit: cover;
}

.btn--folder img {
    width: 100%;
    height: 110px;
    -o-object-fit: cover;
    object-fit: cover;
}

.form--check {
    padding-inline-start: 30px !important;
    cursor: pointer;
    margin-bottom: 0;
    position: relative;
}

.form--check .form-check-input {
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
    float: left;
    margin-inline-start: -1.5em;
    width: 0.75rem;
    height: 0.75rem;
    margin-top: 0.35em;
}

.form--check .form-check-input[type="radio"] {
    border-radius: 50%;
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #c4c8d4;
}

.form--check .form-check-input[type="radio"]:checked {
    background-color: #00868f;
    box-shadow: 0 0 0 3px #fff, 0 0 0 4px #c4c8d4;
}

.form--check .form-check-input[type="checkbox"] {
    background: #f4f5f7;
    box-shadow: 0 0 0 2px #f4f5f7, 0 0 0 3px #c4c8d4;
}

.form--check .form-check-input[checked="checked"],
.form--check .form-check-input[type="checkbox"]:checked {
    background: url("./images/check-mark.png") #f4f5f7 no-repeat center
        center/contain;
    box-shadow: 0 0 0 3px #f4f5f7, 0 0 0 4px #c4c8d4;
}

.form--check label::after {
    position: absolute;
    inset: 0;
    z-index: 3;
    content: "";
}

.alert--danger {
    background: rgba(255, 109, 109, 0.05) !important;
    border: 1px solid rgba(255, 109, 109, 0.5);
    border-inline-start: 4px solid rgb(255, 109, 109);
    border-radius: 0 5px 5px 0;
    color: var(--title-clr);
    padding-top: 22px;
    padding-bottom: 25px;
}

.alert--danger .alert--icon {
    font-size: 22px;
    color: rgb(255, 109, 109);
    line-height: 1;
    margin-inline-end: 5px;
}

.clean--database-checkgroup {
    margin-bottom: -25px;
}

.clean--database-checkgroup .check-item {
    text-transform: capitalize;
    padding-bottom: 25px;
}

@media screen and (min-width: 768px) {
    .clean--database-checkgroup .check-item {
        width: 33.3333333333%;
        max-width: 33.3333333333%;
    }
}
.clean--database-checkgroup .check-item .badge-secondary {
    color: var(--title-clr);
    background: #f3f4f5;
    font-weight: 700;
    font-size: 12px;
    line-height: 15px;
    min-width: 44px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clean--database-checkgroup .check-item .form-check-input {
    margin: 0;
    margin-top: 0.4em;
    inset-inline-start: 4px;
}

.clean--database-checkgroup .check-item .form-check-label {
    max-width: 220px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    word-break: break-all;
}

.flex-grow-1 {
    flex-grow: 1;
}

.check--item-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: 30px -5px -30px -10px;
}

.check-item {
    width: 50%;
    max-width: 248px;
    padding: 0 5px 30px;
}

.check-item .form-check {
    margin: 0;
}

.check-item .form-check-label {
    padding-inline-start: 10px;
}

@media screen and (max-width: 1650px) {
    .check-item {
        font-size: 12px;
        line-height: 2;
        max-width: 180px;
    }
}
@media screen and (max-width: 767px) {
    .check-item {
        width: 100%;
    }
}
@media screen and (max-width: 575px) {
    .check-item {
        max-width: 200px;
    }
}
@media screen and (max-width: 425px) {
    .check-item {
        max-width: 100%;
        width: 100%;
    }
}
@media screen and (max-width: 767px) {
    .check--item-wrapper {
        margin-bottom: -15px;
    }
    .check--item-wrapper .check-item {
        padding-bottom: 15px;
    }
}
.sms-module-form {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sms-module-form .btn--container {
    margin-top: auto;
}

.h-unset {
    height: unset !important;
}

.badge {
    white-space: normal;
}

.blurry {
    position: relative;
    opacity: 0.6;
}

.blurry::after {
    inset: 0;
    content: "";
    position: absolute;
    z-index: 1;
}

.payment--gateway-img {
    margin-bottom: 30px;
    margin-top: 14px;
}

.payment--gateway-img img {
    width: 100%;
    height: 80px;
    -o-object-fit: contain;
    object-fit: contain;
}

.nav-sub {
    background: #005555 !important;
}

.scroll-bar {
    max-height: calc(100vh - 100px);
    overflow-y: auto !important;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 1px #cfcfcf;
    /*border-radius: 5px;*/
}

::-webkit-scrollbar {
    width: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    /*border-radius: 5px;*/
}

::-webkit-scrollbar-thumb:hover {
    background: #fc6a57;
}

.deco-none {
    color: inherit;
    text-decoration: inherit;
}

.qcont {
    text-transform: lowercase;
}

.qcont:first-letter {
    text-transform: capitalize;
}

.navbar-vertical .nav-link {
    color: #ffffff;
}

.navbar .nav-link:hover {
    color: #5affba;
}

.navbar .active > .nav-link,
.navbar .nav-link.active,
.navbar .nav-link.show,
.navbar .show > .nav-link {
    color: #5affba;
}

.navbar-vertical .active .nav-indicator-icon,
.navbar-vertical .nav-link:hover .nav-indicator-icon,
.navbar-vertical .show > .nav-link > .nav-indicator-icon {
    color: #5affba;
}

.nav-subtitle {
    display: block;
    color: rgba(255, 251, 223, 0.568627451);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03125rem;
}

.navbar-vertical .navbar-nav.nav-tabs .active .nav-link,
.navbar-vertical .navbar-nav.nav-tabs .active.nav-link {
    border-inline-start-color: #5affba;
}

.item-box {
    height: 250px;
    width: 150px;
    padding: 3px;
}

.header-item {
    width: 10rem;
}

.cursor-pointer {
    cursor: pointer;
}

.select2-selection--single {
    height: 100%;
}

.select2-selection__rendered {
    word-wrap: break-word !important;
    text-overflow: inherit !important;
    white-space: normal !important;
}

.middle-align {
    vertical-align: middle !important;
}

.middle-align td {
    vertical-align: middle !important;
}

.cart--table-scroll {
    max-height: 300px;
    overflow-y: scroll;
}

td .amount--input {
    width: 70px;
    height: 35px;
    margin: 0 auto;
}

.add--customer-btn .btn,
.add--customer-btn .select2 {
    width: unset;
}

.add--customer-btn .select2 {
    width: calc(100% - 195px) !important;
}

.add--customer-btn .btn {
    margin-inline-start: 15px;
    width: 180px;
    padding-inline-start: 5px;
    padding-inline-end: 5px;
}

@media screen and (max-width: 575px) {
    .add--customer-btn .btn,
    .add--customer-btn .select2 {
        width: 100% !important;
        margin: 0;
    }
    .add--customer-btn .btn {
        margin-top: 10px !important;
    }
}
.add--customer-btn .select2-container--default .select2-selection--single {
    height: 45px !important;
}

.order--item-box {
    height: unset !important;
    max-width: 170px;
    width: 100% !important;
}

@media screen and (min-width: 1200px) and (max-width: 1400px) {
    .order--item-box {
        max-width: 33.3333333333% !important;
        width: 33.3333333333% !important;
    }
}
@media screen and (min-width: 1401px) and (max-width: 1650px) {
    .order--item-box {
        max-width: 25% !important;
        width: 25% !important;
    }
}
@media screen and (min-width: 1651px) and (max-width: 1850px) {
    .order--item-box {
        max-width: 20% !important;
        width: 20% !important;
    }
}
@media screen and (min-width: 1851px) and (max-width: 2000px) {
    .order--item-box {
        max-width: 16.6666666667% !important;
        width: 16.6666666667% !important;
    }
}
@media screen and (max-width: 575px) {
    .order--item-box {
        max-width: 50% !important;
    }
}
@media screen and (min-width: 2001px) {
    .order--item-box {
        width: 170px !important;
        max-width: 182px;
        flex-grow: 1;
    }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
    .order--item-box {
        width: 20% !important;
        max-width: 20%;
    }
}
@media screen and (min-width: 576px) and (max-width: 767px) {
    .order--item-box {
        max-width: 33.3333333333% !important;
        width: 33.3333333333% !important;
    }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
    .order--item-box {
        width: 25% !important;
        max-width: 25%;
    }
}
@media screen and (max-width: 575px) {
    .row .order--item-box[class*="col"] {
        padding: 5px !important;
    }
}
.order--pos-left {
    width: calc(100% - 500px);
    margin-inline-end: auto;
}

.order--pos-left .select2-container--default .select2-selection--single {
    height: 45px !important;
}

.order--pos-right {
    width: 100%;
    max-width: 475px;
}

@media screen and (min-width: 1650px) {
    .order--pos-left {
        width: calc(100% - 540px);
    }
    .order--pos-right {
        max-width: 520px;
    }
}
@media screen and (max-width: 1200px) {
    .order--pos-left {
        width: 100%;
        margin-bottom: 15px;
    }
    .order--pos-right {
        max-width: 100%;
    }
}
.cart--media .avatar {
    width: 50px;
    height: 50px;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
}

.button--bottom-fixed {
    position: sticky;
    bottom: 0;
    padding-top: 14px;
}

.table--vertical-middle td {
    vertical-align: middle !important;
}

.pos--payment-options ul {
    display: flex;
    flex-wrap: wrap;
    margin: -2.5px;
    padding: 0;
}

.pos--payment-options ul li {
    list-style: none;
    padding: 2.5px;
}

.pos--payment-options ul li label {
    margin: 0;
}

.pos--payment-options ul li label span {
    color: var(--title-clr);
    border: 1px solid rgb(208, 219, 233);
    border-radius: 3px;
    text-transform: capitalize;
    padding: 8px 12px;
    cursor: pointer;
}

.pos--payment-options ul li label input:checked ~ span {
    color: #fff;
    background: var(--title-clr);
    border-color: var(--title-clr);
}

.pos--payable-amount {
    color: var(--title-clr);
}

.pos--payable-amount .text-body {
    opacity: 0.8;
    margin-inline-end: 10px;
    width: 25px;
    text-align: center;
    cursor: pointer;
}

.pos--delivery-options {
    padding: 9px 12px 19px;
}

.pos--delivery-options .delivery--edit-icon {
    width: 25px;
    text-align: center;
    cursor: pointer;
}

.pos--delivery-options .card-title {
    font-size: 16px;
    margin-bottom: 12px;
}

.pos--delivery-options .card-title i {
    font-size: 18px;
}

.pos--delivery-options-info {
    margin: 0 -5px -15px;
}

.pos--delivery-options-info ul,
.pos--delivery-options-info > .location {
    padding: 0 5px 15px;
    margin: 0;
}

.pos--delivery-options-info ul {
    width: 100%;
    max-width: 200px;
    font-size: 12px;
}

.pos--delivery-options-info ul li {
    list-style: none;
    display: flex;
    text-transform: capitalize;
}

.pos--delivery-options-info ul li span {
    width: 60px;
    display: flex;
    justify-content: space-between;
}

.pos--delivery-options-info ul li span::after {
    content: ":";
}

.pos--delivery-options-info ul li:not(:last-child) {
    margin-bottom: 8px;
}

.pos--delivery-options-info ul li strong {
    color: var(--title-clr);
    width: calc(100% - 60px);
    padding-inline-start: 15px;
}

.pos--delivery-options-info .location {
    font-size: 12px;
    font-weight: 400;
    width: 200px;
    flex-grow: 1;
    display: flex;
}

.pos--delivery-options-info .location i {
    font-size: 24px;
    color: var(--title-clr);
    margin-inline-end: 5px;
}

.delivery--method-radio {
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.delivery--method-radio li {
    list-style: none;
}

.delivery--method-radio li label {
    margin: 0;
}

.delivery--method-radio li span {
    text-transform: capitalize;
    border-radius: 5px;
    padding: 10px 22px;
    color: rgb(162, 173, 187);
    font-weight: 700;
    cursor: pointer;
}

.delivery--method-radio li span i {
    margin-inline-end: 5px;
}

.delivery--method-radio li input:checked ~ span {
    background: #ff8200;
    color: #fff;
}

.search--no-found {
    max-width: 376px;
    text-align: center;
    margin: 30px auto;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: #99a7ba;
}

.search--no-found img {
    width: 66px;
    height: 66px;
    -o-object-fit: contain;
    object-fit: contain;
    margin: 0 auto 20px;
}

.product-card {
    cursor: pointer;
}

.product-card .product-price .text-accent {
    color: #ff8200 !important;
}

.h--45px ~ .select2 .select2-container .select2-selection--single,
.h--45px ~ .select2 .select2-container--default .select2-selection--single {
    height: 45px !important;
}

.btn-check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

.choice-input {
    width: 7rem;
}

.addon-input {
    height: 7rem;
    width: 7rem;
}

.addon-quantity-input {
    height: 2rem;
    width: 7rem;
    z-index: 9;
    bottom: 1rem;
    visibility: hidden;
}

.check-label {
    background-color: #f3f3f3;
    color: #000000;
    border-width: 2px;
    border-color: #babfc4;
    font-weight: bold;
}

.btn-check:checked + .check-label {
    background-color: #ef7822;
    color: #ffffff;
    border: none;
}

.h-200 {
    height: 200px;
}

@media screen and (min-width: 1200px) {
    .order-print-area-left {
        max-width: calc(100% - 360px) !important;
        flex: 0 0 100%;
    }
    .order-print-area-right {
        width: 360px !important;
        max-width: 360px !important;
        flex-grow: 1;
    }
}
@media screen and (min-width: 1460px) {
    .order-print-area-left {
        max-width: calc(100% - 450px) !important;
        width: calc(100% - 450px) !important;
    }
    .order-print-area-right {
        width: 450px !important;
        max-width: 450px !important;
    }
}
.delivery--information-single {
    display: flex;
    flex-wrap: wrap;
    font-size: 13px;
}

.delivery--information-single > span {
    display: block;
}

.delivery--information-single .name {
    width: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.delivery--information-single .name::after {
    content: ":";
    display: inline-block;
}

.delivery--information-single .info {
    width: calc(100% - 60px);
    padding-inline-start: 20px;
    font-weight: 500;
    color: var(--title-clr);
}

.customer--information-single {
    align-content: inherit;
}

.customer--information-single .avatar-img,
.customer--information-single .avatar {
    width: 70px;
    height: 70px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
}

.customer--information-single .media-body {
    text-align: start;
    width: calc(100% - 70px);
    font-size: 12px;
    padding-inline-start: 23px;
}

.resturant--information-single {
    align-content: inherit;
}

.resturant--information-single .avatar-img,
.resturant--information-single .avatar {
    width: 70px;
    height: 70px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 5px;
}

.resturant--information-single .avatar-img {
    border: 1px solid #f8923b;
}

.resturant--information-single .media-body {
    text-align: start;
    width: calc(100% - 70px);
    font-size: 12px;
    padding-inline-start: 23px;
}

.order-invoice-right-contents h6 span:not(.badge) {
    font-weight: 400;
    color: var(--title-clr);
    font-size: 12px;
}

.order-invoice-right-contents h6 strong {
    color: var(--title-clr);
    font-weight: 600;
}

.order-invoice-right-contents h6 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    gap: 5px;
}

.order-invoice-left {
    font-size: 12px;
}

.order-invoice-left .btn--sm {
    font-size: 12px;
    padding: 5px 10px;
}

.order-invoice-left,
.order-invoice-right {
    max-width: 50%;
}

@media screen and (max-width: 575px) {
    .order-invoice-left,
    .order-invoice-right {
        max-width: 100%;
        width: 100%;
    }
}
@media screen and (max-width: 575px) {
    .print--btn {
        padding: 0 !important;
        width: 28px;
        height: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .print--btn span {
        display: none;
    }
    .print--btn i {
        font-size: 16px !important;
        font-weight: 400 !important;
        margin: 0;
    }
}
.order--note {
    max-width: 540px;
}

.add--referal {
    font-size: 12px !important;
    padding: 4px 10px !important;
}

.line--limit-1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media screen and (max-width: 767px) {
    .media--sm {
        align-items: center;
    }
    .media--sm .avatar {
        width: 68px !important;
        height: 68px !important;
        margin-inline-end: 10px !important;
    }
    .media--sm .media-body {
        width: calc(100% - 80px);
    }
    .media--sm .media-body .line--limit-1 {
        font-size: 0.75rem;
    }
    .media--sm .media-body .font-size-sm {
        font-size: 0.7rem;
    }
}
.media--sm .avatar img {
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
}

@media screen and (max-width: 767px) {
    .table--mobile-sm tr td {
        padding: 5px !important;
    }
    .font--max-sm {
        font-size: 0.875rem;
    }
}
.profile-sidebar-sticky {
    position: sticky;
    top: 80px;
}

.profile-sidebar-sticky .nav-item .nav-link.active {
    color: var(--primary-clr) !important;
}

.app--version {
    color: rgb(36, 186, 195);
    background: rgba(36, 186, 195, 0.1);
}

.bootstrap-tagsinput input {
    width: 100%;
    height: 36px !important;
    color: #005555;
    background-color: #fff;
    border: 0.0625rem solid #e7eaf3;
    border-radius: 0.3125rem;
    border-radius: 6px;
    padding-inline: 16px;
}

.quick--address-bar {
    display: flex;
}

.quick--address-bar .quick-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border-radius: 50%;
}

.quick--address-bar .info {
    width: 0;
    flex-grow: 1;
    padding-inline-start: 13px;
    font-size: 0.75rem;
}

.quick--address-bar .info h6 {
    text-transform: capitalize;
    margin: 0;
}

#pac-input {
    display: none;
}

#map-canvas #pac-input,
.gm-style #pac-input,
#location_map_canvas #pac-input {
    display: block;
}

.fixed--to-right {
    position: absolute;
    inset-inline-end: -5px;
    top: -10px;
}

.imgbgchk + label > img {
    transition: transform 0.3s !important;
}

.imgbgchk:checked + label > img {
    border-color: var(--primary-clr) !important;
    transform: scale(1.05);
}

.w-75px {
    width: 75px;
}

.cart--media .avatar img {
    -o-object-fit: cover;
    object-fit: cover;
    aspect-ratio: 1;
    width: 100%;
    border-radius: 5px;
}

.img--134 {
    height: 134px;
    width: 100%;
    overflow: hidden;
    -o-object-fit: cover;
    object-fit: cover;
}

.chart-bg-1 {
    background: #005555;
}

.chart-bg-2 {
    background: #00aa96;
}

.chart-bg-3 {
    background: #b9e0e0;
}
.chart-bg-4 {
    background: #91d9eb;
}

.w--30 {
    width: 30px !important;
}

.transaction--information {
    border: 1px solid #f4f4f4;
    box-shadow: 0px 3px 5px rgba(51, 66, 87, 0.05);
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    margin-bottom: 30px;
}

.transaction--information li {
    list-style: none;
    display: flex;
    padding: 5px;
    font-size: 0.7rem;
}

.transaction--information li i {
    font-size: 1.5rem;
}

.transaction--information li > div {
    align-self: center;
    padding-inline-start: 5px;
}

.transaction--information li > div span {
    margin-inline-end: 10px;
    font-weight: 500;
}

.transaction--information li > div * {
    display: inline-block;
}

.transaction--information li > div strong {
    font-size: 14px;
}

@media screen and (max-width: 1199px) {
    .transaction--information {
        justify-content: space-evenly;
    }
    .transaction--information .seperator {
        display: none;
    }
}
@media screen and (max-width: 767px) {
    .transaction--information li {
        flex-grow: 1;
    }
}
@media screen and (max-width: 575px) {
    .transaction--information {
        padding: 15px 10px;
    }
    .transaction--information li {
        flex-grow: 1;
    }
    .transaction--information li > div {
        display: flex;
        justify-content: space-between;
        flex-grow: 1;
    }
    .transaction--information li > div strong {
        white-space: nowrap;
    }
}
.transaction--information .seperator {
    background: rgba(0, 0, 0, 0.45);
    height: 25px;
    width: 1px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding: 0;
}

.text--info {
    color: rgba(6, 97, 203, 0.8) !important;
}

.text--success {
    color: rgba(0, 170, 109, 0.9) !important;
}

.text--danger {
    color: rgba(248, 59, 59, 0.8) !important;
}

.text--warning {
    color: var(--warning-clr) !important;
}

.success--icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 26px;
    height: 26px;
    background: rgba(0, 170, 109, 0.2);
    font-size: 1rem !important;
    line-height: 1;
    border-radius: 50%;
}

.table-rest-info {
    display: flex;
    align-items: center;
}

.table-rest-info img {
    width: 50px;
    height: 50px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 10px;
}

.table-rest-info .info {
    max-width: calc(100% - 50px);
    padding-inline-start: 10px;
    font-size: 0.875rem;
    font-weight: 600;
}

.table-rest-info .info .rating {
    font-size: 0.875rem;
    font-weight: 400;
}

.rating {
    color: #f8923b;
}

.owner--name {
    font-size: 0.875rem;
    font-weight: 600;
}

.resturant-list-table .table > tbody > tr:first-child > td {
    padding-top: 0.614rem;
    padding-bottom: 0.614rem;
}

.cash-icon img {
    max-width: 100%;
}

.cash--title {
    font-size: 2.25rem;
    line-height: 1;
    margin: 0;
    color: var(--title-clr) !important;
}

.cash--subtitle {
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: capitalize;
    margin-top: 10px;
    color: var(--title-clr) !important;
}

.resturant--info-address {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.resturant--info-address .logo {
    width: 130px;
    aspect-ratio: 1;
    border: 1px solid var(--base-clr);
    border-radius: 10px;
    overflow: hidden;
}

.resturant--info-address .logo img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.resturant--info-address .avatar {
    width: 106px !important;
    height: 106px !important;
    aspect-ratio: 1;
}

.resturant--info-address .avatar img {
    -o-object-fit: cover;
    object-fit: cover;
}

.resturant--info-address .address-info {
    width: calc(100% - 130px);
    padding-inline-start: 20px;
    margin: 0 !important;
}

.resturant--info-address .address-info .name {
    font-size: 16px;
    font-weight: 600;
}

.resturant--info-address .address-info li {
    padding: 0;
    display: flex;
}

.resturant--info-address .address-info li:not(:last-child) {
    margin-bottom: 8px;
}

.resturant--info-address .address-info-2 {
    width: calc(100% - 106px);
}

.resturant--info-address .address-info-2 li {
    font-size: 0.875rem;
}

.resturant--info-address .address-info-2 li:not(:last-child) {
    margin-bottom: 5px;
}

@media screen and (max-width: 490px) {
    .resturant--info-address .logo {
        width: 100px;
    }
    .resturant--info-address .address-info {
        width: calc(100% - 100px);
    }
    .resturant--info-address .address-info li {
        font-size: 0.875rem;
    }
    .resturant--info-address .address-info li:not(:last-child) {
        margin-bottom: 5px;
    }
    .resturant--info-address .avatar {
        width: 70px !important;
        height: 70px !important;
    }
    .resturant--info-address .address-info-2 {
        width: calc(100% - 90px);
    }
}
@media screen and (max-width: 425px) {
    .resturant--info-address .address-info,
    .resturant--info-address .address-info-2 {
        width: 100%;
        padding-inline-start: 0;
        margin-top: 10px !important;
    }
}
#map.single-page-map {
    height: 157px !important;
}

.bank-name {
    font-size: 16px;
    font-weight: 600;
}

.order--status {
    font-size: 12px;
}

.customer-info-table-data {
    min-width: 130px;
}

.customer-info-table-data a {
    font-weight: 600;
    color: var(--title-clr);
}

.customer-info-table-data small {
    font-size: 0.75rem;
    line-height: 16px;
    margin-top: 4px;
}

.total-amount-table-data {
    min-width: 80px;
}

.badge--pending-1 {
    color: #09c0d8;
    background: rgba(0, 198, 174, 0.05);
}
.badge--accepted {
    color: #00c6ae;
    background: rgba(0, 198, 174, 0.05);
}

.badge--pending {
    color: #ff8620;
    background: rgba(255, 134, 32, 0.05);
}

.badge--cancel {
    color: #ff6d6d;
    background: rgba(255, 109, 109, 0.05);
}

.action-btn i {
    font-size: 1.14rem;
}

.resturant-card-navbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 0px 5px rgba(51, 66, 87, 0.1);
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 6px 10px 6px 5px;
    justify-content: space-between;
}

.resturant-card-navbar .order-info-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
}

.resturant-card-navbar .order-info-item img {
    width: 24px;
    height: 24px;
    -o-object-fit: contain;
    object-fit: contain;
}

.resturant-card-navbar .order-info-item .order-info-icon {
    margin-inline-end: 10px;
}

.resturant-card-navbar .order-info-item .icon-sm img {
    width: unset;
    height: unset;
    max-width: 24px;
    max-height: 24px;
}

.resturant-card-navbar .order-info-item .card-subtitle {
    font-size: 0.875rem;
    margin: 0;
    text-transform: capitalize;
    color: var(--title-clr);
    font-weight: 400;
    transition: all ease 0.3s;
}

.resturant-card-navbar .order-info-item .card-subtitle .amount {
    font-weight: 700;
    margin-inline-start: 10px;
    display: inline-block;
}

@media screen and (min-width: 1200px) and (max-width: 1300px) {
    .resturant-card-navbar .order-info-item .card-subtitle {
        font-size: 0.77rem;
    }
}
.resturant-card-navbar .order-info-item:hover .card-subtitle {
    color: var(--primary-clr);
}

.resturant-card-navbar .order-info-seperator {
    display: inline-block;
    width: 1px;
    height: 25px;
    background: var(--border-clr);
}

.resturant-card-navbar .order-info-seperator:last-child {
    display: none;
}

@media screen and (max-width: 1199px) {
    .resturant-card-navbar {
        justify-content: space-evenly;
    }
}
@media screen and (max-width: 991px) {
    .resturant-card-navbar {
        padding: 15px 0 10px;
    }
    .resturant-card-navbar .order-info-seperator {
        display: none;
    }
}
@media screen and (max-width: 500px) {
    .resturant-card-navbar .order-info-item {
        width: 100%;
        padding-inline-start: 15px;
        padding-inline-end: 20px;
        margin: 0 auto;
    }
    .resturant-card-navbar .order-info-item .card-subtitle {
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
    }
}
.badge-circle {
    border-radius: 50%;
}

@media screen and (min-width: 576px) {
    .flex-grow-sm-0 {
        flex-grow: 0 !important;
    }
}
@media screen and (max-width: 575px) {
    .flex-grow-sm-0 {
        margin-inline-end: 7px;
    }
}
.navbar-vertical .navbar-nav.nav-tabs .active .nav-link .badge-pill {
    padding: 0.3em 0.5em;
}

#map.single-page-map {
    height: 157px !important;
}

.dataTables_empty {
    text-align: center;
}

.progress-name {
    width: 100px;
    margin-inline-end: 23px;
    color: var(--title-clr);
}

.resturant-review-top {
    max-width: 900px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

@media screen and (min-width: 576px) {
    .resturant-review-top {
        margin-top: 30px;
        margin-bottom: 60px;
    }
}
.resturant-review-top .resturant-review-right {
    flex-grow: 1;
    width: 300px;
    max-width: 555px;
}

.resturant-review-top .resturant-review-right ul li:not(:last-child) {
    padding-bottom: 20px;
}

.resturant-review-top .resturant-review-left {
    width: 190px;
    padding-inline-end: 20px;
}

.resturant-review-top .resturant-review-left .title {
    font-weight: 600;
    font-size: 50px;
    line-height: 59px;
    letter-spacing: 0.007em;
    color: var(--primary-clr);
}

.resturant-review-top .resturant-review-left .title .out-of {
    font-size: 35px;
    line-height: 41px;
    color: #758590;
    font-weight: 500;
}

.resturant-review-top .resturant-review-left .info {
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    letter-spacing: 0.007em;
    color: #758590;
}

.resturant-review-top .resturant-review-left i {
    color: var(--primary-clr);
    font-size: 18px;
}

@media screen and (max-width: 575px) {
    .resturant-review-top .resturant-review-left {
        width: 100%;
        padding: 15px 0 20px;
        text-align: center;
    }
    .resturant-review-top .resturant-review-left .title {
        font-size: 40px;
        line-height: 40px;
        letter-spacing: 0.007em;
    }
    .resturant-review-top .resturant-review-left .title .out-of {
        font-size: 28px;
        line-height: 35px;
    }
}
.progress {
    background: #f3f4f5;
    min-width: 100px;
}

.progress .progress-bar {
    background: var(--primary-clr);
}

.review-color-progress .progress .progress-bar {
    background: #f59300;
}

.review-table-date {
    max-width: 95px;
    white-space: initial;
    text-align: end;
    text-transform: uppercase;
}

.start--time,
.end--time {
    border: 1px solid #005555;
    border-radius: 5px;
    text-transform: uppercase;
    padding: 8px 14px;
    display: flex;
    margin: 4px;
}

.start--time .info span,
.end--time .info span {
    text-transform: capitalize;
}

.start--time .clock--icon,
.end--time .clock--icon {
    width: 20px;
}

.start--time .info,
.end--time .info {
    width: calc(100% - 20px);
}

.start--time .info span,
.end--time .info span {
    display: block;
}

.schedult-date-content .add--primary {
    margin-top: 21px;
}

.schedult-date-content .btn-outline-danger.disabled ~ .add--primary {
    margin-top: 0;
}

@media screen and (max-width: 575px) {
    .schedule-item {
        flex-wrap: wrap;
    }
    .schedule-item .schedult-date-content {
        width: 100% !important;
    }
    .schedule-item > .btn {
        padding-bottom: 0 !important;
    }
    #schedule {
        padding-inline-start: 15px;
        padding-inline-end: 15px;
    }
}
.add--primary {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 0 !important;
    background: var(--primary-clr);
    color: #fff !important;
    padding: 0 !important;
    border: none;
}

.dismiss--date {
    text-align: center;
    color: var(--danger-clr);
    display: inline-block;
    margin: 0 5px;
    font-size: 20px;
    cursor: pointer;
}

.schedule-item {
    display: flex;
    align-items: center;
}

.schedule-item > span:first-child {
    width: 119px;
    text-align: start;
    padding-inline-start: 0 !important;
    padding-inline-end: 0 !important;
    color: var(--title-clr);
}

.schedule-item .schedult-date-content {
    width: 0;
    flex-grow: 1;
    color: var(--title-clr);
    border-bottom: 1px solid var(--border-clr);
    padding: 12px 0;
}

@media screen and (max-width: 500px) {
    .schedule-item > span:first-child {
        width: 80px;
        font-size: 12px;
    }
    .schedule-item .schedult-date-content {
        width: calc(100% - 80px);
        font-size: 12px;
    }
}
.transaction--table-nav {
    margin: 5px;
}

@media screen and (min-width: 768px) {
    .transaction--table-nav {
        border: none;
        border-inline-end: 1px solid #99a7ba !important;
        border-inline-start: 1px solid #99a7ba !important;
        border-radius: 5px;
    }
    .transaction--table-nav .nav-item {
        flex-grow: 1;
        margin: 0 !important;
    }
    .transaction--table-nav .nav-item .nav-link {
        font-weight: 600 !important;
        margin: 0 !important;
        padding: 10px 12px;
        color: #99a7ba;
        border-top: 1px solid #99a7ba !important;
        border-bottom: 1px solid #99a7ba !important;
        font-size: 0.75rem;
        display: block;
        text-align: center;
        border-radius: 0 !important;
    }
    .transaction--table-nav .nav-item .nav-link.active {
        color: #fff;
        background: var(--title-clr) !important;
        border-color: var(--title-clr) !important;
    }
    .transaction--table-nav .nav-item:first-child .nav-link {
        border-radius: 5px 0 0 5px !important;
    }
}
@media screen and (min-width: 768px) and (max-width: 680px) {
    .transaction--table-nav .nav-item:first-child .nav-link {
        border-radius: 5px 5px 0 0 !important;
    }
}
@media screen and (min-width: 768px) {
    .transaction--table-nav .nav-item:last-child .nav-link {
        border-radius: 0 5px 5px 0 !important;
    }
}
@media screen and (min-width: 768px) and (max-width: 680px) {
    .transaction--table-nav .nav-item:last-child .nav-link {
        border-radius: 0 0 5px 5px !important;
    }
}
@media screen and (min-width: 768px) and (max-width: 680px) {
    .transaction--table-nav .nav-item {
        width: 100%;
    }
}
@media screen and (min-width: 768px) and (max-width: 680px) {
    .transaction--table-nav .nav-item:not(:first-child) .nav-link {
        border-top: none !important;
    }
}
@media screen and (min-width: 768px) {
    .transaction--table-nav .nav-item:not(:last-child) .nav-link {
        border-inline-end: 1px solid #99a7ba !important;
    }
}
@media screen and (min-width: 768px) and (max-width: 680px) {
    .transaction--table-nav .nav-item:not(:last-child) .nav-link {
        border-bottom: none;
        border-inline-end: none !important;
    }
}
.navbar-nav .btn-icon {
    width: 36px;
    height: 36px;
    box-shadow: none !important;
}

.select2
    .select2-container
    .select2-container--default
    .select2-container--above
    .select2-container--focus {
    width: 100% !important;
}

#location_map_canvas {
    height: 100%;
}

@media only screen and (max-width: 768px) {
    /* For mobile phones: */
    #location_map_canvas {
        height: 200px;
    }
}
.img--circle {
    border-radius: 50%;
}

.p--10 {
    padding: 10px;
}

div.scroll-down {
    max-height: 300px;
    overflow-y: auto;
}

.iti {
    width: 100%;
}

.conv-active {
    border-top: 1px solid rgba(0, 134, 143, 0.4);
    border-bottom: 1px solid rgba(0, 134, 143, 0.4);
    background-color: rgba(0, 159, 170, 0.05);
}

.conv-active .chat-user-info-content h5 {
    color: var(--primary-clr);
}

.btn-check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

.choice-input {
    width: 7rem;
}

.addon-input {
    height: 7rem;
    width: 7rem;
}

.addon-quantity-input {
    height: 2rem;
    width: 7rem;
    z-index: 9;
    bottom: 1rem;
    visibility: hidden;
}

.check-label {
    background-color: #f3f3f3;
    color: #000000;
    border-width: 2px;
    border-color: #babfc4;
    font-weight: bold;
}

.btn-check:checked + .check-label {
    background-color: #ef7822;
    color: #ffffff;
    border: none;
}

.fz--10 {
    font-size: 10px !important;
}

.inline--1 {
    height: 60px;
    width: 60px;
    overflow-x: hidden;
    overflow-y: hidden;
}

.inline--2 {
    padding: 10px;
    border: 1px solid;
    cursor: pointer;
}

.inline--3 {
    max-width: 150px;
}

.initial--4 {
    border: 1px solid #f4f4f4;
    border-radius: 10px;
    height: 92px;
    aspect-ratio: 3;
    -o-object-fit: cover;
    object-fit: cover;
}

.initial--5 {
    aspect-ratio: 3;
    width: 100%;
}

.initial--6 {
    padding: 10px;
    border: 1px solid;
    cursor: pointer;
}

.initial--7 {
    width: 100%;
    border: 1px solid;
    border-radius: 10px;
    max-height: 200px;
}

.initial--8 {
    background: rgba(253, 255, 221, 0.8196078431);
    padding: 10px;
    border: 1px solid rgba(128, 128, 128, 0.3411764706);
    border-radius: 10px;
    width: 50%;
}

.initial--9 {
    background: rgba(137, 250, 255, 0.2784313725);
    padding: 10px;
    border: 1px solid rgba(128, 128, 128, 0.3411764706);
    border-radius: 10px;
    width: 50%;
}

.initial--10 {
    width: 54px;
    height: 54px;
}

.initial--11 {
    overflow-y: scroll;
    height: 600px;
}

.initial--12 {
    cursor: pointer;
    padding-inline-start: 6px;
    border-radius: 10px;
    margin-top: 2px;
}

.initial--13 {
    color: rgba(113, 120, 133, 0.62);
}

.initial--14 {
    height: 10px !important;
    width: 10px !important;
}

.initial--15 {
    height: 130px !important;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
}

.initial--16 {
    height: 100%;
    width: 100%;
    max-width: 120px;
    border-radius: 5%;
    -o-object-fit: cover;
    object-fit: cover;
}

.initial--17 {
    height: 9.5rem;
}

.initial--18 {
    font-size: 12px !important;
}

.initial--19 {
    width: 160px;
}

.initial--20 {
    height: 120px;
    width: auto;
    max-width: 120px;
    overflow: hidden;
    border-radius: 5px;
    -o-object-fit: cover;
    object-fit: cover;
}

.initial--21 {
    border: 1px solid rgba(128, 128, 128, 0.2392156863);
    border-radius: 5px;
}

.initial--22 {
    height: auto;
    width: 50%;
}

.initial--23 {
    max-height: 400px;
}

.initial--24 {
    width: 600px;
    height: 400px;
}

.initial--25 {
    width: 100%;
    height: 400px !important;
}

.initial--26 {
    height: 20rem;
}

.initial--27 {
    padding-inline-start: 6px;
    padding-inline-end: 6px;
    cursor: pointer;
}

.initial--28 {
    width: 100%;
    max-height: 120px;
    border-radius: 8px;
    -o-object-fit: cover;
    object-fit: cover;
    aspect-ratio: 1;
}

.initial--29 {
    height: 3em;
    width: -moz-fit-content;
    width: fit-content;
}

.initial--30 {
    height: 120px;
    width: 120px;
    -o-object-fit: cover;
    object-fit: cover;
    overflow: hidden;
    border-radius: 5%;
}

.initial--31 {
    height: 134px;
    width: 100%;
    overflow: hidden;
}

@media (max-width: 450px) {
    .initial--31 {
        height: 100px;
    }
}
.initial--32 {
    height: 3.5rem;
    max-height: 3.5rem;
}

.initial--33 {
    height: 3em;
    width: -moz-fit-content;
    width: fit-content;
}

.initial--34 {
    height: 100%;
    margin: 0px;
    padding: 0px;
}

.initial--35 {
    width: 35rem;
}

.initial--36 {
    max-height: 55px;
    border-radius: 8px;
    max-width: 100% !important;
}

.initial--37 {
    border-radius: 50%;
    height: 47px;
    width: 47px !important;
    border: 5px solid rgba(128, 128, 128, 0.0705882353);
}

.initial--38 {
    cursor: pointer;
    font-weight: bold;
    font-size: 15px;
}

.initial--39 {
    border-radius: 50%;
    height: 55px;
    width: 55px !important;
    border: 5px solid rgba(128, 128, 128, 0.0705882353);
}

.initial--40 {
    max-width: 150px;
    margin-top: -10px;
}

.initial--41 {
    max-width: 150px;
    margin-top: 0px;
}

.initial--42 {
    height: 3em;
    width: -moz-fit-content;
    width: fit-content;
}

.mb--1 {
    margin-bottom: -1px;
}

.min-height-170 {
    min-height: 170px;
}

.fz--13 {
    font-size: 13px !important;
}

.fz--30 {
    font-size: 30px;
}

.min-height-260 {
    min-height: 260px;
}

.opacity-0 {
    opacity: 0;
}
.opacity-50 {
    opacity: 0.5;
}
.opacity-60 {
    opacity: 0.6;
}
.opacity-70 {
    opacity: 0.7;
}

.bg--005555 {
    background-color: #005555;
}

.loading-inner {
    position: fixed;
    z-index: 9999;
    inset-inline-start: 40%;
    top: 37%;
    width: 100%;
}

.btn-info {
    background: #0b1226 !important;
    color: white !important;
    border: #0b1226 !important;
}

.min-height-100px {
    min-height: 100px;
}

.h--70px {
    height: 70px !important;
}

.h--100px {
    height: 100px;
}

.mt--n-11 {
    margin-top: -11px;
}

.stripe-button-el {
    display: none !important;
}

.razorpay-payment-button {
    display: none !important;
}

.w-7rem {
    width: 7rem;
}

.visiblity-visible {
    visibility: visible !important;
}

.visiblity-hidden {
    visibility: hidden !important;
}

.bg-511281 {
    background-color: #511281 !important;
}

.bg-4CA1A3 {
    background-color: #4ca1a3 !important;
}

.fz-30px {
    font-size: 30px;
}

.bg-4D6F72 {
    background-color: #4d6f72;
}

.bg-308575 {
    background-color: #308575;
}

.bg-2AA26A {
    background-color: #2aa26a;
}

.bg-0468A6 {
    background-color: #0468a6;
}

.dropdown-card {
    width: 220px;
}

.w-5p {
    width: 5%;
}

.w-10p {
    width: 10%;
}

.w-15p {
    width: 15%;
}

.w-20p {
    width: 20%;
}

.w-25p {
    width: 25%;
}

.w-30p {
    width: 30%;
}

.w-33p {
    width: 33%;
}

.w-35p {
    width: 35%;
}

.w-40p {
    width: 40%;
}

.w-45p {
    width: 45%;
}

.w-50p {
    width: 50%;
}

.w-55p {
    width: 55%;
}

.w-60p {
    width: 60%;
}

.w-65p {
    width: 65%;
}

.w-70 {
    width: 70%;
}

.w-75p {
    width: 75%;
}

.w-80p {
    width: 80%;
}

.w-85p {
    width: 85%;
}

.w-90p {
    width: 90%;
}

.w-95p {
    width: 95%;
}

.navbar-vertical-aside-has-menu.active > .nav {
    display: block;
}

.w-18rem {
    width: 18rem;
}

.bank--card {
    background: rgba(51, 66, 87, 0.05);
    border-radius: 5px;
    width: 100%;
    max-width: 555px;
    position: relative;
}

.bank--card__header {
    padding: 15px 30px;
}

.bank--card__header .title {
    font-size: 18px;
    line-height: 30px;
    color: var(--title-clr);
}

.bank--card__body {
    padding: 30px;
    position: relative;
}

.bank--card__body .bank__icon {
    position: absolute;
    inset-inline-end: 30px;
    top: 30px;
}

.bank--card__body .bank__icon img {
    max-width: 78px;
}

@media screen and (max-width: 575px) {
    .bank--card__body .bank__icon {
        max-width: 60px;
        inset-inline-end: 20px;
        top: 20px;
    }
}
@media screen and (max-width: 575px) {
    .bank--card__body {
        padding: 20px;
    }
}
.bank--card__body ul {
    padding: 0;
    margin: 0;
}

.bank--card__body ul li {
    list-style: none;
}

.bank--card__body ul li h5 {
    margin: 0;
    margin-bottom: 5px;
    font-size: 16px;
}

.bank--card__body ul li .info {
    font-weight: 500;
    font-size: 14px;
}

.bank--card__body ul li:not(:last-child) {
    margin-bottom: 10px;
}

.bank--card::before {
    position: absolute;
    content: "";
    bottom: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 146px;
    background: url(./images/bank-shapes.png) no-repeat center top/cover;
}

@media screen and (max-width: 575px) {
    .bank--card .bank--card__header {
        padding: 15px;
    }
    .bank--card .bank--card__header .title {
        font-size: 15px;
        line-height: 1.4;
    }
    .bank--card .bank--card__body ul li h5 {
        font-size: 14px;
    }
    .bank--card .bank--card__body ul li .info {
        font-size: 13px;
    }
}
.btn--container .file_upload {
    width: 120px !important;
    height: 120px !important;
    display: contents !important;
}

.btn--container .spartan_item_wrapper .file_upload img {
    width: 120px !important;
    height: 120px !important;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 8px;
    margin: 0 !important;
}

.badge {
    text-transform: capitalize;
}

.max--200 {
    max-width: 200px;
}

.my-resturant--card {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 40px 45px;
    max-width: 676px;
    margin: -80px 40px 40px;
    background: #fff;
    box-shadow: 0 6px 12px rgba(140, 152, 164, 0.08);
    position: relative;
    z-index: 9;
    border-radius: 8px;
}

.my-resturant--card .my-resturant--avatar {
    width: 156px;
    aspect-ratio: 1;
}

.my-resturant--card .my-resturant--avatar img {
    width: 100%;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
}

.my-resturant--card .my-resturant--content {
    max-width: 323px;
    width: calc(100% - 156px);
    margin: 0 auto;
    padding: 0 20px;
}

.my-resturant--card .my-resturant--content span strong,
.my-resturant--card .my-resturant--content span a {
    color: var(--title-clr);
}

@media screen and (max-width: 767px) {
    .my-resturant--card {
        margin-inline-start: 20px;
        margin-inline-end: 20px;
    }
}
@media screen and (max-width: 575px) {
    .my-resturant--card {
        padding: 25px;
    }
    .my-resturant--card .my-resturant--avatar {
        width: 90px;
    }
    .my-resturant--card .my-resturant--content {
        width: calc(100% - 90px);
    }
}
@media screen and (max-width: 460px) {
    .my-resturant--card .my-resturant--avatar {
        width: 110px;
    }
    .my-resturant--card .my-resturant--avatar img {
        border-radius: 15px;
    }
    .my-resturant--card .my-resturant--content {
        width: 100%;
        max-width: 100%;
        padding: 20px 0 0;
    }
}
@media screen and (max-width: 460px) {
    .my-resturant--card {
        padding: 20px;
    }
}
.my-restaurant-img {
    width: 100%;
    border-radius: 15px 15px 0 0;
    height: 320px;
    -o-object-fit: cover;
    object-fit: cover;
}

@media (max-width: 375px) {
    #shop-image-modal .modal-content {
        width: 367px !important;
        margin-inline-start: 0 !important;
    }
}
@media (max-width: 500px) {
    #shop-image-modal .modal-content {
        width: 400px !important;
        margin-inline-start: 0 !important;
    }
}
.store-banner {
    max-width: 100%;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
    max-height: 160px;
}

.w-5px {
    width: 5px;
}

.w--5px {
    width: 5px;
}

.w-10px {
    width: 10px;
}

.w--10px {
    width: 10px;
}

.w-15px {
    width: 15px;
}

.w--15px {
    width: 15px;
}

.w-20px {
    width: 20px;
}

.w--20px {
    width: 20px;
}

.w-25px {
    width: 25px;
}


.w--25px {
    width: 25px;
}

.w-30px {
    width: 30px;
}

.w--30px {
    width: 30px;
}

.w-35px {
    width: 35px;
}
.h-35px{
    height: 35px;
}

.w--35px {
    width: 35px;
}

.w-40px {
    width: 40px;
}

.w--40px {
    width: 40px;
}

.w-45px {
    width: 45px;
}

.w--45px {
    width: 45px;
}

.w-50px {
    width: 50px;
}

.w--50px {
    width: 50px;
}

.w-55px {
    width: 55px;
}

.w--55px {
    width: 55px;
}

.w-60px {
    width: 60px;
}

.w--60px {
    width: 60px;
}

.w-65px {
    width: 65px;
}

.w--65px {
    width: 65px;
}

.w-70x {
    width: 70px;
}

.w--70x {
    width: 70px;
}

.w-75px {
    width: 75px;
}

.w-75px {
    width: 75px;
}

.w-80px {
    width: 80px;
}

.w--80px {
    width: 80px;
}

.w-85px {
    width: 85px;
}

.w-85px {
    width: 85px;
}

.w-90px {
    width: 90px;
}

.w--90px {
    width: 90px;
}

.w-95px {
    width: 95px;
}

.w--95px {
    width: 95px;
}

.w-100px {
    width: 100px;
}

[class*="card--bg"] {
    border: none !important;
}

.config-inline-remove-class {
    /* Rounded sliders */
}

.config-inline-remove-class .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
}

.config-inline-remove-class .slider:before {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    inset-inline-start: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
}

.config-inline-remove-class input:checked + .slider {
    background-color: #00868f;
}

.config-inline-remove-class input:focus + .slider {
    box-shadow: 0 0 1px #00868f;
}

.config-inline-remove-class input:checked + .slider:before {
    transform: translateX(26px);
}

.config-inline-remove-class .slider.round {
    border-radius: 34px;
}

.config-inline-remove-class .slider.round:before {
    border-radius: 50%;
}

.dataTables_empty {
    display: none;
}

.p-20 {
    padding: 20px;
}

.initial--43 {
    z-index: 9;
    position: absolute;
    inset-inline-end: 0.5rem;
    top: 0.5rem;
}

.max-height-100px {
    height: 100px;
}

.max-width-300px {
    max-width: 300px;
}

.pr-10 {
    padding-inline-end: 10px;
}

.pl-10 {
    padding-inline-start: 10px;
}

.mw-15rem {
    max-width: 15rem;
}

.loader--inner {
    position: fixed;
    z-index: 9999;
    inset-inline-start: 40%;
    top: 37%;
    width: 100%;
}

.img--max-200 {
    max-width: 200px;
    max-height: 60px;
}

.fz--60px {
    font-size: 60px;
}

.fz--40px {
    font-size: 40px;
}

.h-20rem {
    height: 20rem;
}

.h-15rem {
    height: 15rem;
}

.color-8a8a8a {
    color: #8a8a8a;
}

.color-3C3C3C {
    color: #3c3c3c;
}

.color-484848 {
    color: #484848;
}

.h-auto {
    height: auto;
}

.fz-10px {
    font-size: 10px !important;
}

.mb--n-1 {
    margin-bottom: -1px;
}

.max-300px-y-auto {
    max-height: 300px;
    overflow-y: scroll;
}

.h-9rem {
    height: 9.5rem;
}

.img-responsive-1 {
    height: 100%;
    width: auto;
    overflow: hidden;
    border-radius: 5%;
}

.navbar-brand-logo {
    border-radius: 50%;
    height: 55px;
    width: 55px !important;
    border: 5px solid rgba(128, 128, 128, 0.0705882353);
}

.fz-15px {
    font-size: 15px;
}

.w-16rem {
    width: 16rem;
}

.initial--44 .scroll-bar {
    max-height: calc(100vh - 100px);
    overflow-y: auto !important;
}

.initial--44 ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 1px #cfcfcf;
    /*border-radius: 5px;*/
}

.initial--44 ::-webkit-scrollbar {
    width: 3px;
}

.initial--44 ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    /*border-radius: 5px;*/
}

.initial--44 ::-webkit-scrollbar-thumb:hover {
    background: #fc6a57;
}

.initial--44 .deco-none {
    color: inherit;
    text-decoration: inherit;
}

.initial--44 .qcont {
    text-transform: lowercase;
}

.initial--44 .qcont:first-letter {
    text-transform: capitalize;
}

.initial--44 .navbar-vertical .nav-link {
    color: #ffffff;
}

.initial--44 .navbar .nav-link:hover {
    color: #5affba;
}

.initial--44 .navbar .active > .nav-link,
.initial--44 .navbar .nav-link.active,
.initial--44 .navbar .nav-link.show,
.initial--44 .navbar .show > .nav-link {
    color: #5affba;
}

.initial--44 .navbar-vertical .active .nav-indicator-icon,
.initial--44 .navbar-vertical .nav-link:hover .nav-indicator-icon,
.initial--44 .navbar-vertical .show > .nav-link > .nav-indicator-icon {
    color: #5affba;
}

.initial--44 .nav-subtitle {
    display: block;
    color: rgba(255, 251, 223, 0.568627451);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03125rem;
}

.initial--44 .navbar-vertical .navbar-nav.nav-tabs .active .nav-link,
.initial--44 .navbar-vertical .navbar-nav.nav-tabs .active.nav-link {
    border-inline-start-color: #5affba;
}

.initial--44 .item-box {
    height: 250px;
    width: 150px;
    padding: 3px;
}

.initial--44 .header-item {
    width: 10rem;
}

.initial--44 .cursor-pointer {
    cursor: pointer;
}

.table {
    width: 100%;
}

.select2-selection__rendered span {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-line-clamp: 1;
}

.empty--data {
    text-align: center;
    padding: 20px;
}

.empty--data img {
    max-width: 145px;
    margin-bottom: 20px;
}

.empty--data h5 {
    text-transform: capitalize;
}

@media (min-width: 992px) and (max-width: 1450px) {
    .order--pos-left {
        width: calc(100% - 395px);
    }
    .order--pos-right {
        max-width: 375px;
    }
}
.w-160px {
    width: 160px;
}

.p-10px {
    padding: 10px;
}

.sidebar--badge-container {
    display: flex;
    flex-grow: 1;
    justify-content: space-between;
}

.sidebar--badge-container span:first-child {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
}

.sidebar--badge-container .badge {
    font-size: 10px;
    min-width: 25px;
    padding: 3px;
    font-weight: 700;
}

.form-control:focus {
    border-color: var(--primary-clr);
}

.input--group .form-control:first-child:focus {
    border-inline-start-color: var(--primary-clr);
}

#printableArea {
    justify-content: space-between;
}

.w-450px {
    width: 450px;
}

.dashed-border {
    border-top: 1px dashed #677788;
    margin: 5px 0 15px;
}

.invoice--inline-1 {
    font-size: 20px;
    font-weight: lighter;
    line-height: 1;
}

.lh--1 {
    line-height: 1 !important;
}

.lh--12 {
    line-height: 1.2;
}

.w-28p {
    width: 28%;
}

.mr-4rem {
    margin-inline-end: 4rem;
}

a.text-body span,
a.text-body div,
a.text--title {
    transition: all ease 0.3s;
}

a.text--title:hover,
a.text-body:hover,
a.text-body:hover div,
a.text-body:hover span {
    color: var(--primary-clr) !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
}

.nav-tabs .nav-link {
    color: var(--title-clr);
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: var(--primary-clr);
    background-color: transparent;
    border-color: transparent transparent var(--primary-clr);
    font-weight: 600;
}

.toggle-switch-input:checked:disabled + .toggle-switch-label {
    background-color: rgba(0, 134, 143, 0.3490196078);
}

textarea.form-control.h--45 {
    height: 45px;
}

.js-hs-unfold-invoker.dropdown-toggle:not(:hover) {
    color: #47566B !important;
    border-color: #99A7BA !important;
    border-radius: 6px !important;
}
.js-hs-unfold-invoker.dropdown-toggle:hover {
    color: #47566B !important;
    border-color: var(--primary-clr) !important;
    border-radius: 6px !important;
}

#location_map_canvas #pac-input,
#map-canvas #pac-input {
    height: 40px;
    border: 1px solid #fbc1c1;
    outline: none;
    box-shadow: none;
    top: 7px !important;
    transform: translateX(7px);
    padding-inline-start: 10px;
}

@media (min-width: 576px) {
    #location_map_canvas #pac-input,
    #map-canvas #pac-input {
        width: 200px;
    }
}
#location_map_canvas .gmnoprint,
#map-canvas .gmnoprint {
    transform: translateY(10px) !important;
}

#location_map_canvas .gmnoprint ~ div,
#map-canvas .gmnoprint ~ div {
    transform: translateY(10px) !important;
}

#map #pac-input {
    height: 40px;
    border: 1px solid #fbc1c1;
    outline: none;
    box-shadow: none;
    top: 7px !important;
    transform: translateX(7px);
    padding-inline-start: 10px;
}

@media (min-width: 576px) {
    #map #pac-input {
        width: 200px;
    }
}
.table-rest-info .font-light {
    color: #6d8c7e;
}

@media (max-width: 575px) {
    .statistics--title,
    .statistics--select {
        margin-bottom: 10px;
        flex-grow: 1;
    }
    [name="user_overview"] {
        flex-grow: 1;
        margin-inline-start: 5px;
        font-size: 13px;
        max-width: 140px;
        height: 40px;
        padding: 0 10px;
    }
    .footer {
        padding-bottom: 20px;
    }
    .footer .row {
        margin: 0;
        display: block;
    }
    .footer .row > div {
        width: 100%;
    }
    .footer .list-separator {
        width: 100%;
        justify-content: center;
        text-align: center;
    }
    .footer .list-separator .list-inline-item a {
        font-size: 13px;
    }
    .footer .list-inline-item:last-child {
        margin-bottom: 10px;
        display: block;
        flex-grow: 1;
        text-align: center;
        margin-top: 5px;
    }
    .order-invoice-right .btn--danger {
        padding: 5px 10px;
        width: unset;
        font-size: 11px;
    }
    /* .order-invoice-right .btn-outline-primary {
        padding: 5px 10px;
        width: unset;
        font-size: 11px;
    } */
    .max-sm-flex-grow-1 {
        flex-grow: 1;
    }
    .empty--data img,
    img.w-7rem {
        max-width: 5rem;
    }
}
/* .order-invoice-right .btn-outline-primary {
    padding: 5px 10px;
    width: unset;
    font-size: 11px;
} */

.page-area nav {
    overflow-x: auto;
    width: 100%;
}

@media (min-width: 992px) {
    .pagination {
        justify-content: flex-end;
    }
}
.review--table {
    min-width: 920px;
}

.max-sm-mw-200 {
    max-width: 350px;
}

.max-sm-w-200 {
    width: 350px;
}

@media (max-width: 575px) {
    .max-sm-mw-200 {
        max-width: 200px;
    }
    .max-sm-w-200 {
        width: 200px;
    }
    .role--table {
        min-width: 720px;
    }
}
@media (max-width: 450px) {
    .select--all-checkes .check-item {
        width: 100% !important;
        margin-top: 10px;
        margin-inline-start: -19px;
    }
}
.modal-body .rest-part .card-body {
    padding: 0;
}

.modal-body .rest-part .card-body .form-group {
    margin-bottom: 0;
}

.create-date {
    white-space: nowrap;
}

.search--button-wrapper .ml-auto {
    margin-inline-start: auto !important;
}

.search--button-wrapper .mr-auto {
    margin-inline-end: auto !important;
}

.delivery--information-single .tio-poi {
    margin-inline-end: 5px;
}

.img-responsive-1 {
    width: 130px;
    height: 130px;
    -o-object-fit: cover;
    object-fit: cover;
}

.table {
    font-size: 13px;
}

*,
body,
table th {
    font-family: "Inter", sans-serif;
}

strong {
    font-weight: 500;
}

@media (min-width: 1200px) {
    .min--260 {
        min-width: 260px;
    }
    .min--270 {
        min-width: 270px;
    }
    .min--280 {
        min-width: 280px;
    }
    .search--button-wrapper .search-form.min--280 {
        min-width: 280px;
    }
}
@media (max-width: 1199px) {
    .max-lg-h-40px {
        height: 40px !important;
    }
}
.customer--information-single span {
    word-break: break-all;
}

#shipping-address-modal #map #pac-input {
    inset-inline-start: 187px !important;
    top: 10px !important;
}

.media--sm .line--limit-1 {
    max-width: 160px;
}

.aspect-ratio-1 {
    -o-object-fit: cover;
    object-fit: cover;
    aspect-ratio: 1;
}

@media (max-width: 767px) {
    .tio-chart-bar-4.fz--40px {
        font-size: 24px !important;
    }
}
.card-header-title ~ .badge-soft-primary {
    margin: 0;
}

@media (max-width: 450px) {
    .card-header-title ~ .badge-soft-primary {
        margin: 10px 0;
    }
}
.page-header-tabs .nav-item .nav-link.active {
    position: relative;
    padding-inline-start: 0;
    padding-inline-end: 0;
    margin-inline-start: 7px;
    margin-inline-end: 7px;
}

.page-header-tabs .nav-item .nav-link.active::before {
    content: "";
    position: absolute;
    inset-inline-start: 0;
    width: 100%;
    bottom: 5px;
    height: 3px;
    border-radius: 4px;
    background: var(--primary-clr);
}

.page-header-tabs .nav-item .nav-link.text-title.active::before {
    background: var(--title-clr);
    opacity: 0.8;
}

.page-header-tabs .nav-item:first-child .nav-link.active {
    margin-inline-start: 0;
}

.page-header-tabs .nav-item:last-child .nav-link.active {
    margin-inline-end: 0;
}

.resturant-type-group {
    display: flex;
    flex-wrap: wrap;
    border-radius: 5px;
    align-items: center;
    padding: 4px 10px;
    min-height: 45px;
}

.restaurant-type-group {
    display: flex;
    flex-wrap: wrap;
    border-radius: 5px;
    align-items: center;
    padding: 4px 10px;
    min-height: 45px;
}

.schedult-date-content .d-inline-flex {
    flex-wrap: wrap;
}

.avatar-lg {
    -o-object-fit: cover;
    object-fit: cover;
}

.border-primary {
    border-color: var(--info-clr) !important;
}

.print--invoice {
    max-width: 366px;
    margin: 0 auto;
    padding: 25px 18px;
    font-weight: 400;
}

.print--invoice * {
    font-weight: 400;
}

.print--invoice strong {
    font-weight: 500;
}

.print--invoice h5 {
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
}

.print--invoice .store-name {
    text-transform: uppercase;
    font-size: 14px;
    line-height: 20px;
    font-weight: 700 !important;
    margin-bottom: 4px;
    color: #000000;
}

.print--invoice .store-name ~ div {
    font-size: 10px;
    line-height: 18px;
    color: #000000;
}

.print--invoice .invoice-logo {
    width: 36px;
    margin-bottom: 14px;
}

.print--invoice .top-info > div {
    font-weight: 500;
    text-align: center;
    color: #000000;
    font-size: 10px;
    line-height: 18px;
    text-transform: capitalize;
}

.print--invoice .top-info img {
    /* height: 10px; */
    -o-object-fit: cover;
    object-fit: cover;
}

/* .print--invoice .top-info .copyright {
    margin-top: 10px;
} */

.print--invoice .top-info .text-uppercase {
    letter-spacing: 1.04px;
    font-weight: 700;
    padding-top: 8px;
    padding-bottom: 6px;
    font-size: 14px;
    line-height: 18px;
}

.order-info-id {
    font-weight: 500;
    font-size: 10px;
    line-height: 14px;
    color: #000000;
}

.order-info-id * {
    color: #000000;
}

.order-info-id h5 {
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.04em;
    color: #000000;
    margin-top: 15px;
    margin-bottom: 4px;
}

.order-info-details {
    font-size: 10px;
    line-height: 18px;
    letter-spacing: -0.05em;
    color: #000000;
    text-transform: capitalize;
}

.order-info-details span {
    color: #000000;
}

.invoice--table tr th {
    border-bottom: none;
    border-top: none;
    padding: 10px 0;
    text-transform: capitalize;
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
    color: #000000;
}

.fs-9 {
    font-size: 9px !important;
}

.invoice--table tr th:last-child {
    text-align: end;
}

.invoice--table tr td {
    border-top: none;
    font-size: 10px;
    line-height: 16px;
    color: #000000;
    padding: 0.6rem 0.75rem;
}

.invoice--table tr td:first-child {
    padding-inline-start: 0;
}

.invoice--table tr td:last-child {
    padding-inline-end: 0;
    text-align: end;
}

.invoice--table tr td .addons {
    letter-spacing: 0.04em;
    color: #000000;
}

.invoice--table .price {
    letter-spacing: 0.04em;
    color: #000000;
}

.checkout--info {
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    text-align: end;
    color: #000000;
}

.checkout--info .border-top {
    padding-top: 10px;
}

.checkout--info .border-top * {
    font-weight: 600;
}

.checkout--info dt {
    text-align: start;
    font-size: 10px;
    line-height: 18px;
    color: #000000;
    margin-bottom: 7px;
}

.checkout--info dd {
    font-size: 10px;
    line-height: 18px;
    font-weight: 500;
    margin-bottom: 7px;
}

.checkout--info .total {
    font-weight: 700;
    font-size: 12px;
    line-height: 18px;
    color: #000000;
}

.checkout--info .copyright {
    color: #000000;
}

.non-printable {
    white-space: break-spaces;
}

@media (max-width: 444px) {
    .non-printable {
        margin-bottom: 15px;
    }
}
/*Login CSS*/
.auth-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.auth-wrapper * {
    font-family: "Roboto", sans-serif;
}

.auth-wrapper-right {
    width: 100%;
    max-width: 660px;
    background: #fff;
    min-height: 100vh;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
}

.auth-wrapper-right .auth-wrapper-form {
    max-width: 452px;
    width: 100%;
    margin: 0 auto;
}

@media (min-width: 1550px) {
    .auth-wrapper-right .auth-wrapper-form {
        transform: translateX(-30px);
    }
}

@media print {
    .print--invoice {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* .img-wrap svg, */
    .img-wrap img {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        display: block !important;
        max-width: 100% !important;
        height: auto !important;
        visibility: visible !important;
        padding: 10px 0;
    }
}

.auth-wrapper-right .auth-header {
    font-size: 22px;
    line-height: 1.4;
    color: #a0a0a0;
    margin-bottom: 60px;
    font-weight: 300;
}

.auth-wrapper-right .auth-header a {
    font-weight: 700;
    text-decoration: underline;
    color: #039d55;
}

.auth-wrapper-right .auth-header .title {
    font-size: 40px;
    font-weight: 700;
    margin-bottom: 10px;
}

.auth-wrapper-right .auth-header .badge {
    font-weight: 400;
    font-size: 14px;
    color: #039d55;
    background-color: rgba(3, 157, 85, 0.1);
    border-radius: 15px;
    padding: 7px 13px 6px;
}

.auth-wrapper-right .auto-fill-data-copy {
    padding-top: 26px;
}

.auth-wrapper-right .auto-fill-data-copy .action-btn {
    width: 45px;
    height: 45px;
}

.auth-wrapper-right .btn--primary {
    background: #039d55;
    font-weight: 500;
}

.auth-wrapper-right .btn-block {
    height: 45px !important;
    padding-top: 4px;
    padding-bottom: 4px;
    border-radius: 10px;
}

@media (min-width: 1550px) {
    .auth-wrapper-right {
        max-width: 718px;
    }
    .auth-wrapper-right .input-label {
        margin-bottom: 12px;
    }
    .auth-wrapper-right .form-group {
        margin-bottom: 20px;
    }
    .auth-wrapper-right .auto-fill-data-copy {
        color: #9b9b9b;
        font-size: 18px;
    }
    .auth-wrapper-right .btn-block,
    .auth-wrapper-right .form-control {
        height: 45px !important;
        border-radius: 5px;
    }
    .auth-wrapper-right .btn-block:not(.btn-white) {
        font-size: 20px;
    }
    .auth-wrapper-right .auto-fill-data-copy .action-btn {
        width: 45px;
        height: 45px;
        border-radius: 10px;
    }
    .auth-wrapper-right .auto-fill-data-copy .action-btn i {
        font-size: 24px;
    }
}
.auth-wrapper-left {
    width: 0;
    flex-grow: 1;
    background: url(./images/auth-bg.png) no-repeat center left/cover;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.auth-wrapper-left .auth-left-cont {
    margin-inline-start: auto;
    margin-inline-end: 172px;
}

.auth-wrapper-left .auth-left-cont img {
    max-width: 240px;
    height: 65px;
    margin-bottom: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: left center;
    object-position: left center;
}

.auth-wrapper-left .auth-left-cont .title {
    font-size: 70px;
    line-height: 1.3;
    font-weight: 300;
}

.auth-wrapper-left .auth-left-cont .title strong {
    font-weight: 700;
}

@media (max-width: 1549px) {
    .auth-wrapper-left .auth-left-cont {
        margin-inline-end: 134px;
    }
    .auth-wrapper-left .auth-left-cont .title {
        font-size: 55px;
    }
    .auth-wrapper-right .auth-header {
        font-size: 16px;
        margin-bottom: 35px;
    }
    .auth-wrapper-right .auth-header .title {
        font-size: 36px;
        margin-bottom: 0;
    }
    .auth-wrapper-right .auth-header .badge {
        margin-top: 10px;
    }
    .auth-wrapper .mt-xxl-3 {
        margin-top: 0 !important;
    }
    .auth-wrapper-right .btn--primary {
        height: 38px;
    }
}
@media (max-width: 1300px) {
    .auth-wrapper-left .auth-left-cont {
        margin-inline-end: 84px;
    }
    .auth-wrapper-left .auth-left-cont img {
        max-width: 90px;
    }
    .auth-wrapper-left .auth-left-cont .title {
        font-size: 45px;
    }
    .auth-wrapper-right {
        max-width: 560px;
    }
    .auth-wrapper-right .auth-header {
        font-size: 14px;
        margin-bottom: 1rem;
    }
    .auth-wrapper-right .auth-header .title {
        font-size: 28px;
    }
    .auth-wrapper-right .auth-header .badge {
        font-size: 11px;
    }
    .auth-wrapper-right .form-group {
        margin-bottom: 1rem;
    }
    .auth-wrapper-right .form-control {
        height: 38px;
        font-size: 12px;
    }
    .auth-wrapper-right .btn-block {
        height: 38px !important;
    }
    .auth-wrapper-right .custom-control-label {
        font-size: 12px;
    }
    .auth-wrapper-right .auto-fill-data-copy {
        padding-top: 10px;
    }
    .auth-wrapper-right .auto-fill-data-copy .action-btn {
        width: 35px;
        height: 35px;
    }
}
@media (max-width: 1199px) {
    .auth-wrapper-right {
        max-width: 450px;
        padding: 20px 24px;
    }
}
@media (max-width: 991px) {
    .auth-wrapper-left {
        display: none;
    }
    .auth-wrapper-right {
        padding: 25px 24px;
        max-width: 100%;
    }
}
@media (max-width: 767px) {
    .auth-wrapper-right {
        padding: 15px;
        max-width: 100%;
    }
}
.text--039D55 {
    color: #039d55 !important;
}

@media (min-width: 1650px) {
    .vendor-login .auth-wrapper-right .auth-header {
        font-size: 20px;
    }
    .vendor-login .auth-wrapper-right .auth-header .title {
        font-size: 36px;
    }
}
.toggle-login {
    text-align: center;
    margin-top: 15px;
}

.toggle-login .toggle-login-btn {
    cursor: pointer;
    color: #039d55;
}

a:hover .text--title {
    color: var(--primary-clr) !important;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* body .navbar-vertical-content *:not([class*="tio"]) {
    font-family: "Inter", sans-serif !important;
} */
.object-cover {
    -o-object-fit: cover;
    object-fit: cover;
}

.min-h-90px {
    min-height: 100px !important;
}

.btn-primary:active,
.btn-primary:hover,
.btn-primary:focus {
    color: #fff;
    background-color: #00868f;
    border-color: #00868f;
    filter: brightness(0.9);
}

hr.non-printable {
    margin-bottom: 0;
}

.print--invoice .text-center.pt-4 {
    padding-top: 0 !important;
}

.btn-primary:active {
    background-color: #00868f !important;
    border-color: #00868f !important;
}

.invoice-page,
.print--invoice {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-inline-start: 0 !important;
}

.invoice-page *,
.print--invoice * {
    font-family: "Roboto Mono", monospace !important;
}

.invoice-page .table *,
.print--invoice .table * {
    color: #000000;
}

.printable {
    display: block;
}

@media print {
    .non-printable {
        display: none;
    }
    .print--invoice {
        max-width: 410px !important;
        width: 100% !important;
    }
}
.invoice-page {
    padding: 0 !important;
}

.report-page-header .page-header-icon {
    width: 38px;
}

.store-report-content {
    display: flex;
    flex-wrap: wrap;
    gap: 11px;
}

.store-report-content .left-content,
.store-report-content .right-content {
    width: 265px;
    flex-grow: 1;
}

.store-report-content .center-chart-area {
    flex-grow: 1;
    width: 520px;
    padding: 24px;
    background: #ffffff;
    box-shadow: 0px 10px 13px rgba(17, 38, 146, 0.05);
    border-radius: 8px;
}

.store-report-content .center-chart-area .center-chart-header {
    display: flex;
    gap: 15px;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 24px;
}

.store-report-content .center-chart-area .center-chart-header .title {
    font-weight: 500;
    font-size: 19px;
    line-height: 175%;
    color: #232d42;
    margin: 0;
}

.store-report-content .center-chart-area .center-chart-header .subtitle {
    font-weight: 500;
    font-size: 14px;
    line-height: 175%;
    color: #232d42;
    margin: 0;
}

@media (max-width: 575px) {
    .store-report-content .center-chart-area .center-chart-header {
        gap: 5px;
    }
}
@media (max-width: 1499px) {
    .store-report-content .center-chart-area {
        width: 450px;
    }
}
@media (max-width: 1335px) {
    .store-report-content .center-chart-area {
        width: 380px;
    }
}
@media (max-width: 1255px) {
    .store-report-content .center-chart-area {
        width: 300px;
    }
}
@media (max-width: 991px) {
    .store-report-content {
        flex-wrap: wrap;
    }
    .store-report-content .left-content,
    .store-report-content .center-chart-area {
        width: 100%;
    }
}
.store-report-content .left-content {
    gap: 11px;
    display: flex;
    flex-wrap: wrap;
}

.store-report-content .left-content-card {
    background: #ffffff;
    box-shadow: 0px 10px 13px rgba(17, 38, 146, 0.1);
    border-radius: 5px;
    padding: 20px 30px;
    flex-grow: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
}

.store-report-content .left-content-card img {
    width: 35px;
}

.store-report-content .left-content-card .info {
    width: 0;
    flex-grow: 1;
    padding-inline-start: 21px;
}

.store-report-content .left-content-card .info .subtitle {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    font-family: "Roboto", sans-serif;
}

.store-report-content .left-content-card .info .subtext {
    font-weight: 600;
    font-size: 12px;
    line-height: 14px;
}

.store-report-content .left-content-card .info .info-txt {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #00aa6d;
    text-align: end;
    gap: 5px;
}

@media (min-width: 992px) and (max-width: 1280px) {
    .store-report-content .left-content-card {
        padding: 20px;
    }
    .store-report-content .left-content-card .info .subtitle {
        font-size: 24px;
        line-height: 28px;
    }
}
@media (max-width: 991px) {
    .store-report-content .left-content {
        flex-direction: row;
    }
    .store-report-content .left-content-card {
        width: 100%;
    }
}
@media (max-width: 767px) {
    .store-report-content .left-content-card {
        width: 100%;
    }
}
@media (max-width: 374px) {
    .store-report-content .left-content-card {
        padding: 20px;
    }
    .store-report-content .left-content-card .info .subtitle {
        font-size: 24px;
        line-height: 28px;
    }
}
.pie-chart .apexcharts-canvas {
    margin: 0 auto;
}

.pie-chart .apexcharts-legend .apexcharts-legend-series {
    margin-top: 10px !important;
}

@media (max-width: 345px) {
    .pie-chart #dognut-pie {
        transform: translateX(-5px);
    }
}
@media (max-width: 335px) {
    .pie-chart #dognut-pie {
        transform: translateX(-10px);
    }
}
.apex-legends {
    color: #758590;
    display: flex;
    flex-wrap: wrap;
    max-width: 230px;
    margin: 0 auto;
}

.apex-legends div {
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.apex-legends div::before {
    width: 9px;
    border-radius: 50%;
    height: 9px;
    content: "";
    margin-inline-end: 6px;
}

.apex-legends div.before-bg-107980::before {
    background: #107980;
}

.apex-legends div.before-bg-56B98F::before {
    background: #56b98f;
}

.apex-legends div.before-bg-E5F5F1::before {
    background: #e5f5f1;
}

.apex-legends div:not(:last-child) {
    margin-inline-end: 15px;
}

.total--orders {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.total--orders h3 {
    margin: 0;
    font-weight: 700 !important;
    font-size: 19px;
    line-height: 23px;
    color: #334257 !important;
}

@media screen and (max-width: 575px) {
    .total--orders h3 {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 5px;
    }
}
@media screen and (min-width: 1650px) {
    .total--orders h3 {
        font-size: 24px;
        line-height: 24px;
    }
}
.text-006AE5 {
    color: #006ae5;
}

.text-0661CB {
    color: #0661cb !important;
}

.text-00A3FF {
    color: #00a3ff !important;
}

.text-F5A200 {
    color: #f5a200 !important;
}

.text-56b98f {
    color: #56b98f !important;
}

.payment-statistics-shadow {
    background: #ffffff;
    box-shadow: 0px 10px 13px rgba(17, 38, 146, 0.1);
    border-radius: 5px;
    border: none;
}

.store-center-chart {
    height: 340px !important;
}

@media (max-width: 1650px) {
    .store-center-chart {
        height: 300px !important;
    }
}
@media (max-width: 575px) {
    .store-center-chart {
        height: 250px !important;
    }
}
@media screen and (min-width: 1200px) and (max-width: 1255px) {
    .store-center-chart {
        height: 250px !important;
    }
}
.mt-11px {
    margin-top: 11px;
}

.white--space-false th {
    white-space: nowrap;
}

.filter--card {
    background: #ffffff;
    box-shadow: 0px 6px 12px -3px rgba(51, 66, 87, 0.05);
    border-radius: 5px;
    border: none;
}

.filter--card .form-label {
    font-size: 16px;
    font-weight: 600;
}

.earning-statistics-content {
    text-align: center;
}

.earning-statistics-content .subtitle {
    font-weight: 500;
    font-size: 16px;
    line-height: 175%;
    color: #232d42;
    margin-bottom: 18px;
}

.earning-statistics-content .title {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    color: #334257;
    padding-bottom: 11px;
    display: inline-block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
}

.earning-statistics-content .subtxt {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #8a92a6;
    margin-bottom: 30px;
}

@media (min-width: 1440px) {
    .earning-statistics-content .subtitle {
        font-size: calc(16px + 0.1vw);
    }
    .earning-statistics-content .title {
        font-size: calc(28px + 0.1vw);
    }
    .earning-statistics-content .subtxt {
        font-size: calc(12px + 0.1vw);
    }
}
.trx-btn {
    padding: 10px 20px;
    font-weight: 600;
    font-size: 12px;
    line-height: 15px;
    display: inline-block;
    border: 0.5px solid #0177cd;
    border-radius: 20px;
    transition: all ease 0.3s;
}

.trx-btn:not(:hover) {
    color: #47566b;
}

.trx-btn:hover {
    background: var(--primary-clr) !important;
    border-color: var(--primary-clr) !important;
    color: #fff;
}

.coupon__discount {
    font-family: "Roboto", sans-serif;
    color: #9eadc1;
}

.coupon__discount strong {
    color: #006ae5;
    font-weight: 700;
}

@media (1200px < width < 1450px) {
    .coupon__discount {
        font-size: 12px;
    }
}
.text-sm {
    font-size: 12px;
}

.__txt-22 {
    font-weight: 700;
    font-size: 22px !important;
}

.__txt-14px {
    font-size: 14px;
    color: #334257;
}

.__txt-14px thead th {
    font-size: 14px;
}

.__badge-sm {
    line-height: 19px;
    padding-top: 0;
    font-size: 12px;
    padding-bottom: 0;
}

.__nav-tabs-menu {
    padding-top: 10px;
    row-gap: 0px;
    -moz-column-gap: 16px;
    column-gap: 30px;
}

.__nav-tabs-menu .nav-item .nav-link {
    padding-inline-start: 0;
    padding-inline-end: 0;
    padding-top: 0;
}

.statistics-btn-grp {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.statistics-btn-grp label {
    margin: 0;
    display: block;
}

.statistics-btn-grp label span {
    font-size: 12px;
    color: var(--title-clr);
    text-transform: capitalize;
    border-radius: 3px;
    cursor: pointer;
    padding: 5px 15px;
    display: block;
    background: #f2f2f2;
}

.statistics-btn-grp label input:checked ~ span {
    background: #00868f;
    color: #fff;
    font-weight: 700;
}

a:hover .text--title {
    color: var(--primary-clr) !important;
}

.refund--btn {
    margin: 0;
    gap: 16px;
    flex-wrap: nowrap;
}

.refund--btn * {
    margin: 0;
    width: calc(50% - 8px);
}

@media (575px > width) {
    .refund--btn {
        flex-wrap: wrap;
    }
    .refund--btn * {
        flex-grow: 1;
        width: auto;
    }
}
@media (992px < width < 1460px) {
    .refund--btn {
        flex-wrap: wrap;
    }
    .refund--btn * {
        flex-grow: 1;
        width: auto;
    }
}
.img__aspect-1 {
    aspect-ratio: 1 !important;
    -o-object-fit: cover;
    object-fit: cover;
}

ul.delivery--information-single {
    padding: 0;
    margin: 0;
}

ul.delivery--information-single li {
    width: 100%;
    display: flex;
    align-items: flex-start;
}

ul.delivery--information-single li > span {
    display: block;
}

ul.delivery--information-single li .info {
    width: 0;
    flex-grow: 1;
    padding-inline-start: 15px;
}

ul.delivery--information-single li .name {
    width: 110px;
    text-align: start;
}

ul.delivery--information-single li:not(:last-child) {
    margin-bottom: 6px;
}

.__badge {
    line-height: 15px;
    font-size: 12px;
    font-weight: 400;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-inline-end: 10px;
    padding-inline-start: 10px;
    border-radius: 15px;
}

.__badge-abs {
    position: absolute;
    inset-inline-end: 15px;
    top: 24px;
}

.__page-header-txt {
    max-width: 740px;
    padding-inline-start: 32px;
    font-size: 14px;
    margin-top: -6px;
    padding-bottom: 10px;
}

.max-w-180px {
    max-width: 180px;
}

.white--space-initial {
    white-space: initial;
}

.navbar .nav-link .text-truncate {
    text-transform: capitalize;
}

/*Module CSS*/
.navbar {
    border: none;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
}

.__navbar-nav {
    -moz-column-gap: 38px;
    column-gap: 38px;
}

@media (max-width: 1750px) {
    .__navbar-nav {
        -moz-column-gap: 0;
        column-gap: 0;
    }
}
@media (max-width: 991px) {
    .__navbar-nav {
        -moz-column-gap: 25px;
        column-gap: 25px;
    }
}
@media (max-width: 575px) {
    .__navbar-nav {
        -moz-column-gap: 10px;
        column-gap: 10px;
        padding-inline-start: 8px;
    }
    .__navbar-nav li {
        flex-grow: 1;
    }
    .__navbar-nav li a:not(.module--nav-icon) {
        padding-inline-start: 0 !important;
        padding-inline-end: 0 !important;
        margin: 0 !important;
    }
}
.__nav-item {
    position: relative;
    margin-inline-end: 0 !important;
}

.__nav-item .__nav-link {
    font-size: 14px;
    line-height: 19px;
    font-weight: 500;
    padding: 22px 15px;
    position: relative;
    white-space: nowrap;
}

.__nav-item .__nav-link span {
    color: #006161;
    margin: 0 8px;
}

.__nav-item .__nav-link::before {
    height: 3px;
    content: "";
    position: absolute;
    inset-inline-start: 0;
    bottom: 0;
    inset-inline-end: 0;
    background: #009faa;
    border-radius: 1.5px;
    opacity: 0;
}

.__nav-item .__nav-link.active::before {
    opacity: 1;
}

@media (min-width: 768px) and (max-width: 1575px) {
    .__nav-item {
        margin-inline-end: 0 !important;
    }
    .__nav-item .__nav-link {
        padding: 22px 8px;
        font-size: 12px;
    }
    /* .__nav-item .__nav-link img {
        margin-inline-end: 4px;
    } */
}
@media screen and (width < 992px) {
    .__nav-item .__nav-link {
        padding: 22px 12px;
    }
    .__nav-item .__nav-link svg,
    .__nav-item .__nav-link span {
        display: none;
    }
    .__nav-item .__nav-link svg:first-child {
        display: block;
        width: 20px;
    }
}
.__nav-item .__nav-link:focus ~ .__nav-module {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.__nav-item:hover .__nav-module {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.__nav-module {
    position: absolute;
    inset-inline-end: -30px;
    width: 329px;
    top: calc(100% + 35px);
    background: #fff;
    border-radius: 5px;
    transition: all ease 0.4s;
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    z-index: 99;
}

.__nav-module:hover {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.__nav-module-header {
    border-radius: 5px 5px 0 0;
    background: url(./images/setting-shape.png) no-repeat center center/cover;
    position: relative;
    z-index: 3;
}

.__nav-module-header .inner {
    background: rgba(0, 97, 97, 0.8);
    border-radius: 5px 5px 0px 0px;
    padding: 20px;
    color: #fff;
    font-size: 12px;
}

.__nav-module-header .inner h4 {
    font-size: 20px;
    text-transform: capitalize;
    margin-bottom: 3px;
    line-height: 27px;
    color: #fff;
}

@media (max-width: 374px) {
    .__nav-module-header .inner h4 {
        font-size: 18px;
    }
}
.__nav-module-header .inner p {
    font-family: "Inter", sans-serif;
    max-width: 226px;
    margin: 0;
}

.__nav-module-body {
    padding: 15px 15px 20px;
    background: white;
    border-radius: 0 0 5px 5px;
    border: 1px solid rgba(0, 159, 170, 0.2);
    border-top: none;
    box-shadow: 0px 0px 15px -3px rgba(0, 0, 0, 0.1);
    max-height: 380px;
    overflow-y: auto;
}

.__nav-module-body ul {
    padding: 0;
    margin: 0;
}

.__nav-module-body ul li {
    list-style: none;
}

.__nav-module-body ul li a {
    line-height: 19px;
    padding: 10px;
    display: inline-block;
    color: #334257;
}

.__nav-module-body ul li a:hover {
    color: var(--base-clr);
}

.__nav-module-body ul li a svg,
.__nav-module-body ul li a img {
    margin-inline-end: 15px;
}

.__nav-module.style-2 {
    inset-inline-end: 15px;
    top: calc(100% + 18px);
    box-shadow: 0px 0px 15px -3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 576px) {
    .__nav-module.style-2 {
        width: 389px;
    }
}
.__nav-module.style-2 .__nav-module-header {
    background: url(./images/module-shape.png) no-repeat center center/cover;
    border: 1px solid rgba(0, 159, 170, 0.2);
    border-bottom: none;
}

.__nav-module.style-2 .__nav-module-header .inner {
    background: rgba(255, 255, 255, 0.8);
    color: #009faa;
}

.__nav-module.style-2 .__nav-module-header .inner h4 {
    color: #006161;
}

@media (max-width: 991px) {
    .__nav-module {
        inset-inline-end: -125px;
    }
}
@media (max-width: 767px) {
    .__nav-module {
        inset-inline-end: -140px;
    }
}
@media (max-width: 420px) {
    .__nav-module {
        inset-inline-end: -140px;
    }
    .__nav-module.style-2 {
        inset-inline-end: 10px;
    }
}
@media (max-width: 374px) {
    .__nav-module {
        inset-inline-end: -148px;
        max-width: 300px;
    }
    .__nav-module.style-2 {
        inset-inline-end: 10px;
    }
}
.__nav-module-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    /* justify-content: space-between; */
}

.__nav-module-items .__nav-module-item {
    width: calc(33.3333333333% - 10px);
    background: #ffffff;
    border: 1px solid rgba(0, 159, 170, 0.2);
    border-radius: 5px;
    min-height: 90px;
    text-align: center;
    font-size: 12px;
    line-height: 13px;
    font-weight: 600;
    padding: 18px 10px 10px;
    color: #00868f;
    white-space: initial;
    word-break: break-all;
}

.__nav-module-items .__nav-module-item img {
    max-height: 30px;
    margin: 0 auto 8px;
}

.__nav-module-items .__nav-module-item img *[fill="black"] {
    fill: #00868f;
}

.__nav-module-items .__nav-module-item.active {
    background: #006161;
    color: #fff;
}

.__nav-module-items .__nav-module-item.active img *[fill="black"] {
    fill: #ffffff;
}

@media screen and (min-width: 768px) {
    .__nav-module-items {
        -moz-column-gap: 25px;
        column-gap: 25px;
        row-gap: 20px;
        padding: 11px;
    }
    .__nav-module-items .__nav-module-item {
        width: calc(33.3333333333% - 16.6666666667px);
    }
}
@media (max-width: 374px) {
    .__nav-module-items {
        gap: 8px;
    }
    .__nav-module-items .__nav-module-item {
        width: calc(33.3333333333% - 5.5px);
    }
    .__nav-module-items .__nav-module-item img {
        width: 32px;
    }
}
.__nav-module-checkboxes {
    font-size: 12px;
    font-weight: 600;
    padding: 5px 15px;
    margin: 0;
    justify-content: center;
}

.__nav-module-checkboxes span {
    color: #009faa;
}

.__nav-module-checkboxes .form-check-input {
    display: none;
}

.__nav-module-checkboxes .form-check-label {
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(1px);
    cursor: pointer;
}

.__nav-module-checkboxes .form-check-label::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 1px solid #009faa;
    border-radius: 4px;
}

.__nav-module-checkboxes .form-check-label span {
    padding-inline-start: 7px;
}

.__nav-module-checkboxes .form-check-input:checked ~ .form-check-label::before {
    background: url("./images/check-mark-2.png") #009faa no-repeat center
        center/15px;
}

.__nav-module-checkboxes .form-check {
    margin: 5px 0 20px;
}

@media (max-width: 374px) {
    .__nav-module-checkboxes .form-check {
        margin: 0px 0px 15px;
    }
}
.pie-chart .apexcharts-canvas {
    margin: 0 auto;
}

.pie-chart .apexcharts-legend .apexcharts-legend-series {
    margin-top: 10px !important;
}

@media (max-width: 345px) {
    .pie-chart #dognut-pie {
        transform: translateX(-5px);
    }
}
@media (max-width: 335px) {
    .pie-chart #dognut-pie {
        transform: translateX(-10px);
    }
}
.p-10px {
    padding: 10px !important;
}

.grid--card {
    display: flex;
    align-items: flex-start;
    flex-wrap: nowrap;
}

.grid--card img {
    width: 49.12px;
    height: 49.12px;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid rgba(7, 59, 116, 0.3);
    border-radius: 5px;
}

.grid--card .cont {
    width: 0;
    flex-grow: 1;
    font-weight: 700;
    font-size: 13.75px;
    line-height: 15px;
    color: var(--title-clr);
    padding-inline-start: 9px;
    max-width: 184px;
    padding-inline-end: 9px;
}

.grid--card .cont span {
    color: #596779;
    font-weight: 400;
    font-size: 11.79px;
}

.grid--card .cont h6,
.grid--card .cont .fz--13 {
    font-weight: 700;
    font-size: 13.75px !important;
    line-height: 15px;
    color: var(--title-clr);
}

.grid--card .badge {
    padding: 9px 10px 7px;
    min-width: 85px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Inter", sans-serif;
    margin-top: 6px;
}

@media (max-width: 450px) {
    .grid--card .badge {
        padding: 9px 10px 7px;
        min-width: 0;
        font-size: 10px;
        margin-inline-start: 3px;
    }
}
.badge.badge-soft {
    background: rgba(16, 121, 128, 0.05);
    border-radius: 3px;
}

.top--selling .grid--card:not(:last-child) {
    margin-bottom: 19.65px;
}

.order-header-shadow {
    box-shadow: 0px 3px 3px rgba(51, 66, 87, 0.05);
}

.order-header-shadow .card-title {
    font-size: calc(13px + 0.051vw) !important;
}

.order-header-shadow .fz-12px {
    font-size: 11.87px;
    text-transform: capitalize;
}

.fz-12px {
    font-size: 12px !important;
}

.rated--products a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5.11px 15px 5.2px 5px;
    background: #f8f9fb;
    border-radius: 5px;
}

.rated--products a:not(:last-child) {
    margin-bottom: 8px;
}

.rated--products a .rated-media {
    width: 0;
    flex-grow: 1;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    color: var(--title-clr);
}

.rated--products a .rated-media img {
    width: 49.12px;
    height: 49.12px;
    border-radius: 5px;
    -o-object-fit: contain;
    object-fit: contain;
}

.rated--products a .rated-media span {
    padding-inline-start: 10px;
}

.rated--products a .rating {
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
    color: #ff6d6d;
}

.line--limit-1,
.line--limit-2,
.line--limit-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
}

.line--limit-1 {
    -webkit-line-clamp: 1;
}

.line--limit-2 {
    -webkit-line-clamp: 2;
}

.line--limit-3 {
    -webkit-line-clamp: 3;
}

.text-FF6D6D {
    color: #ff6d6d;
}

.__top-resturant {
    display: flex;
    flex-wrap: wrap;
    gap: 4.91px;
}

.__top-resturant a {
    display: block;
    width: calc(33.3333333333% - 3.3px);
    padding: 4.91px;
    background: #ffffff;
    box-shadow: 0px 4.91213px 9.82425px -2.94728px rgba(0, 0, 0, 0.1);
    position: relative;
    border-radius: 4.91px;
}

.__top-resturant a .info {
    font-weight: 700;
    font-size: 11.7891px;
    line-height: 16px;
    color: #ffffff;
    background: rgba(0, 97, 97, 0.8);
    border-radius: 0px 0px 4.91213px 4.91213px;
    padding: 4.91px;
    text-align: center;
    transition: all ease 0.3s;
    position: absolute;
    inset-inline-start: 0;
    bottom: 0;
    width: 100%;
    transform: translateY(110%);
}

.__top-resturant a img {
    width: 100%;
    height: 98.24px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 4.91px;
}

.__top-resturant a:hover .info {
    transform: translateY(0);
}

.__top-resturant-card {
    padding: 16.7px 14.74px;
}

@media (min-width: 1200px) {
    .col--xl-8 {
        width: calc(100% - 310px) !important;
        max-width: calc(100% - 310px) !important;
        flex-basis: 100% !important;
    }
    .col--xl-4 {
        width: 310px !important;
        max-width: 310px !important;
    }
}
@media (min-width: 1650px) {
    .col--xl-8 {
        width: calc(100% - 400px) !important;
        max-width: calc(100% - 400px) !important;
        flex-basis: 100% !important;
    }
    .col--xl-4 {
        width: 400px !important;
        max-width: 400px !important;
    }
}
#grow-sale-chart {
    height: 292px;
}

/* #grow-sale-chart .apexcharts-area-series .apexcharts-area {
  stroke: #00bf79;
}
#grow-sale-chart .apexcharts-marker {
  fill: #00bf79;
}
#grow-sale-chart .apexcharts-tooltip-marker {
  background: #00bf79 !important;
} */
#grow-sale-chart *[stroke="#e0e0e0"] {
    stroke: #ffffff;
}

.__chart-label {
    white-space: nowrap;
}

.__chart-label .indicator {
    width: 12.14px;
    height: 12.14px;
    margin-top: 2px;
}

@media (max-width: 500px) {
    .__chart-label {
        font-size: 13px;
    }
}
.__gross-amount {
    color: #8a92a6;
    font-weight: 400;
    font-size: 14.9359px;
    line-height: 175%;
}

.__gross-amount h6 {
    font-weight: 500;
    font-size: 17.7363px;
    line-height: 175%;
    color: #232d42;
    margin: 0;
}

@media (max-width: 500px) {
    .__gross-amount {
        font-size: 12px;
    }
    .__gross-amount h6 {
        font-size: 15px;
    }
}
.__gap-12px {
    gap: 12px;
}

@media (min-width: 1300px) {
    .move-left-100 {
        transform: translateX(-100px);
    }
    [dir="rtl"] .move-left-100 {
        transform: translateX(100px);
    }
}
.apexcharts-legend-marker {
    margin: 0;
    margin-inline-end: 5px;
}

.__dashboard-card {
    position: relative;
    padding-inline-start: 41.38px;
}

.__dashboard-card .meter {
    width: 7px;
    height: calc(100% - 20px);
    bottom: 10px;
    display: block;
    position: absolute;
    background: #d1edf3;
    border-radius: 10px;
    inset-inline-start: 14px;
}

.__dashboard-card .meter span {
    bottom: 0;
    inset-inline-start: 0;
    border-radius: 4px;
    background: #16abcb;
    position: absolute;
    width: 100%;
}

.__dashboard-card.card--bg-2 .meter {
    background: #ffe7b7;
}

.__dashboard-card.card--bg-2 .meter span {
    background: #feb019;
}

.__dashboard-card.card--bg-3 .meter {
    background: #c9ede0;
}

.__dashboard-card.card--bg-3 .meter span {
    background: rgba(0, 170, 109, 0.5);
}

.__dashboard-card.card--bg-4 .meter {
    background: #ffdbdb;
}

.__dashboard-card.card--bg-4 .meter span {
    background: #ff6d6d;
}

.print--invoice {
    margin: 0 auto !important;
}

@media print {
    .print--invoice {
        margin: 0 !important;
    }
}
.status-filter-wrap {
    border: 1px solid rgba(0, 134, 143, 0.5);
    border-radius: 5px;
    padding: 3px;
    margin: 20px 0;
}

.status-filter-wrap .statistics-btn-grp {
    gap: 0;
}

.status-filter-wrap .statistics-btn-grp span {
    background: transparent;
    line-height: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 14px;
    color: #00868f;
}

@media (max-width: 500px) {
    .status-filter-wrap .statistics-btn-grp span {
        padding: 5px 12px;
        font-size: 11px;
    }
}
.__dashboard-card-2 {
    padding: 25px 20px 25.81px;
    background: #ffffff;
    box-shadow: 2px 2px 10px rgba(58, 58, 58, 0.1);
    border-radius: 5px;
    text-align: center;
}

.__dashboard-card-2 img {
    margin-bottom: 12.21px;
    height: 36px;
    width: 36px;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: center center;
    object-position: center center;
}

.__dashboard-card-2 .name {
    font-weight: 600;
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 5px;
    color: #334257;
}

.__dashboard-card-2 .count {
    font-weight: 700;
    font-size: 28px;
    line-height: 33px;
    margin-bottom: 5px;
    color: #334257;
}

.__dashboard-card-2 .subtxt {
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    color: #9d9d9d;
}

.text-3F8CE8 {
    color: #3f8ce8;
}

.text-FFA800 {
    color: #ffa800;
}

.text-success {
    color: #00aa6d;
}

.text-danger {
    color: #ff6d6d;
}

.__sidebar-hs-unfold .js-hs-unfold-invoker {
    background: #83c4c9;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    display: block;
    padding: 10px 15px;
}

.__sidebar-hs-unfold .js-hs-unfold-invoker span {
    color: #393939;
}

.__sidebar-hs-unfold .cmn--media .card-title {
    color: #334257;
}

.__sidebar-hs-unfold .right-dropdown-icon::before {
    border: 4px solid #334257;
    border-top-color: transparent;
    border-inline-start-color: transparent;
    transform: rotate(45deg);
    inset-inline-end: 2px;
    inset-inline-start: unset !important;
}

.__sidebar-hs-unfold .dropdown-menu {
    border-radius: 10px !important;
}

.__sidebar-hs-unfold {
    position: sticky;
    bottom: 0;
}

.__nav-item .__nav-link.module--nav-icon {
    background: linear-gradient(180deg, #0177cd 0%, #0e4da3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    height: 3.75rem;
}

@media (min-width: 1400px) {
    .__nav-item .__nav-link.module--nav-icon {
        min-width: 195px;
    }
}
@media (min-width: 1200px) {
    .__nav-item .__nav-link.module--nav-icon {
        margin-inline-start: 10px !important;
    }
}
.__nav-item .__nav-link.module--nav-icon::before {
    display: none;
}

@media (max-width: 1399px) {
    .__nav-item .__nav-link.module--nav-icon {
        font-size: 12px;
    }
    .__nav-item .__nav-link.module--nav-icon svg:first-child,
    .__nav-item .__nav-link.module--nav-icon img:first-child {
        width: 20px;
    }
}
@media (max-width: 991px) {
    .__nav-item .__nav-link.module--nav-icon img,
    .__nav-item .__nav-link.module--nav-icon svg {
        margin: 0;
    }
}
@media (max-width: 575px) {
    .__nav-item .__nav-link.module--nav-icon {
        padding: 0 8px !important;
    }
    .__nav-item .__nav-link.module--nav-icon img,
    .__nav-item .__nav-link.module--nav-icon svg {
        width: 18px;
    }
}
.w-0 {
    width: 0;
}

@media screen and (width < 991px) {
    .nav-msg-icon img {
        width: 24px;
    }
}
@media (max-width: 575px) {
    .max-sm-m-0 {
        margin: 0 !important;
    }
}
.flex-grow {
    flex-grow: 1;
}

.__user-dashboard-card {
    padding: 15px;
    background: #ffffff;
    box-shadow: 0px 0px 15px rgba(0, 85, 85, 0.05);
    border-radius: 7.46793px;
    --theme-clr: #107980;
    transition: all ease 0.3s;
}

.__user-dashboard-card:hover {
    box-shadow: 0px 10px 15px rgba(0, 85, 85, 0.15);
}

.__user-dashboard-card .__user-dashboard-card-thumbs {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img {
    width: 35px;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #fff;
    border-radius: 50%;
    position: relative;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:not(:first-child) {
    margin-inline-start: -10px;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:nth-child(1) {
    z-index: 4;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:nth-child(2) {
    z-index: 3;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:nth-child(3) {
    z-index: 2;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:nth-child(4) {
    z-index: 1;
}

.__user-dashboard-card .__user-dashboard-card-thumbs img:nth-child(5) {
    z-index: 0;
}

.__user-dashboard-card .__user-dashboard-card-thumbs .more-icon {
    background: var(--theme-clr);
    width: 35px;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #fff;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    text-align: center;
    color: #ffffff;
    border-radius: 50%;
    line-height: 33px;
    z-index: 5;
}

.__user-dashboard-card
    .__user-dashboard-card-thumbs
    .more-icon:not(:first-child) {
    margin-inline-start: -10px;
}

.__user-dashboard-card .title {
    font-weight: 700;
    font-size: 26px;
    line-height: 35px;
    color: var(--theme-clr);
}

.__user-dashboard-card .subtitle {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    color: #334257;
}

.__customer-satisfaction {
    background: #ffffff;
    box-shadow: 0px 10px 15px rgba(0, 85, 85, 0.05);
    border-radius: 7.46793px;
    padding: 15px 11px 10px;
}

.__customer-satisfaction .subtitle,
.__customer-satisfaction .review-count {
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    color: #334257;
    margin-bottom: 18px;
}

.__customer-satisfaction .review-count {
    font-weight: 700;
    margin-bottom: 5px;
}

.__customer-satisfaction .review-received {
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: #596779;
}

.__customer-review {
    margin: 0;
    padding: 0;
    margin-top: 15px;
}

.__customer-review li {
    list-style: none;
    display: flex;
    background: #f8f9fb;
    border-radius: 5px;
    padding: 12px 15px;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #596779;
}

.__customer-review li:not(:last-child) {
    margin-bottom: 13px;
}

.__customer-review li .review {
    width: 0;
    flex-grow: 1;
    --clr: #e7eaf3;
    display: flex;
    gap: 5px;
    align-items: center;
}

.__customer-review li .review i {
    text-align: center;
    width: 12px;
    color: var(--clr);
}

@media (min-width: 1550px) {
    .__customer-review li .review {
        gap: 7px;
    }
}
.__customer-review li .tag {
    width: 67px;
}

@media (max-width: 1350px) and (min-width: 992px) {
    .__customer-review li {
        font-size: 11px;
    }
    .__customer-review li .tag {
        width: 52px;
    }
    .__customer-review li .review {
        gap: 3px;
    }
}
@media (max-width: 374px) {
    .__customer-review li {
        font-size: 11px;
    }
    .__customer-review li .tag {
        width: 52px;
    }
    .__customer-review li .review {
        gap: 3px;
    }
}
@media (width < 992px) {
    .order-max-lg-1 {
        order: -1;
    }
}
.__customer-statistics-card {
    --clr: #008958;
    padding: 17px 15px;
    background: #ffffff;
    box-shadow: 0px 10px 15px rgba(0, 85, 85, 0.05);
    border-radius: 7.46793px;
    min-height: 101px;
    transition: all ease 0.3s;
}

.__customer-statistics-card:hover {
    box-shadow: 0px 10px 15px rgba(0, 85, 85, 0.15);
}

.__customer-statistics-card .title {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    -moz-column-gap: 15px;
    column-gap: 15px;
}

.__customer-statistics-card .title h4,
.__customer-statistics-card .title {
    margin: 0;
    color: var(--clr);
    font-weight: 700;
    font-size: 26px;
    line-height: 35px;
}

.__customer-statistics-card .subtitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: #334257;
    margin: 0;
}

@media (992px < width < 1340px) {
    .__customer-statistics-card .subtitle {
        font-weight: 500;
        font-size: 12px;
    }
}
.gap__10 {
    margin: -5px !important;
}

.gap__10 > div {
    padding: 5px !important;
}

.map-warper.map-wrapper-2 {
    height: 305px;
    overflow: hidden;
    border-radius: 15px;
}

.sidebar-close-icon {
    padding: 10px 10px 0;
    display: inline-block;
}

.mb-n-5 {
    margin-bottom: -5px;
}

.text-A2C8C8 {
    color: #a2c8c8 !important;
}

.text-transform-unset {
    text-transform: unset !important;
}

.navbar-vertical-aside-has-menu > .nav-link-toggle::after {
    filter: brightness(5);
}

.input-date {
    position: relative;
    margin: 0;
    display: block;
    cursor: pointer;
}

.input-date::after {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    top: 50%;
    inset-inline-end: 20px;
    transform: translateY(-50%);
    background: url(./images/calendar.png) no-repeat center center/contain;
    z-index: 1;
}

.__card-1 {
    background: #f1f5f9;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: relative;
    border-radius: 10px;
    padding: 30px 25px;
}

.__card-1 .title {
    font-weight: 700;
    font-size: 32px;
    line-height: 44px;
    color: #f39200;
}

.__card-1 .subtitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    color: #334257;
}

.__card-1 .icon {
    width: 60px;
    height: 60px;
    -o-object-fit: contain;
    object-fit: contain;
    margin: 0 auto;
    margin-bottom: 14px;
}

.__card-3 {
    background: #f1f5f9;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: relative;
    border-radius: 10px;
    padding: 25px 25px 20px;
}

.__card-3 .title {
    font-weight: 700;
    font-size: 26px;
    line-height: 35px;
    margin-bottom: 5px;
}

.__card-3 .subtitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    color: #334257;
    max-width: 142px;
    margin-inline-start: auto;
    margin-inline-end: auto;
}

.__card-3 .icon {
    width: 85px;
    height: 85px;
    -o-object-fit: contain;
    object-fit: contain;
    margin: 0 auto;
    margin-bottom: 14px;
}

@media (max-width: 767px) {
    .__card-3 {
        padding: 25px 25px 20px;
    }
    .__card-3 .icon {
        width: 60px;
        height: 60px;
    }
    .__card-3 .title {
        font-size: 22px;
        line-height: 30px;
    }
}
.__card-3 .info-icon {
    position: absolute;
    inset-inline-end: 10px;
    top: 10px;
    width: 20px;
    height: 20px;
}

.__card-3 .info-icon img {
    width: 20px;
    margin: 0 !important;
    transform: translateY(0);
}

.__card-2 {
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    border-radius: 10px;
    padding: 30px 55px 28px 30px;
}

.__card-2 .title {
    font-weight: 700;
    font-size: 22px;
    line-height: 30px;
    margin-bottom: 5px;
}

.__card-2 .subtitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    color: #334257;
}

.__card-2 .card-icon {
    position: absolute;
    inset-inline-end: 15px;
    top: 15px;
    max-width: 30px;
    height: 30px;
    -o-object-fit: contain;
    object-fit: contain;
}

@media screen and (min-width: 1450px) {
    .__card-2 .card-icon {
        inset-inline-end: 20px;
        top: 25px;
    }
}
.__bg-1 {
    background: rgba(0, 224, 255, 0.1);
}

.__bg-1 .title {
    color: #006ab4;
}

.__bg-2 {
    background: #deefff;
}

.__bg-2 .title {
    color: #0097ff;
}

.__bg-3 {
    background: #defff3;
}

.__bg-3 .title {
    color: #008958;
}

.__bg-4 {
    background: #fff9db;
}

.__bg-4 .title {
    color: #a47000;
}

.__bg-5 {
    background: rgba(255, 117, 0, 0.05);
}

.__bg-5 .title {
    color: #ffa012;
}

.__bg-6 {
    background: rgba(255, 117, 0, 0.05);
}

.__bg-6 .title {
    color: #ff5a54;
}

.mb-20 {
    margin-bottom: 20px;
}
.pb-20 {
    padding-bottom: 20px;
}
.pt-20 {
    padding-top: 20px;
}

.text-006AB4 {
    color: #006ab4;
}

.text-008958 {
    color: #008958;
}

.text-FF5A54 {
    color: #ff5a54;
}

.__card-vertical {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(110, 137, 175, 0.05);
    border-radius: 10px;
    padding: 22px 22px 14px;
}

.__card-vertical-img {
    display: flex;
    align-items: center;
}

.__card-vertical-img .img {
    width: 30px;
}

.__card-vertical-img .name {
    padding-inline-start: 15px;
}

.__card-vertical-img .info-icon {
    display: inline-flex;
    margin-inline-start: 5px;
    opacity: 0.85;
    transform: translateY(2px);
}

.__card-vertical-img img {
    margin: 0;
}

.__card-vertical .name {
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    color: #334257;
    margin: 0;
}

.__card-vertical .earning {
    font-size: 18px;
    font-weight: 700;
    white-space: nowrap;
    margin: 0;
    padding-inline-start: 5px;
}

.__card-vertical .earning small {
    font-weight: normal;
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
}

@media (max-width: 575px) {
    .__card-vertical {
        padding: 15px;
    }
    .__card-vertical .__card-vertical-img .img {
        width: 20px;
    }
    .__card-vertical .__card-vertical-img .name {
        font-size: 14px;
        font-weight: 600;
        padding-inline-start: 10px;
    }
    .__card-vertical .earning {
        font-size: 16px;
    }
}
@media (min-width: 992px) and (max-width: 1220px) {
    .__card-vertical {
        padding: 21px 15px;
    }
    .__card-vertical .__card-vertical-img .img {
        width: 20px;
    }
    .__card-vertical .__card-vertical-img .name {
        font-size: 14px;
        font-weight: 600;
        padding-inline-start: 10px;
    }
    .__card-vertical .earning {
        font-size: 16px;
    }
}
.text-00AA6D {
    color: #00aa6d;
}

.text-FF7500 {
    color: #ff7500;
}

.navbar-vertical-content .navbar-nav {
    min-height: calc(100vh - 3.5rem - 81px);
}

.__sidebar-hs-unfold {
    margin-top: auto;
}

/* #grow-sale-chart .apexcharts-area-series .apexcharts-area {
  stroke: #00bf79;
}
#grow-sale-chart .apexcharts-marker {
  fill: #00bf79;
}
#grow-sale-chart .apexcharts-tooltip-marker {
  background: #00bf79 !important;
} */
.__gap-5px {
    gap: 5px;
}

body > #printableArea {
    opacity: 0;
}

@media print {
    body > #printableArea {
        opacity: 1;
    }
}
[dir="rtl"] .select2-container {
    direction: rtl;
}

[dir="rtl"] .select2-dropdown--below {
    direction: ltr;
}

[dir="rtl"] .select2-dropdown--below * {
    direction: rtl;
}

[dir="rtl"] .select2-container--default .select2-results__option {
    padding-inline-start: 0.5rem;
    padding-inline-end: 3rem;
}

[dir="rtl"] .close {
    margin-inline-end: 0;
    margin-inline-start: auto;
}

[dir="rtl"] [class*="js-toggle-password-target"] .input-group-text {
    padding-inline-end: 0.875rem !important;
    padding-inline-start: 0 !important;
}

.direction-toggle {
    background: #35365e;
}

.direction-toggle {
    cursor: pointer;
}

.toggle-tour,
.direction-toggle {
    color: #ffffff;
    padding: 8px 0;
    padding-inline-end: 18px;
    padding-inline-start: 10px;
    position: fixed;
    top: 30%;
    border-radius: 5px;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all ease 0.3s;
    white-space: nowrap;
    inset-inline-start: 100%;
    transform: translateX(calc(-100% + 3px));
}

.toggle-tour {
    top: 20%;
}

.toggle-tour:not(:hover),
.direction-toggle:not(:hover) {
    transform: translateX(-39px);
}

.toggle-tour i,
.direction-toggle i {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: cogs 1s linear infinite;
}

@keyframes cogs {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
[dir="rtl"] .toggle-tour,
[dir="rtl"] .direction-toggle {
    transform: translateX(calc(100% - 3px));
}

[dir="rtl"] .toggle-tour:not(:hover),
[dir="rtl"] .direction-toggle:not(:hover) {
    transform: translateX(39px);
}

[dir="rtl"] .input-group .btn {
    border-radius: 5px 0 0 5px !important;
}

[dir="rtl"] .input-group .form-control:not(.js-toggle-password):first-child,
[dir="rtl"] .input-group .input-group-append:first-child,
[dir="rtl"] .input-group .input-group-first-child .input-group-text {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
}

[dir="rtl"] .input-group .form-control:last-child,
[dir="rtl"] .input-group .input-group-append:last-child,
[dir="rtl"] .input-group .input-group-append:last-child .input-group-text {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
}

/* [dir="rtl"]
    .toggle-switch-sm
    .toggle-switch-input:checked
    + .toggle-switch-label
    .toggle-switch-indicator {
    transform: translate3d(-18px, 50%, 0) !important;
} */
[dir="rtl"] .custom-select:not(.ltr) {
    background-position-x: 7px;
}

[dir="rtl"] .modal .close {
    float: left;
}

[dir="rtl"] .right-dropdown-icon::before {
    transform: rotate(-45deg);
}

.chartjs-custom {
    direction: ltr;
}

.apexcharts-yaxis {
    direction: ltr;
}

body > .initial-38-1 {
    opacity: 0;
}

@media print {
    body > .initial-38-1 {
        opacity: 1;
    }
}
.inline_product {
    overflow: hidden;
}

/* .iti__country {
    display: flex;
}

.iti--allow-dropdown .iti__flag-container,
.iti--separate-dial-code .iti__flag-container {
    inset-inline-start: 0;
    inset-inline-end: unset !important;
}

.iti--allow-dropdown input,
.iti--allow-dropdown input[type="text"],
.iti--allow-dropdown input[type="tel"],
.iti--separate-dial-code input,
.iti--separate-dial-code input[type="text"],
.iti--separate-dial-code input[type="tel"] {
    -webkit-padding-end: 6px !important;
    padding-inline-end: 6px !important;
    -webkit-padding-start: 93px !important;
    padding-inline-start: 93px !important;
} */
#navbarSettings .nav-item .nav-link {
    display: flex;
}

.shadow--order-card .order--card {
    min-height: 70px;
}

.shadow--order-card .row {
    gap: 5px;
}

.shadow--order-card {
    padding: 10px;
    background: #ffffff;
    box-shadow: 0px 10px 15px rgba(0, 85, 85, 0.05);
    border-radius: 7.46793px;
}

.__customer-statistics-card-wrap-2 .__customer-statistics-card {
    padding: 23px 22px 31px;
}

/* .iti--container {
    direction: ltr;
}

@media (min-width: 1240px) {
    [dir="rtl"] .iti__country {
        direction: rtl;
    }
}
[dir="rtl"] .iti-mobile .iti--container .iti__country {
    direction: rtl;
} */
@media print {
    .direction-toggle {
        display: none;
    }
}
.__login-badge {
    position: absolute;
    inset-inline-end: 25px;
    top: 25px;
}

.input-group-merge .input-group-append .input-group-text:last-child {
    padding: 0 0.875rem;
}

.js-toggle-password.form-control,
.auth-wrapper-form .input-group .form-control.form-control-lg {
    border-top-right-radius: 5px !important;
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
}

.navbar-nav-wrap-content-right .cmn--media .card-title {
    justify-content: flex-end;
}

[dir="rtl"] .input---group .input-group-prepend {
    border-radius: 0 5px 5px 0;
}

.popover-navigation {
    display: flex;
    gap: 7px;
}

.popover-navigation .btn-group {
    gap: 7px;
}

.popover-navigation .btn.btn-secondary {
    background: #000;
    color: white;
}

.popover-navigation .btn.btn-secondary.disabled {
    opacity: 0.9;
    color: white;
}

.popover-navigation .btn {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 5px !important;
    padding: 5px 12px 6px;
}

.popover-navigation .btn[data-role="next"] {
    background: #005555;
}

.popover-navigation .btn[data-role="end"] {
    color: #54c0ff;
    background: transparent;
    order: -1;
}

.popover-header {
    color: #000000;
}

.card-header label.badge {
    margin: 0;
    margin-inline-end: auto;
    margin-inline-start: 4px;
}

.size-8px {
    height: 8px;
    width: 8px;
    top: 0px;
    inset-inline-end: -11px;
}

.overflow-visible {
    overflow: visible !important;
}

.map-pop-deliveryman {
    background: #ffffff;
    box-shadow: 0px 6px 16px rgba(51, 66, 87, 0.1);
    border-radius: 5px;
    z-index: 9;
    padding: 6px 15px;
    margin-bottom: 10px;
}

.map-pop-deliveryman .form-control {
    height: 35px;
    padding-left: 35px;
}

.map-pop-deliveryman-inner {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

@media (min-width: 1200px) {
    .map-pop-deliveryman .map-pop-deliveryman-inner {
        -moz-column-gap: 16px;
        column-gap: 16px;
    }
}
.map-pop-deliveryman i {
    position: absolute;
    line-height: 35px;
    left: 12px;
}

.map-pop-deliveryman label {
    font-weight: 600;
    margin: 0;
    color: var(--title-clr);
}

.map-pop-deliveryman a {
    color: var(--info-clr);
    text-transform: capitalize;
}

.__map-wrapper-2 {
    position: relative;
}

.__map-wrapper-2 .map-wrapper-2 {
    min-height: 305px;
}

@media (min-width: 992px) and (max-width: 1340px) {
    .map-pop-deliveryman-inner {
        flex-wrap: nowrap;
        font-size: 12px;
    }
    .map-pop-deliveryman .form-control {
        width: 190px;
    }
}
@media (min-width: 768px) {
    .__map-wrapper-2 .map-wrapper-2 {
        min-height: 375px;
    }
    .__map-wrapper-2 .gm-fullscreen-control {
        top: 188px !important;
    }
    .__map-wrapper-2 .gmnoprint {
        top: 40px !important;
    }
    .map-pop-deliveryman {
        position: absolute;
        right: 8px;
        top: 8px;
        width: calc(100% - 16px);
    }
}
.tour-tour-1-element {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.tour-tour-4-element {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.sidebar--search-form {
    background-color: #005555;
    position: sticky;
    top: 0;
    z-index: 9;
}

.word-nobreak {
    white-space: nowrap;
}

.initial--23 .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.__custom-upload-img {
    display: block;
    margin: 0;
}

.__custom-upload-img input {
    display: none;
}

.__custom-upload-img center img {
    height: 100px;
    border-radius: 5px;
    border: 2px dashed #e5e5e5;
    cursor: pointer;
}

.navbar-nav .dropdown-menu.lang-menu {
    position: absolute;
}

@media (max-width: 575px) {
    .navbar-nav .dropdown-menu.lang-menu {
        left: unset;
        right: 0;
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    .js-navbar-vertical-aside-toggle-invoker {
        margin-right: 0 !important;
    }
}
.bs-popover-auto[x-placement^="left"] {
    inset-inline-end: auto !important;
}

.bs-popover-auto[x-placement^="right"] {
    inset-inline-start: auto !important;
}

[dir="rtl"] .bs-popover-left .arrow {
    direction: ltr;
}

[dir="rtl"] .bs-popover-right .arrow {
    direction: ltr;
}

.forget-pass-content {
    text-align: center;
}

.forget-pass-content .btn--primary {
    background: #039d55;
}

.forget-pass-content {
    max-width: 355px;
    margin: 0 auto;
    padding-bottom: 20px;
}

.forget-pass-content button {
    max-width: 300px;
    margin-inline: auto;
}

.forget-pass-content img {
    margin-bottom: 15px;
}

.otp-card {
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
}

.__gap-15px {
    gap: 15px;
}

.otp-field {
    width: 50px;
    height: 50px;
    background: #ffffff;
    border: 1px solid rgba(181, 181, 181, 0.5);
    box-shadow: 0px 3px 10px rgba(31, 39, 29, 0.05);
    border-radius: 5px;
    text-align: center;
}

.otp-field:focus {
    color: var(--base-clr);
}

.otp-card strong {
    color: #000000;
}

.otp-card .btn--primary {
    margin: 30px 0 20px;
}

.reset-password {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.auth-wrapper-right .reset-password .btn--primary,
.auth-wrapper-right .otp-card .btn--primary {
    font-size: 16px;
    font-weight: 400;
}

.capcha-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.capcha-spin:not(.active) {
    animation-play-state: paused;
    -webkit-animation-play-state: paused;
    -moz-animation-play-state: paused;
}

.nav--tabs.nav--pills .nav-item .nav-link {
    padding: 6px 17px;
    border: none !important;
    min-height: 35px;
    border-radius: 60px !important;
    font-weight: 400;
}

.nav--tabs.nav--pills .nav-item .nav-link.active {
    font-weight: 600;
    background: rgba(0, 97, 97, 0.7) !important;
    color: #ffffff !important;
}

.blinkings {
    position: relative;
    display: inline-flex;
    font-size: 16px;
    color: var(--primary);
    position: relative;
}

.blinkings::after,
.blinkings::before {
    content: "";
    background: rgba(0, 97, 97, 0.2) !important;
    border-radius: 50%;
    position: absolute;
    width: 28px;
    height: 28px;
    animation: blink 2s linear infinite;
    -webkit-animation: blink 2s linear infinite;
    -moz-animation: blink 2s linear infinite;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
}

.blinkings:not(.open-shadow)::before,
.blinkings:not(.open-shadow)::after {
    display: none;
}

.blinkings i {
    display: inline-flex;
}

.blinkings::before {
    animation-delay: 1s;
    -webkit-animation-delay: 1s;
    -moz-animation-delay: 1s;
}

.blinkings .business-notes {
    position: absolute;
    inset-inline-end: -5px;
    top: calc(100% + 16px);
    transition: all ease 0.3s;
    font-weight: 500;
    font-size: 14px;
    line-height: 23px;
    z-index: 9;
    color: var(--title-clr);
    width: 85vw;
    max-width: 379px;
    background: #f5feff;
    border: 1px solid rgba(0, 97, 97, 0.4);
    box-shadow: 0px 5px 15px -3px rgba(0, 97, 97, 0.2);
    border-radius: 10px;
    padding: 20px;
}

.blinkings .business-notes::before {
    content: "";
    position: absolute;
    inset-inline-end: 6px;
    bottom: 100%;
    background: #93bfc0;
    width: 20px;
    height: 15px;
    -webkit-clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.blinkings .business-notes h6 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    color: var(--primary);
}

.blinkings:not(.active):not(:hover) .business-notes {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
    transform-origin: start end;
}

@media (min-width: 992px) {
    .blinkings.lg-top .business-notes {
        top: unset;
        bottom: calc(100% + 16px);
    }
    .blinkings.lg-top .business-notes::before {
        right: 6px;
        top: 100%;
        bottom: unset;
        -webkit-clip-path: polygon(0 0, 50% 100%, 100% 0);
        clip-path: polygon(0 0, 50% 100%, 100% 0);
    }
}
@keyframes blink {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.5;
    }
}
.card-header .card-title {
    color: #5b6777;
}

.card-title {
    color: #5b6777;
}

.form-label {
    font-weight: 500;
}

.h--90px {
    height: 90px !important;
}

.height-285px {
    height: 285px !important;
}

.form-control {
    border-color: #e7eaf3;
}

.form--check .form-check-input[type="radio"]:checked,
.toggle-switch-input:checked + .toggle-switch-label {
    background-color: #14b19e;
}

.hs-nav-scroller-horizontal {
    max-width: 100%;
}

.w-90px {
    width: 90px;
}

.popover:not(.tour-tour) {
    background: #fff4f4;
    border: 1px solid rgba(255, 109, 109, 0.4);
    box-shadow: 0px 5px 15px -3px rgba(255, 78, 78, 0.2);
    border-radius: 10px;
}

.popover:not(.tour-tour) .popover-header {
    color: rgb(255, 109, 109);
    background: #fff4f4;
}

.form-check-input.rounded {
    border-radius: 1.5px !important;
}

.warning-modal h5,
.status-warning-modal h5 {
    font-family: "Roboto", sans-serif;
}

.status-warning-modal {
    max-width: 436px !important;
    font-weight: 400;
    font-size: 13px;
    line-height: 21px;
    color: #a0a0a0;
}

.status-warning-modal h5 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 9px;
}

.btn--cancel {
    color: #ff6d6d !important;
    background: #ffdcdc !important;
    border-radius: 5px;
    height: 40px;
    box-shadow: none !important;
    outline: none !important;
    border: none !important;
}

.text--primary-2 {
    color: var(--primary);
}

.modal {
    background: rgba(0, 0, 0, 0.4);
}

.__popover {
    position: absolute;
    min-width: 276px;
    inset-inline-end: calc(100% + 22px) !important;
    inset-inline-start: unset !important;
    transform: translateX(15px) translateY(-50%);
    transition: all ease 0.3s;
    top: 50%;
    z-index: 9;
    visibility: hidden;
    opacity: 0;
}

.__popover .arrow {
    position: absolute;
    top: 50%;
    inset-inline-start: 100%;
    width: 22px;
    transform: translateY(-50%);
    height: 22px;
    margin: 0;
    background: #ffb1b1;
    -webkit-clip-path: polygon(0 0, 0% 100%, 100% 50%);
    clip-path: polygon(0 0, 0% 100%, 100% 50%);
}

.popover-wrapper {
    position: relative;
}

.popover-wrapper:hover .__popover,
.popover-wrapper.active .__popover {
    visibility: visible;
    opacity: 1;
    transform: translateX(0) translateY(-50%);
}

.popover-wrapper > a:focus ~ .__popover,
.popover-wrapper button:focus ~ .__popover {
    visibility: visible;
    opacity: 1;
    transform: translateX(0) translateY(-50%);
}

.table-responsive {
    padding-block: 0 10px;
}

.table-responsive thead {
    position: sticky;
    top: 0;
    z-index: 9;
}

.how-it-works {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.how-it-works .item {
    width: calc(33.33% - 16px);
    flex-grow: 1;
    font-size: 12px;
    position: relative;
}

.how-it-works .item h5 {
    font-size: 12px;
    margin-bottom: 6px;
}

.how-it-works .item img {
    height: 50px;
    margin-bottom: 12px;
}

.how-it-works .item::before {
    content: "";
    position: absolute;
    left: 65px;
    background: rgba(158, 173, 193, 0.3);
    width: calc(100% - 80px);
    top: 88px;
    height: 1px;
}

.how-it-works .item .serial {
    background: rgba(0, 159, 170, 0.05);
    border-radius: 6px;
    font-weight: 600;
    font-size: 18px;
    line-height: 31px;
    color: #107980;
    width: 34px;
    text-align: center;
    margin: 0 10px 15px;
}

.how-it-works .item:last-child {
    max-width: 144px;
}

.how-it-works .item:last-child::before {
    display: none;
}

@media (max-width: 991px) {
    .how-it-works {
        gap: 10px;
        justify-content: center;
    }
    .how-it-works .item {
        width: 100%;
        max-width: 100%;
        text-align: center;
    }
    .how-it-works .item::before {
        display: none;
    }
    .how-it-works .item .serial {
        margin-inline: auto;
    }
}
.__bg-F8F9FC-card {
    background: #f8f9fc;
    border-radius: 10px;
    padding: 15px;
}

@media (min-width: 1450px) {
    .__bg-F8F9FC-card {
        padding: 20px;
    }
}
.single-item-slider {
    color: var(--title-clr);
}

.single-item-slider img {
    -o-object-fit: contain;
    object-fit: contain;
    height: 80px;
}

.single-item-slider .owl-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    transform: translateY(35px);
}

.single-item-slider .owl-dots .owl-dot {
    width: 8px;
    height: 6px;
    background: rgba(0, 159, 170, 0.39);
    border-radius: 8.35714px;
    transition: all ease 0.3s;
}

.single-item-slider .owl-dots .owl-dot.active {
    background: var(--primary);
    width: 12px;
}

.slide-counter {
    text-align: center;
    padding: 3px;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    background: #107980;
    border-radius: 5px;
    color: #fff;
}



.customer-list.new-msg {
    margin: 12px;
    background: rgba(0, 159, 170, 0.05);
    border: 1px solid rgba(0, 134, 143, 0.4) !important;
    border-radius: 5px;
}

@media (min-width: 768px) {
    .customer-list.new-msg {
        margin: 20px;
    }
}
.max-w-640 {
    max-width: 640px;
}

.btn-items * {
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
    box-shadow: none;
    width: 100%;
}

.btn-items a {
    border-radius: 3px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.25);
    display: block;
    width: 28px;
}

.btn-items a img {
    width: 28px;
    margin: 0;
}

.btn-items button {
    aspect-ratio: 1;
    border-radius: 3px;
    overflow: hidden;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.25);
    display: block;
    width: 28px;
}

.btn-items button img {
    width: 28px;
    margin: 0;
}

.btn-items {
    position: absolute;
    right: 5.86px;
    display: flex;
    flex-direction: column;
    gap: 3px;
    transition: all ease 0.3s;
    bottom: 28px;
}

.folder-btn-item {
    position: relative;
}

.folder-btn-item button {
    box-shadow: none !important;
}

.folder-btn-item:not(:hover) .btn-items {
    opacity: 0;
    visibility: hidden;
    transform: translateX(10px);
}

.gallery-modal-header {
    background: #4153b3;
    border-radius: 5px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 13px;
    gap: 5px;
    -moz-column-gap: 15px;
    column-gap: 15px;
    font-size: 11px;
    line-height: 1.5;
}

.gallery-modal-header a {
    color: #fff;
}

.right-top-close-icon {
    position: absolute;
    right: -22px;
    top: -22px;
    background-color: #fff !important;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.pt-0 {
    padding-top: 0 !important;
}

.card-header-shadow {
    border: none;
    box-shadow: 0px 0px 2px rgba(145, 158, 171, 0.2),
        0px 6px 12px -4px rgba(145, 158, 171, 0.05);
}

.digital_payment_methods strong {
    font-weight: 700;
}

.digital_payment_methods {
    position: relative;
}

.digital_payment_methods.inactive {
    opacity: 0.6;
}

.digital_payment_methods.inactive::before {
    position: absolute;
    content: "";
    inset: 0;
    z-index: 99;
}

.trx_top {
    transform: translateY(-15px);
    z-index: 999;
}

.alert--primary {
    background: transparent !important;
    border: 1px solid rgba(16, 121, 128, 0.2);
    border-inline-start: 4px solid var(--primary);
    border-radius: 0 5px 5px 0;
    color: var(--title-clr);
    padding-top: 22px;
    padding-bottom: 25px;
}

.alert--primary .alert--icon {
    font-size: 22px;
    color: var(--primary);
    line-height: 1;
    margin-inline-end: 5px;
}

.btn-right-fixed {
    position: absolute;
    right: 0;
    top: 0;
    height: 45px;
    width: 45px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.text--underline {
    text-decoration: underline;
}

.list-gap-5 {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.min-h-60vh {
    min-height: 60vh;
}

.mw-353px {
    max-width: 353px;
}

.mw-353px .btn-wrap {
    max-width: 300px;
    margin-inline: auto;
}

.mw-300px {
    max-width: 300px;
}

.disable-on-turn-of.inactive {
    position: relative;
    opacity: 0.4;
}

.disable-on-turn-of.inactive::before {
    content: "";
    z-index: 2;
    inset: 0;
    position: absolute;
}

.row-gap-1 {
    row-gap: 10px;
}

.gap-2 {
    gap: 10px;
}

.card-header .nav-tabs .nav-item .nav-link:not(.active) {
    font-weight: 400 !important;
    color: #9eadc1 !important;
}

.card-header .nav-tabs .nav-item .nav-link:not(.active) img {
    filter: grayscale(1);
}

.upload-btn-grp {
    display: flex;
    gap: 4px;
    position: absolute;
    bottom: 14px;
    right: 110px;
}

.upload__img-wrap {
    position: absolute;
    bottom: 14px;
    left: 14px;
    display: flex;
    gap: 10px;
}

.upload__img-box {
    width: 43px;
    border-radius: 5px;
    margin-bottom: 12px;
}

.upload__img-close {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: -6px;
    right: -6px;
    font-size: 11px;
    line-height: 14px;
    text-align: center;
    z-index: 1;
    cursor: pointer;
}

.upload__img-close:after {
    content: "✖";
    color: white;
}

.img-bg {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: relative;
    padding-bottom: 100%;
    border-radius: 5px;
}

.conv-dropdown-menu {
    inset-inline-start: auto !important;
    inset-inline-end: 17px !important;
    inset-block-start: calc(100% - 30px) !important;
    transform: translate(0) !important;
    padding: 0 5px;
    min-width: 200px;
}

.conv-dropdown-menu li a {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #9eadc1;
    display: block;
    padding: 12px 15px;
}

.conv-dropdown-menu li:not(:last-child) {
    border-bottom: 1px solid #e5e5e5;
}

.conv-txtarea .conv-txtarea-row:after,
.conv-txtarea .conv-txtarea-row:before {
    content: " ";
    display: table;
    clear: both;
}

.conv-txtarea .span6 {
    float: left;
    width: 48%;
    padding: 1%;
}

.conv-txtarea .emojionearea-standalone {
    float: right;
}

.conv-txtarea .divOutside {
    height: 20px;
    width: 20px;
    background-position: -1px -26px;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,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);
}

.conv-txtarea .emojionearea-button {
    opacity: 1 !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.conv-txtarea .form-control {
    box-shadow: none !important;
    outline: none !important;
    border: none !important;
}

.emojionearea .emojionearea-editor {
    min-height: 0;
    max-height: unset;
    height: 118px;
}

.emoji-icon-hidden {
    opacity: 0;
}

.emojionearea .emojionearea-button {
    right: 120px;
    top: 69px;
}

.emojionearea .emojionearea-picker.emojionearea-picker-position-top {
    margin-top: -201px !important;
}

.emojionearea
    .emojionearea-picker.emojionearea-picker-position-top
    .emojionearea-wrapper:after {
    right: 137px !important;
}

#file-upload-filename {
    position: absolute;
    bottom: 0;
    left: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    max-width: 260px;
    background: #fff;
    background: #fff;
    padding: 0 15px 18px;
}

@media (max-width: 575px) {
    #file-upload-filename {
        padding-bottom: 45px;
        max-width: 160px;
    }
}
.tab--content .item:not(.show) {
    display: none !important;
}

.max-349 {
    max-width: 349px;
}

.toggle-switch {
    font-weight: 500;
}

.upload-img-3 {
    cursor: pointer;
}

.upload-img-3 img {
    width: 100%;
    max-width: 245px;
    aspect-ratio: 245/140;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 3px;
}

.upload-img-3 img.vertical-img {
    -o-object-fit: contain;
    object-fit: contain;
    aspect-ratio: unset;
}

.upload-img-3 img.vertical-img {
    -o-object-fit: cover;
    object-fit: cover;
    aspect-ratio: 756/189;
}

.upload-img-3 .img {
    overflow: hidden;
}

.upload-img-3 .img.vertical-img {
    -o-object-fit: contain;
    object-fit: contain;
    aspect-ratio: unset;
    height: 140px;
    width: 100%;
}

.card-table td,
.card-table th {
    padding-inline: 1rem;
}

.avatar {
    -o-object-fit: cover;
    object-fit: cover;
}

.__size-105 {
    width: 105px;
    height: 40px;
    -o-object-fit: cover;
    object-fit: cover;
}

.gap-40px {
    gap: 20px;
}

.gap-30px {
    gap: 20px;
}

.gap-20px {
    gap: 20px;
}

@media (min-width: 768px) {
    .gap-40px {
        gap: 40px;
    }
    .gap-30px {
        gap: 30px;
    }
}
@media (min-width: 992px) {
    .min-w-187px {
        min-width: 187px;
    }
    .gap-lg-40px {
        gap: 40px;
    }
}
.min-w-135px {
    min-width: 135px !important;
    max-width: 135px !important;
}

.max-w-187px {
    max-width: 187px !important;
}

.h92px {
    height: 92px !important;
}

.card-body > .d-flex {
    position: relative;
    z-index: 1;
}

.email-format-wrapper * {
    white-space: initial;
    word-break: break-word;
}

.email-format-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
    --base: #006161;
}

.email-format-wrapper .left-content {
    width: 100%;
    max-width: 464px;
}

.email-format-wrapper .left-content h1,
.email-format-wrapper .left-content h2,
.email-format-wrapper .left-content h3,
.email-format-wrapper .left-content h4,
.email-format-wrapper .left-content h5,
.email-format-wrapper .left-content h6 {
    color: #000000;
}

.email-format-wrapper .right-content {
    width: 0;
    flex-grow: 1;
}

.email-format-wrapper h1,
.email-format-wrapper h2,
.email-format-wrapper h3,
.email-format-wrapper h4,
.email-format-wrapper h5,
.email-format-wrapper h6 {
    color: #5b6777;
    margin: 0;
}

.email-format-wrapper .form-label,
.email-format-wrapper .card-title {
    color: #5b6777;
}

.email-format-wrapper .main-table {
    width: 100%;
    background: #ffffff;
    margin: 0 auto;
    padding: 40px;
    font-family: "Roboto", sans-serif;
    font-size: 11.5207px;
    line-height: 21px;
    color: #737883;
}

.email-format-wrapper img {
    max-width: 100%;
}

.email-format-wrapper .cmn-btn {
    background: var(--base);
    color: #fff;
    padding: 8px 20px;
    display: inline-block;
    text-decoration: none;
}

.email-format-wrapper .mb-1 {
    margin-bottom: 5px !important;
}

.email-format-wrapper .mb-2 {
    margin-bottom: 10px !important;
}

.email-format-wrapper .mb-3 {
    margin-bottom: 15px !important;
}

.email-format-wrapper .mb-4 {
    margin-bottom: 20px !important;
}

.email-format-wrapper .mb-5 {
    margin-bottom: 25px !important;
}

.email-format-wrapper hr {
    border-color: rgba(0, 170, 109, 0.3);
    margin: 16px 0 !important;
}

.email-format-wrapper .border-top {
    border-top: 1px solid rgba(0, 170, 109, 0.3);
    padding: 15px 0 10px !important;
    display: block;
}

.email-format-wrapper .d-block {
    display: block;
}

.email-format-wrapper .privacy {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.email-format-wrapper .privacy a {
    text-decoration: none;
    color: #334257;
    position: relative;
}

.email-format-wrapper .privacy a:not(:first-child) .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #334257;
    display: inline-block;
    margin: 0 7px;
}

.email-format-wrapper .social {
    margin: 15px 0 8px;
    display: block;
}

.email-format-wrapper .social img {
    width: 24px;
}

.email-format-wrapper .copyright {
    text-align: center;
    display: block;
}

.email-format-wrapper .text-center {
    text-align: center;
}

.email-format-wrapper .text-base {
    color: var(--base);
    font-weight: 700;
}

.email-format-wrapper .font-medium {
    font-family: 500;
}

.email-format-wrapper .font-bold {
    font-family: 700;
}

.email-format-wrapper a {
    text-decoration: none;
}

.email-format-wrapper .bg-section {
    background: #e3f5f1;
}

.email-format-wrapper .p-10 {
    padding: 10px !important;
}

.email-format-wrapper .mt-0 {
    margin-top: 0;
}

.email-format-wrapper .w-100 {
    width: 100%;
}

.email-format-wrapper .order-table {
    padding: 10px;
    background: #fff;
}

.email-format-wrapper .order-table tr td {
    vertical-align: top;
}

.email-format-wrapper .order-table .subtitle {
    margin: 0;
    margin-bottom: 10px !important;
}

.email-format-wrapper .text-left {
    text-align: left;
}

.email-format-wrapper .text-right {
    text-align: right;
}

.email-format-wrapper .bg-section-2 {
    background: #f8f9fb;
}

.email-format-wrapper .p-1 {
    padding: 5px !important;
}

.email-format-wrapper .p-2 {
    padding: 10px !important;
}

.email-format-wrapper .px-3 {
    padding-inline: 15px !important;
}

.email-format-wrapper .mb-0 {
    margin-bottom: 0 !important;
}

.email-format-wrapper .m-0 {
    margin: 0 !important;
}

.email-format-wrapper .text-base {
    color: var(--base);
    font-weight: 700;
}

.email-format-wrapper .cke_bottom {
    display: none;
}

.cke_top {
    border-radius: 5px 5px 0 0;
}

.cke,
.cke_inner {
    border-radius: 5px;
}

.cke_contents {
    border-radius: 0 0 5px 5px;
}

.page-links-checkgrp {
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    row-gap: 5px;
    -moz-column-gap: 15px;
    column-gap: 15px;
}

.page-links-checkgrp li {
    list-style: none;
}

/* .chat-user-info-content .badge {
    position: absolute;
    right: 16px;
    top: 60px;
} */
.object--cover {
    -o-object-fit: cover;
    object-fit: cover;
}

.border--dashed {
    border-style: dashed;
    border-width: 2px;
}

.shadow--card-2 {
    background: #ffffff;
    border: 1px solid #f4f4f4;
    box-shadow: 0px 5px 15px rgba(51, 66, 87, 0.08);
    border-radius: 5px;
    height: 100%;
}

@media (max-width: 575px) {
    .shadow--card-2 .card-header {
        padding-inline: 15px;
    }
    .shadow--card-2 .card-body {
        padding: 15px;
    }
}
.btn-danger {
    background-color: #ff3a3a;
    border-color: #ff3a3a;
}

.floating-date-toggler {
    position: absolute;
    inset: 0;
}

.floating--date {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 9999;
    transition: all ease 0.3s;
}

.floating--date .floating--date-inner {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    -moz-column-gap: 22px;
    column-gap: 22px;
    row-gap: 12px;
}

.floating--date .floating--date-inner .item {
    width: 100px;
    flex-grow: 1;
}

.floating--date .floating--date-inner .item.smaller {
    flex-grow: 0;
    width: 85px;
}

.floating--date:not(.active) {
    opacity: 0;
    visibility: hidden;
    transform: translateY(5px);
}

.offcanvas {
    position: fixed;
    inset: 0;
    z-index: 999;
}

.offcanvas:not(.active) {
    visibility: hidden;
}

@media (min-width: 1200px) {
    .offcanvas {
        left: 256px;
    }
}
.upload-image-5 {
    display: block;
    max-width: 845px;
}

.upload-image-5 img {
    width: 100%;
    max-width: 100%;
    height: 170px;
    aspect-ratio: 1200/250;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: center center;
    object-position: center center;
}

.border-9EADC1 {
    border-color: #9eadc1;
}

.border-1px {
    border-width: 1px !important;
}

.word-break {
    white-space: pre-line;
}

@media (min-width: 992px) {
    .min-w-187px {
        min-width: 187px;
    }
}
.img__aspect-1 {
    aspect-ratio: 1 !important;
    -o-object-fit: cover;
    object-fit: cover;
}

.max-w-555px {
    max-width: 555px !important;
}

.img__aspect-unset {
    aspect-ratio: unset !important;
}

.card .btn--container .btn:not(.action-btn) {
    min-width: 120px;
}

.max-width-170px {
    max-width: 170px;
}
.min-width-170px {
    min-width: 170px;
}

.min-height-170px {
    min-height: 170px;
}

.height-200px {
    height: 200px;
}

.height-150px {
    height: 150px;
}

.min-w-100px {
    min-width: 100px;
}

.min-w-120px {
    min-width: 120px;
}

.max-w-300px {
    max-width: 300px !important;
}

.__new-coba .file_upload {
    align-items: flex-start !important;
}

.__new-coba .file_upload img {
    margin: 0 !important;
}

.mail-img-1 {
    width: 140px;
    height: 60px;
    -o-object-fit: contain;
    object-fit: contain;
}

.mail-img-2 {
    width: 130px;
    height: 45px;
    -o-object-fit: contain;
    object-fit: contain;
}

.mail-img-3 {
    width: 100%;
    height: 172px;
    -o-object-fit: cover;
    object-fit: cover;
}

.__gap-24px {
    gap: 24px;
}

.__choos-item {
    width: 100%;
    flex-grow: 1;
    max-width: 300px;
}

@media (min-width: 768px) {
    .__choos-item {
        max-width: 33vw;
    }
}
.select2-container--default
    .select2-selection--multiple
    .select2-selection__choice,
.bootstrap-tagsinput .tag {
    background: rgba(0, 159, 170, 0.15) !important;
    border-radius: 5px !important;
    color: #1f1f1f !important;
}

.pickup-zone-tag
    .select2-container--default
    .select2-selection--multiple
    .select2-selection__choice,
.pickup-zone-tag .bootstrap-tagsinput .tag {
    background: rgba(51, 66, 87, 0.06) !important;
    border-radius: 33px !important;
    color: rgba(51, 66, 87, 0.9) !important;
    font-weight: 500;
}

.__bg-7 th {
    background: rgba(0, 159, 170, 0.1) !important;
    border: 1px solid rgba(16, 121, 128, 0.1);
}

.max-w-100px {
    max-width: 100px !important;
}

.max-w-100px {
    max-width: 100px !important;
}

.spartan_item_wrapper.max-w-100px {
    border-radius: 5px;
    overflow: hidden;
}

.spartan_item_wrapper {
    position: relative;
}

.remove_image_button {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: red;
    font-size: 22px;
}

#instruction-modal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
}

#instruction-modal .modal-body {
    position: relative;
    padding: 0px;
}

#instruction-modal .close {
    position: absolute;
    right: -30px;
    top: 0;
    z-index: 999;
    font-size: 2rem;
    font-weight: normal;
    color: #fff;
    opacity: 1;
}

#email-modal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
}

#email-modal .modal-body {
    position: relative;
    padding: 0px;
}

#email-modal .close {
    position: absolute;
    right: -30px;
    top: 0;
    z-index: 999;
    font-size: 2rem;
    font-weight: normal;
    color: #fff;
    opacity: 1;
}

#food-modal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
}

#food-modal .modal-body {
    position: relative;
    padding: 0px;
}

#food-modal .close {
    position: absolute;
    right: -30px;
    top: 0;
    z-index: 999;
    font-size: 2rem;
    font-weight: normal;
    color: #fff;
    opacity: 1;
}

#attribute-modal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
}

#attribute-modal .modal-body {
    position: relative;
    padding: 0px;
}

#attribute-modal .close {
    position: absolute;
    right: -30px;
    top: 0;
    z-index: 999;
    font-size: 2rem;
    font-weight: normal;
    color: #fff;
    opacity: 1;
}

.toggle-tour {
    background: #fff;
    color: #006161;
    box-shadow: 0px 0px 20px rgba(71, 71, 71, 0.1);
    border-radius: 10px 0px 0px 10px;
    padding: 8px;
    gap: 12px;
}

.gap-10px {
    gap: 8px;
}

.toggle-tour > a,
.toggle-tour > div {
    cursor: pointer;
    padding: 12px;
}

.toggle-tour > a:hover {
    padding: 12px;
    background: #e5f9fb;
    border-radius: 5px;
}

.toggle-tour > div:hover {
    padding: 12px;
    background: #e5f9fb;
    border-radius: 5px;
}

.inputDnD .form-control-file {
    position: relative;
    inline-size: 100%;
    block-size: 100%;
    min-block-size: 8rem;
    outline: none;
    visibility: hidden;
    cursor: pointer;
    border-radius: 10px;
    /* opacity: 0; */
}

.inputDnD .form-control-file:before {
    content: attr(data-title);
    position: absolute;
    inset-inline-start: 0;
    inline-size: 100%;
    min-block-size: 100%;
    line-height: 2em;
    opacity: 1;
    visibility: visible;
    text-align: center;
    border: 1px dashed currentColor;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: visible;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
}

.inputDnD .form-control-file:hover:before {
    border-style: solid;
}

.img-fit {
    inline-size: 100%;
    block-size: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    display: block;
}

.radius-10 {
    border-radius: 10px !important;
}

.checkbox-color-primary {
    color: #005555 !important;
}

.d--none {
    display: none;
}

.ripple-animation {
    line-height: 1;
    box-shadow: 0 0 0px 0px rgba(2, 134, 255, 0.3);
    cursor: pointer;
    border-radius: 50%;
    transition: 250ms color;
    animation-name: wave;
    animation-duration: 1.3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

@keyframes wave {
    0% {
        box-shadow: 0 0 0px 0px rgba(2, 134, 255, 0.3);
    }
    100% {
        box-shadow: 0 0 0px 10px rgba(2, 134, 255, 0);
    }
}
.mb-30 {
    margin-block-end: 1.25rem;
}

.gap-4 {
    gap: 1.5rem !important;
}

@media screen and (min-width: 576px) {
    .pl-sm-5,
    .px-sm-5 {
        padding-left: 2rem !important;
    }
}
/* .navbar-fixed{
    z-index: 999;
}
  .modal{
    z-index: 9999;
} */
.__input-tab:not(.active) {
    display: none;
}

.max-w-487 {
    max-width: 487px;
}

.instructions-list {
    display: flex;
    gap: 16px;
    flex-direction: column;
}

.instructions-info,
.instructions-list li {
    color: #334257;
    font-size: 14px;
    line-height: 1.6;
    font-style: normal;
    font-weight: 500;
}

.max-w-842 {
    max-width: 842px !important;
}

.max-w-595 {
    max-width: 595px !important;
}

.text-underline {
    text-decoration: underline;
}

.reject-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    border-radius: 0px 0px 0.3125rem 0.3125rem;
    border: 1px solid rgba(243, 71, 112, 0.3);
    background: rgba(243, 71, 112, 0.9);
    color: #ffffff;
    padding: 10px;
}

.pending-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    border-radius: 0px 0px 0.3125rem 0.3125rem;
    border: 1px solid rgba(41, 180, 197, 0.3);
    background: rgba(41, 180, 197, 0.9);
    color: #ffffff;
    padding: 10px;
}

.review-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    border-radius: 0px 0px 0.3125rem 0.3125rem;
    border-radius: 0px 0px 10px 10px;
    background: rgba(7, 88, 159, 0.9);
    color: #ffffff;
    padding: 10px;
}

@media screen and (max-width: 767px) {
    .reject-info,
    .pending-info,
    .review-info {
        font-size: 12px;
    }
}
.__upload-img {
    border-radius: 10px;
    border: 2px dashed #e5e5e5;
    max-height: 249px;
    max-width: 100%;
    display: flex !important;
}

.__upload-img .img {
    width: 100%;
    height: 100%;
    margin-inline: auto;
    overflow: hidden;
}

.__upload-img img {
    border-radius: 10px;
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    -o-object-position: center center;
    object-position: center center;
}

.aspect-1-1 {
    aspect-ratio: 1/1;
}

.aspect-235-375 {
    aspect-ratio: 235/375;
}

.aspect-230-600 {
    aspect-ratio: 230/600;
}

.aspect-615-350 {
    aspect-ratio: 615/350;
}

.aspect-2-1 {
    aspect-ratio: 2/1;
}

.aspect-3-1 {
    aspect-ratio: 3/1;
}

.aspect-3-2 {
    aspect-ratio: 3/2 !important;
}

.aspect-3-4 {
    aspect-ratio: 3/4;
}

.aspect-4-1 {
    aspect-ratio: 4/1;
}

.aspect-5-1 {
    aspect-ratio: 5/1;
}

.aspect-6-1 {
    aspect-ratio: 6/1;
}

.aspect-7-1 {
    aspect-ratio: 7/1;
}

.aspect-1-2 {
    aspect-ratio: 1/2;
}

.aspect-1-3 {
    aspect-ratio: 1/3;
}

.aspect-1-4 {
    aspect-ratio: 1/4;
}

.aspect-1-5 {
    aspect-ratio: 1/5;
}

.aspect-1-6 {
    aspect-ratio: 1/6;
}

.aspect-1-7 {
    aspect-ratio: 1/7;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 1rem;
}

.gap-5 {
    gap: 3rem;
}

.fs-13 {
    font-size: 13px;
}

.fs-10 {
    font-size: 10px !important;
}

.fs-12 {
    font-size: 12px !important;
}

.fs-14 {
    font-size: 14px !important;
}

.fs-16 {
    font-size: 16px !important;
}

.fs-20 {
    font-size: 20px !important;
}
.fs-32 {
    font-size: 32px !important;
}

.fs-22 {
    font-size: 22px !important;
}

.select-product-item {
    flex: 1 0 48%;
    background-color: transparent;
    border-radius: 5px;
    padding: 10px;
    transition: all 300ms ease-in-out;
}

.select-product-item.selected,
.select-product-item:hover {
    background-color: #f5f5f5;
}

.column-gap-4 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem;
}

.row-gap-2 {
    row-gap: 0.5rem;
}

.custom-width {
    --width: 14ch;
    width: var(--width);
}

.min-width-720 {
    min-width: 720px;
}

#search-form .col-11 {
    width: 0 !important;
    max-width: auto !important;
    flex: unset !important;
    flex-grow: 1 !important;
}

#search-form .col-1 {
    width: unset !important;
    max-width: unset !important;
    flex-grow: 0 !important;
}

#search-form .col-1 .btn--primary {
    height: 100%;
}

/* body{
    word-break: break-all !important;
    word-wrap: break-word !important;
} */
/* Disburstment CSS */
.disbursement-report {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

@media screen and (max-width: 767px) {
    .disbursement-report {
        justify-content: space-evenly;
    }
}
.disbursement-report .rebursement-item {
    width: calc(33% - 12px);
    border-radius: 5px;
    background: #fff;
    box-shadow: 6px 6px 12px -3px rgba(51, 66, 87, 0.05);
}

.disbursement-report .rebursement-item .icon {
    width: 65px;
    height: 65px;
}

.disbursement-report .rebursement-item .subtitle {
    font-weight: 400;
}

@media screen and (max-width: 767px) {
    .disbursement-report .rebursement-item {
        width: calc(50% - 8px);
        border: 1px solid #f0f0f0;
    }
    .disbursement-report .rebursement-item .icon {
        width: 50px;
        height: 50px;
    }
}
@media screen and (max-width: 424px) {
    .disbursement-report .rebursement-item {
        width: 100%;
    }
}
@media screen and (min-width: 1200px) {
    .btn-xl-inline-block {
        width: 120px;
    }
}
.form-check-input {
    cursor: pointer;
}

.mb-14 {
    margin-bottom: 24px;
}

.payment-modal-close {
    position: absolute;
    right: 5px;
    top: 5px;
    padding: 10px;
    cursor: pointer;
}

.payment-modal-close i::before {
    font-size: 30px;
}

.mx-450 {
    max-width: 450px;
}

.mx-260 {
    max-width: 260px;
}

@media screen and (min-width: 576px) and (max-width: 992px) {
    .modal-dialog {
        max-width: 720px;
    }
}
.payment-info-modal-info {
    row-gap: 40px;
    -moz-column-gap: 24px;
    column-gap: 24px;
}

.payment-info-modal-info .item {
    width: 280px;
    flex-grow: 1;
}

.payment-info-modal-info .item h5 {
    font-size: 18px;
}

.payment-info-modal-info .item .item-list {
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    row-gap: 6px;
}

.payment-info-modal-info .item .item-list li {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    max-width: 360px;
}

.payment-info-modal-info .item .item-list li strong {
    margin-left: auto;
    color: var(--title-clr);
    font-weight: 600;
    padding-left: 10px;
}

@media screen and (min-width: 576px) {
    .payment-info-modal-info .item .item-list li strong {
        width: 120px;
    }
}
@media screen and (min-width: 1200px) {
    .payment-info-modal-info .item .item-list li strong {
        width: 215px;
    }
}
.payment-info-modal-info .item .item-list li .name {
    padding-right: 10px;
}

@media screen and (min-width: 576px) {
    .payment-info-modal-info .item .item-list li .name {
        width: 130px;
    }
}
@media screen and (min-width: 576px) {
    .payment-info-modal-info .item.w-100 .item-list li {
        width: 50%;
    }
    .payment-info-modal-info .item.w-100 .item-list li:nth-child(even) {
        margin-inline: auto;
    }
}
.btn.text-title {
    color: var(--title-clr) !important;
}
.__alert-2 {
    border-radius: 4px;
    background: #fff1f1 !important;
    display: flex;
    align-items: center;
    flex-grow: 1;
    white-space: initial;
    word-wrap: break-word;
    position: fixed;
    top: 4px;
    left: 292px;
    width: calc(100vw - 518px);
    z-index: 99;
}

@media screen and (max-width: 991px) {
    .__alert-2 {
        left: 10vw;
        width: 80vw;
        bottom: 20px;
        top: unset;
    }
}
.__alert-2 .cont {
    width: 0;
    flex-grow: 1;
    font-size: 12px;
}

.input--group .copy-btn {
    position: absolute;
    height: calc(100% - 10px);
    padding-block: 0;
    font-size: 12px;
    top: 5px;
    right: 5px;
    z-index: 1;
}

.module-warning-text {
    color: #8c1515;
    padding-top: 10px;
}

.view_conversation-style {
    margin-top: 10%;
}

.view_conversation-h4-style {
    color: rgba(113, 120, 133, 0.62);
}

.tab-button-group button {
    border: none;
    background-color: transparent;
    padding: 5px;
    font-weight: 600;
    border-bottom: 3px solid transparent;
}

.tab-button-group button.active {
    border-color: #334257;
}

.chat-user-conv-img img {
    border-radius: 50%;
}

/* .learn-more-wrap {
    position: relative;
} */
.learn-more-content {
    position: absolute;
    width: 100%;
    background-color: #fff;
    left: 0;
    bottom: 0;
    border-radius: 0px 0px 10px 10px;
    border-top: 0.5px solid var(--primary-color-30, rgba(3, 157, 85, 0.3));
    box-shadow: 0px 2px 32px 0px rgba(0, 0, 0, 0.05);
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: all 300ms ease-in-out;
}

.learn-more-wrap:hover .learn-more-content {
    visibility: visible;
    opacity: 1;
    transform: translateY(0px);
}

.font-weight-medium {
    font-weight: 500 !important;
}

.key-val-list-item > * {
    font-size: 12px;
}

.key-val-list {
    --min-width: 100px;
}

.key-val-list-item > *:first-child {
    min-width: var(--min-width, 100px);
}

.mx-h150 {
    max-height: 150px;
}

.mx-w-100 {
    max-width: 100%;
}

.hr-light {
    border-color: #f1f1ff !important;
}

@media (min-width: 992px) {
    .border-lg-left {
        border-left: 0.0625rem solid #e7eaf3 !important;
    }
}
.color-card {
    display: flex;
    gap: 1rem;
    padding: 40px 35px;
    border-radius: 10px;
    background: rgba(0, 224, 255, 0.1);
}

@media (max-width: 767px) {
    .color-card {
        display: flex;
        gap: 1rem;
        padding: 20px 16px;
        border-radius: 10px;
        background: rgba(0, 224, 255, 0.1);
    }
}
.color-card .img-box {
    --size: 60px;
    width: var(--size);
    height: var(--size);
    border-radius: var(--size);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background-color: #fff;
}

.color-card .title {
    color: var(--primary-clr);
}

.color-card.color-2 {
    background: rgba(3, 157, 85, 0.05);
}

.color-card.color-2 .title {
    color: #039d55;
}

.color-card.color-3 {
    background: #fffcde;
}

.color-card.color-3 .title {
    color: #e27e26;
}

.color-card.color-4 {
    background: rgba(255, 126, 13, 0.1);
}

.color-card.color-4 .title {
    color: #e27e26;
}

.color-card.color-5 {
    background: rgba(168, 99, 255, 0.1);
}

.color-card.color-5 .title {
    color: #a863ff;
}

.color-card.color-6 {
    background: rgba(0, 224, 255, 0.1);
}

.color-card.color-6 .title {
    color: #006fbd;
}

.color-card.color-7 {
    background: rgba(0, 111, 189, 0.1);
}

.color-card.color-7 .title {
    color: #006fbd;
}

.mt-half {
    margin-top: 2px !important;
}

@page {
    size: auto;
    margin: 0;
    /* this affects the margin in the printer settings */
}
.theme-style input,
.theme-style + .select2 .select2-selection {
    border-color: #5faeb4 !important;
}

.theme-style .btn--secondary {
    background-color: #5faeb4 !important;
}

.theme-style + .select2 .select2-selection__rendered > span {
    color: #5faeb4 !important;
}

.mt-3px {
    margin-top: 3px !important;
}

.max-text-2-line {
    --line-count: 2;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    line-clamp: var(--line-count);
    -webkit-line-clamp: var(--line-count);
}

.break-all {
    word-break: break-all !important;
}

.avatar-70 {
    width: 70px;
    min-width: 70px;
    height: 70px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9;
}

.input-group--merge > input {
    padding-left: 35px !important;
}

.product-card .product-title {
    line-height: 1.2;
}

.pos-product-card {
    position: relative;
}

.pos-product-card::after {
    content: attr(data-item-count);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1;
    display: grid;
    place-items: center;
    color: #fff;
    font-size: 24px;
    border-radius: 10px;
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.3s;
}

.pos-product-card.active::after {
    opacity: 1;
    visibility: visible;
}

.amount--input.update-Quantity {
    background: #fff !important;
    padding: 5px 10px;
    height: 28px;
    width: 52px;
}

.pos-cart-remove-btn {
    width: 25px;
    height: 25px;
    display: grid;
    place-items: center;
    background-color: var(--danger-clr);
    color: #fff;
}

.pos-cart-remove-btn:hover {
    color: #fff;
}

.modal-scroll {
    max-height: 94dvh;
    overflow-y: auto;
}

.custom-select.theme-style {
    border-color: var(--primary-clr) !important;
}

.withdraw-info-sidebar {
    --width: 400px;
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: var(--width);
    max-width: 100%;
    background: #fff;
    z-index: 999;
    transform: translateX(100%);
    transition: transform ease 0.3s;
    padding: 1.25rem;
    overflow-y: auto;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
}

.withdraw-info-sidebar.show {
    transform: translateX(0);
}

.withdraw-info-sidebar-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 998;
    visibility: hidden;
    opacity: 0;
    transition: all ease 0.3s;
}

.withdraw-info-sidebar-overlay.show {
    visibility: visible;
    opacity: 1;
}

.circle {
    --size: 26px;
    width: var(--size);
    min-width: var(--size);
    height: var(--size);
    border-radius: var(--size);
    display: grid;
    place-items: center;
}

.table-responsive {
    max-height: 67dvh;
    overflow-y: auto;
}

.light-card {
    background-color: #edf7ff;
    color: #0477ca;
}

.icon-file {
    width: 25px;
    height: 25px;
    border-radius: 4px;
    border: 1px solid #0477ca;
    color: #0477ca;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    line-height: 1;
    margin: 0;
}

.icon-file input {
    display: none;
}

#viewer.border {
    border: 2px dashed #e5e5e5 !important;
    border-radius: 6px;
}

.image--border {
    border: 1px dashed #e5e5e5 !important;
    border-radius: 6px;
}

.img--176 {
    width: 176px;
    aspect-ratio: 1;
    -o-object-fit: cover;
    object-fit: cover;
}

.icon-file-group {
    display: flex;
    position: absolute;
    inset-inline-end: 15px;
    inset-block-start: 15px;
    z-index: 1;
    gap: 8px;
}

.icon-file-group.outside {
    inset-inline-end: -5px;
    inset-block-start: -5px;
}

.icon-file-group .action-btn {
    width: 25px;
    height: 25px;
}

.min-w-176px {
    min-width: 176px;
}

.max-w-176px {
    max-width: 176px;
}

.spartan_item_wrapper img {
    border: 2px dashed #e5e5e5 !important;
    border-radius: 6px;
}

.spartan_remove_row {
    border: 1px solid rgb(255, 7, 0);
}

.product-gallery-info > .d-block > span:first-child {
    display: inline-block;
    width: 93px;
}

.product-gallery-info > .d-block > span:nth-child(2) {
    margin-right: 5px;
}

.btn-outline-primary:focus,
.btn-outline-primary.focus {
    box-shadow: none;
}

.alert-soft-primary {
    background: #edf7ff;
    color: #0477ca;
}

.alert-soft-primary .close {
    color: var(--title-clr);
}

.module-radio-group .form--check {
    padding: 0 !important;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
}

.module-radio-group .form--check input {
    margin: 0;
    margin-inline-end: 20px;
}

.module-radio-group .form--check .form-check-label {
    padding: 15px 20px;
    width: 100%;
}

.module-radio-group .form--check:first-child .form-check-label {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.module-radio-group .form--check:last-child .form-check-label {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.module-radio-group .form--check input:checked ~ .form-check-label {
    background: #e8f2f3;
    font-weight: 700;
    color: #334257;
}

.module-radio-group .form--check:not(:last-child) {
    border-bottom: 1px solid #f6f6f6;
}

.product-gallery-info {
    font-size: 12px;
}

.h-60px {
    height: 60px !important;
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain;
}

.object-left {
    -o-object-position: center left;
    object-position: center left;
}

.text-14 {
    font-size: 14px;
}

.bg-E6F6EE {
    background: #e6f6ee !important;
}

.bg-F1E8FA {
    background: #f1e8fa !important;
}

.bg-E5F5F6 {
    background: #e5f5f6 !important;
}

.__dashboard-card.bg-F1E8FA .meter {
    background: #fdfcff !important;
}

.__dashboard-card.bg-F1E8FA .meter span {
    background: #d0b4ec !important;
}

.top-50px {
    top: 50px !important;
}

.export-steps-2 {
    margin-bottom: 32px;
}

.export-steps-item-2 {
    border-radius: 10px;
    border: 1px solid rgba(51, 66, 87, 0.1);
    background: #fff;
    padding: 30px;
}

.export-steps-item-2 .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.input_image_edit {
    width: 100%;
    height: auto;
    background-position: center !important;
    background-size: contain;
    background-repeat: no-repeat;
}

.upload-text {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    z-index: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: absolute;
    inset-inline-start: 0;
    inline-size: 100%;
    min-block-size: 100%;
    line-height: 2em;
    opacity: 1;
    visibility: visible;
    text-align: center;
    border: 1px dashed var(--c1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: visible;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    color: var(--c1);
}

.py-20 {
    padding-block: 20px !important;
}

.h--42px {
    height: 42px !important;
}

.rounded-8 {
    border-radius: 8px !important;
}

.bg-not-hover-primary-ash:not(:hover) {
    background: #f5feff !important;
    --primary-clr: #107980;
    border: 1px solid rgba(16, 121, 128, 0.5) !important;
    box-shadow: 0px 2px 10px 0px rgba(0, 97, 97, 0.1);
}

label.position-relative {
    display: block;
}

.btn-number {
    box-shadow: none !important;
}

.dropdown-toggle.btn--primary::after {
    filter: brightness(1);
    -webkit-filter: brightness(1);
}

.max-w-542 {
    max-width: 542px !important;
}

.__bg-8 {
    background: rgba(3, 157, 85, 0.0509803922);
}

.__bg-8 .title {
    color: #039d55;
}

.w--85px {
    width: 85px;
}

.check--item-wrapper-2 {
    display: flex;
    align-items: center;
    margin-bottom: -20px;
}

.check--item-wrapper-2 .check-item {
    padding-bottom: 20px;
}

.custom-limit-box {
    display: none;
}

.order--card .card-subtitle img[width="20"] {
    width: 20px;
    height: 20px;
}

.__card-2 .card-icon[width="35"] {
    width: 35px;
    height: 35px;
}

.__plan-details {
    border-radius: 8px;
    padding: 20px 28px;
}

.__plan-details-top .left {
    padding-inline-end: 20px;
    width: 0;
    flex-grow: 1;
}

@media screen and (max-width: 767px) {
    .__plan-details-top .left {
        width: 100%;
    }
}
.__plan-details-top .left .name {
    color: #006ab4;
    font-size: 26px;
    margin-bottom: 0;
}

.__plan-details-top .right {
    font-size: 30px;
    display: flex;
    align-items: flex-end;
    margin-top: 15px;
}

.__plan-details-top .right small {
    font-size: 65%;
}

@media (max-width: 374px) {
    .__plan-details-top .left {
        padding-inline-end: 0;
    }
    .__plan-details-top .right {
        font-size: 26px;
    }
}
.__plan-details .form-check-input {
    border-radius: 50%;
    background: var(--primary);
}

.check--grid-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    row-gap: 20px;
}

.max-w-850px {
    max-width: 850px;
}

.__trx-print {
    max-width: 595px !important;
    width: 100% !important;
}

.__trx-print .initial-38-2 {
    width: 190px !important;
    max-height: 50px !important;
    margin-inline-start: auto !important;
    margin-inline-end: auto !important;
}

@media print {
    .__trx-print {
        /* max-width: 382px !important;
    width: 100% !important; */
        margin: 0 auto !important;
    }
}
.__trx-print.initial-38-1 .initial-38-3 {
    font-weight: 700 !important;
    font-size: 28px;
}

.__trx-print.initial-38-1 .initial-38-3.name {
    font-size: 18px;
}

.__trx-print.initial-38-1 .initial-38-4 {
    font-weight: 700 !important;
    padding-top: 10px;
    padding-bottom: 24px;
}

.text--base {
    color: var(--primary-clr) !important;
}

.__subscribe-table {
    width: 100%;
    background: #f5f5f5;
}

.__subscribe-table tr th,
.__subscribe-table tr td {
    padding: 10px 0;
    vertical-align: middle;
}

.__subscribe-table tr th {
    white-space: nowrap;
}

.__subscribe-table tr td:not(:last-child) span,
.__subscribe-table tr th:not(:last-child) span {
    border-inline-end: 1px solid #e5e5e5;
}

.__subscribe-table tr td span,
.__subscribe-table tr th span {
    font-size: 12px;
    display: block;
    padding: 0px 15px;
    text-align: center;
}

.__subscribe-table tr td span {
    font-size: 10px;
}

.__txt-nowrap {
    white-space: nowrap;
}

.__trx-print.initial-38-1 * {
    font-family: "Inter", sans-serif !important;
}

.text-star i,
.text-star {
    color: #f8923b !important;
}

.transaction--information {
    border: 1px solid #f4f4f4;
    box-shadow: 0px 3px 5px rgba(51, 66, 87, 0.05);
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    margin-bottom: 30px;
}

.transaction--information li {
    list-style: none;
    display: flex;
    padding: 5px;
    font-size: 0.7rem;
}

.transaction--information li i {
    font-size: 1.5rem;
}

.transaction--information li > div {
    align-self: center;
    padding-inline-start: 5px;
}

.transaction--information li > div span {
    margin-inline-end: 10px;
    font-weight: 500;
}

.transaction--information li > div * {
    display: inline-block;
}

.transaction--information li > div strong {
    font-size: 14px;
}

@media screen and (max-width: 1199px) {
    .transaction--information {
        justify-content: space-evenly;
    }
    .transaction--information .seperator {
        display: none;
    }
}
@media screen and (max-width: 767px) {
    .transaction--information li {
        flex-grow: 1;
    }
}
@media screen and (max-width: 575px) {
    .transaction--information {
        padding: 15px 10px;
    }
    .transaction--information li {
        flex-grow: 1;
    }
    .transaction--information li > div {
        display: flex;
        justify-content: space-between;
        flex-grow: 1;
    }
    .transaction--information li > div strong {
        white-space: nowrap;
    }
}
.transaction--information .seperator {
    background: rgba(0, 0, 0, 0.45);
    height: 25px;
    width: 1px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding: 0;
}

@media screen and (min-width: 1400px) {
    .resturant--info-address .logo {
        width: 140px;
    }
    .resturant--info-address .address-info {
        width: 0;
        flex-grow: 1;
    }
}
.__plan-item {
    --border-clr: #f2f5f9;
    --success: #039d55;
    --primary-clr: #00868f;
    background: #ffffff;
    border: 0.721461px solid #f4f4f4;
    box-shadow: 0px 3.60731px 10.8219px rgba(51, 66, 87, 0.05);
    border-radius: 8px;
    transition: all ease 0.3s;
}
.__plan-item .inner-div {
    padding: 39px 16px 30px;
    border-radius: inherit;
    position: relative;
    overflow: hidden;
}
.__plan-item .inner-div::before {
    content: "";
    position: absolute;
    top: -120px;
    left: 35%;
    background: rgba(0, 134, 143, 0.05);
    transition: all ease 0.3s;
    width: 214px;
    height: 214px;
    border-radius: 50%;
}
.__plan-item * {
    position: relative;
    z-index: 1;
}
.__plan-item .title {
    font-size: 18px;
    font-weight: 500;
    color: var(--primary-clr);
    margin-bottom: 20px;
}
.__plan-item .price {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--primary-clr);
}
.__plan-item .day-count {
    display: inline-block;
    padding-inline: 24px;
    border-bottom: 1px solid var(--border-clr);
    font-size: 14px;
    padding-bottom: 6px;
}
.__plan-item .info-text {
    height: 152px;
    overflow-y: auto;
}
.__plan-item .info {
    padding: 0;
    margin: 0;
    list-style: none;
    margin-top: 16px;
    height: 160px;
    overflow-y: auto;
    margin-bottom: 30px;
}
.__plan-item .info li {
    padding: 6px 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}
.__plan-item .info li:not(:last-child) {
    border-bottom: 1px solid var(--border-clr);
}
.__plan-item .info li i {
    color: var(--success);
}
.__plan-item.active,
.__plan-item.hover:hover {
    background: var(--primary-clr);
    --border-clr: #ffffff10;
    --success: #ffffff;
}
.__plan-item.active .inner-div,
.__plan-item.hover:hover .inner-div {
    color: var(--white);
}
.__plan-item.active .inner-div::before,
.__plan-item.hover:hover .inner-div::before {
    top: -80px;
    left: 20px;
    background: rgba(255, 255, 255, 0.05);
    width: 260px;
    height: 260px;
}
.__plan-item.active .inner-div .title,
.__plan-item.active .inner-div .price,
.__plan-item.hover:hover .inner-div .title,
.__plan-item.hover:hover .inner-div .price {
    color: #ffffff;
}
.__plan-item.active .btn--primary,
.__plan-item.hover:hover .btn--primary {
    --primary-clr: #f99c4d;
}
.subscription__plan-info {
    font-size: 14px;
    font-weight: 500;
    color: #334257;
}

.subscription__plan-info .subtitle {
    margin: 0;
    margin-top: 5px;
    font-size: 22px;
    font-weight: 600;
}

.subscription__plan-info .subtitle sub {
    bottom: 0;
    font-size: 12px;
}

.bg-ECEEF1 {
    background: #eceef1;
}

.rounded-20 {
    border-radius: 20px !important;
}

.subscription__plan-info-wrapper {
    padding: 20px;
}

@media (min-width: 768px) and (max-width: 991px) {
    .subscription__plan-info-wrapper .subscription__plan-info .subtitle {
        font-size: 18px;
    }
}
@media (max-width: 991px) {
    .subscription__plan-info-wrapper {
        padding-top: 35px;
        padding-bottom: 35px;
    }
    .subscription__plan-info-wrapper .subscription__plan-info {
        max-width: 350px;
        margin: 0 auto;
    }
}
@media (max-width: 991px) and (max-width: 425px) {
    .subscription__plan-info-wrapper .subscription__plan-info .subtitle {
        font-size: 18px;
    }
}
.change-plan-wrapper {
    max-width: 694px;
    margin: 0 auto 30px;
    display: flex;
    justify-content: center;
}
.change-plan-wrapper .__plan-item {
    width: 100%;
    max-width: 246px;
}
.change-plan-wrapper .__plan-item .inner-div::before {
    top: -140px;
    left: 35%;
    width: 214px;
    height: 214px;
}
.change-plan-wrapper .__plan-item .inner-div .day-count {
    border-bottom: none;
}
.change-plan-wrapper .__plan-item .inner-div .price {
    border-top: 1px solid var(--border-clr);
    padding-top: 15px;
}

@media (min-width: 992px) {
    .change-plan-wrapper {
        margin-bottom: 55px;
    }
}
@media (max-width: 600px) {
    .change-plan-wrapper {
        margin-bottom: 55px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 15px;
    }
}
.payment-item {
    display: block;
    cursor: pointer;
    margin: 0;
}
.payment-item .payment-item-inner {
    color: #4b5864;
    padding: 20px 15px;
    border-radius: 20px;
    border: 1px solid rgba(147, 162, 174, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
}
.payment-item .payment-item-inner .check {
    width: 20px;
    display: inline-flex;
}
.payment-item .payment-item-inner .check img {
    width: 100%;
}
.payment-item .payment-item-inner .check .check {
    display: none;
}
.payment-item input:checked ~ .payment-item-inner {
    border: 1px solid #14b19e;
    background: rgba(20, 177, 158, 0.1);
}
.payment-item input:checked ~ .payment-item-inner .check .check {
    display: block;
}
.payment-item input:checked ~ .payment-item-inner .check .uncheck {
    display: none;
}

.max-w-800px {
    max-width: 800px;
}

.renew-badge {
    border-radius: 8px;
    background: #ffeaea;
    padding: 6px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}
.renew-badge .renew-content {
    width: 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}
.renew-badge .renew-content .txt {
    width: 0;
    flex-grow: 1;
}
.renew-badge .btn--danger {
    font-size: 12px;
    padding-block: 8px !important;
}

.free-trial {
    padding: 23px 60px 23px 30px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}
.free-trial .inner-div {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 16px;
}
.free-trial .inner-div .left {
    display: flex;
    flex-grow: 1;
    gap: 12px;
}
.free-trial .inner-div .left .left-content {
    width: 180px;
    flex-grow: 1;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: rgba(255, 255, 255, 0.8);
}
.free-trial .inner-div .left .left-content h6 {
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    color: #fff;
    margin: 0;
}
@media screen and (min-width: 576px) {
    .free-trial .inner-div .left .left-content {
        width: 300px;
    }
}
.free-trial .trial-close {
    position: absolute;
    right: 20px;
    top: 30px;
    color: rgba(255, 255, 255, 0.5);
    background: transparent;
    outline: none;
    box-shadow: none;
    border: none;
    font-size: 24px;
}
.free-trial .right {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-left: auto;
    justify-content: flex-end;
}
.free-trial .right .btn-light {
    padding: 12px 20px;
    box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
    color: #039d55;
    font-size: 12px;
    font-weight: 600;
}
@media screen and (max-width: 575px) {
    .free-trial .right .btn-light {
        padding: 10px 15px;
    }
}
@media screen and (max-width: 575px) {
    .free-trial {
        padding: 23px 40px 23px 20px;
    }
    .free-trial .trial-close {
        right: 10px;
    }
    .free-trial .inner-div .left .left-content {
        font-size: 12px;
    }
    .free-trial .inner-div .left .left-content h6 {
        font-size: 16px;
    }
}

.success-bg {
    background: linear-gradient(0deg, #039d55 0%, #039d55 100%), #fff;
}

.danger-bg {
    background: linear-gradient(0deg, #ff6d6d 0%, #ff6d6d 100%), #fff;
}
.danger-bg .btn-light {
    color: #ff6d6d !important;
}

.btn-2 {
    padding: 10px 20px;
    box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
    color: white;
    font-size: 12px;
    font-weight: 600;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 4px;
}
.btn-2:hover {
    background: #fff;
    color: var(--primary-clr);
}
.btn-2:hover [stroke="#ffffff"] {
    stroke: var(--primary-clr);
}
.btn-2:hover [stroke="#ffffff20"] {
    stroke: rgba(0, 0, 0, 0.2);
}
@media screen and (max-width: 575px) {
    .btn-2 {
        padding: 10px 15px;
    }
}

.circle-progress-container {
    position: relative;
    display: inline-block;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
}
.circle-progress-container svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.blocked-subscription {
    border-radius: 5px;
    background: rgba(255, 103, 103, 0.05);
    padding: 12px 20px;
    display: flex;
    gap: 10px;
    color: #ff6767;
    font-size: 12px;
    font-weight: 500;
    line-height: 150%;
}

@media screen and (min-width: 768px) {
    .trial-ended-modal .modal-dialog {
        max-width: 740px;
    }
}

.trial-ended-modal-wrapper {
    display: flex;
}
.trial-ended-modal-wrapper .trial-ended-modal-content {
    width: calc(100% - 286px);
    flex-grow: 1;
    padding: 80px 60px;
}
@media screen and (max-width: 480px) {
    .trial-ended-modal-wrapper .trial-ended-modal-content {
        padding: 40px 20px;
    }
}
.trial-ended-modal-wrapper .trial-ended-modal-img {
    width: 286px;
}
.trial-ended-modal-wrapper .trial-ended-modal-img img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.trial-ended-close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 1;
    background: transparent;
    outline: none;
    border: none;
    box-shadow: none;
    outline: none;
    font-size: 24px;
}

@media screen and (min-width: 768px) {
    .text-md-white {
        color: rgba(255, 255, 255, 0.3137254902);
    }
}
.w-200px {
    width: 200px;
}

.form-control[readonly].bg-white {
    background-color: #fff !important;
}

.text-180 {
    font-size: 180%;
} /*# sourceMappingURL=style.css.map */

.mt-10px {
    margin-top: 10px;
}
.mb-20px {
    margin-bottom: 20px;
}
.fs-24 {
    font-size: 24px !important;
}
.add-outline-btn {
    font-size: 14px;
    font-weight: 600;
    text-transform: capitalize;
    height: 44px;
    border-radius: 5px;
    border: 1px solid var(--primary-clr);
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--title-clr);
    transition: all ease 0.3s;
}
.add-outline-btn:hover {
    background: var(--title-clr);
    border-color: var(--title-clr);
    color: #fff;
}
.add-outline-btn .txt {
    margin-top: 2px;
}
.add-content-btn {
    height: 40px;
    padding-inline: 15px;
    padding-block: 0 !important;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    --primary-clr: #00aa6d;
}
.edit-content-btn {
    height: 40px;
    padding-inline: 15px;
    padding-block: 0 !important;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.edit-content-btn:not(:hover) {
    background: #006fbd0d !important;
}
@media screen and (min-width: 1200px) {
    .p-xl-30 {
        padding: 30px !important;
    }
}
.robots-meta-checkbox-card {
    padding: 20px;
    border: 1px solid #3342571a;
    border-radius: 5px;
}
.checkbox--item {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    font-size: 14px;
    margin: 0;
}
.checkbox--item:not(:last-child) {
    margin-bottom: 10px;
}
.checkbox--item input {
    display: none;
}
.checkbox--item .checked {
    display: none;
}
.checkbox--item input:checked ~ .checked {
    display: inline-block;
}
.checkbox--item input:checked ~ .unchecked {
    display: none;
}

.checkbox--item input:checked ~ * {
    font-weight: 600;
}

.robots-meta-checkbox-card .item {
    flex-grow: 1;
}
.h-30 {
    height: 30px;
}
@media screen and (max-width: 450px) {
    .robots-meta-checkbox-card .item,
    .robots-meta-checkbox-card .checkbox--item {
        width: 100%;
    }
}
.check-item-2 {
    display: inline-flex;
    border-radius: 3px;
    background: #eeeeee;
}
.check-item-2 input {
    width: 18px;
    height: 18px;
    border-radius: 3px;
    border: none !important;
}
.check-item-2 input:not(:checked) {
    opacity: 0;
}
.check-item-2 input:checked {
    background-color: #006fbd;
    opacity: 1;
}
.max-w-353px {
    max-width: 353px;
}
.__bg-FAFAFA {
    background: #fafafa;
}
.sorting-card:not(:last-child) {
    border-bottom: 1px solid #e7eaf3;
}
.sorting-card .form--check .form-check-input[type="radio"]:checked,
.sorting-card .toggle-switch-input:checked + .toggle-switch-label {
    background-color: #006fbd !important;
}
.sorting-card .form-check-input:checked + .form-check-label {
    font-weight: 600;
}
.mb-10px {
    margin-bottom: 10px;
}
.mt-10px {
    margin-top: 10px;
}
.inner-collapse-div {
    display: none;
}

.language-warning-modal .modal-content {
    border-left: 4px solid #ff6767;
}
@media screen and (min-width: 450px) {
    .language-complete-modal .modal-dialog {
        max-width: 510px;
        font-size: 12px;
    }
}
.max-w-362px {
    max-width: 362px;
}
.text-9EADC1 {
    color: #9eadc1;
}
.btn--primary[disabled] {
    cursor: not-allowed;
}
.max-w-215px {
    max-width: 215px;
}

.progress-circle-container .progress-circle {
    transform: rotate(-90deg);
}

.progress-circle-container .progress-circle circle {
    fill: none;
    stroke-width: 8;
}

.progress-circle-container .progress-circle .bg {
    stroke: transparent;
}

.progress-circle-container .progress-circle .progress {
    stroke: var(--base-clr);
    stroke-dasharray: 283; /* Circumference of the circle (2 * π * r) where r=45 */
    stroke-dashoffset: 283; /* Initially, hide the progress circle */
    transition: stroke-dashoffset 1s ease;
    stroke-linecap: round;
}
.h-5px {
    height: 5px;
}
div.dataTables_wrapper div.dataTables_length select {
    margin-inline: 8px;
}
.btn.autoTranslate,
.btn.update-lang {
    max-width: 80px;
    margin: 0 auto;
}
.btn-ghost-success {
    background-color: rgba(0, 201, 167, 0.1);
}
.max-w-450px {
    max-width: 450px !important;
}
.min-h-225px {
    min-height: 160px;
}
.dropdown-2 .dropdown-menu {
    padding: 8px;
}
.dropdown-2 .dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 5px;
}
.dropdown-2 .dropdown-menu .dropdown-item:hover {
    background: #fff1e6;
    color: var(--base-clr) !important;
}
.mt-30 {
    margin-top: 30px;
}
.mb-30 {
    margin-bottom: 30px;
}
.mt-20 {
    margin-top: 20px;
}
.mb-20 {
    margin-bottom: 20px;
}

.icon-absolute-on-right {
    position: absolute;
    font-size: 20px;
    opacity: 0.6;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.h-160 {
    height: 160px;
}

video#VisaChipCardVideo {
    max-width: 100%;
    border-radius: 8px;
    height: calc(100% - 24px);
}

.min-h-200 {
    min-height: 200px;
}

.h-200 {
    height: 200px !important;
}

.video video {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    object-fit: cover;
}

.upload-file__img video {
    width: 100%;
    height: 100%;
    border-radius: 6px;
}
.remove-file-button {
    position: absolute;
    right: 15px;
    top: 15px;
    background: transparent;
    border: none;
    outline: none;
    z-index: 999;
    display: none;
    color: red;
}
.promotion-typewise-upload-box {
    display: none;
}
.w-50 {
    width: 50%;
}
.w-75 {
    width: 75%;
}
.w-65 {
    width: 65%;
}
.w-35 {
    width: 35%;
}
.w-25 {
    width: 25%;
}
.text-8797AB {
    color: #8797ab;
}
.profile-prev-image {
    width: 30%;
    max-width: 90px;
    aspect-ratio: 1;
    border-radius: 50%;
}
.profile-preview-box .wishlist-btn {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    position: absolute;
    right: 15px;
    top: 15px;
}
.profile-preview-box .wishlist-btn-2 {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    position: absolute;
    right: 15px;
    top: 15px;
    color: var(--base-clr);
    /* color: white; */
    font-size: 20px;
}

.w-0 {
    width: 0;
}
.static-text {
    display: none;
}
@media screen and (min-width: 1200px) {
    .static-text {
        font-size: 0.9rem;
        line-height: 1.5;
    }
}
.line-limit-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.drp-buttons .btn {
    padding: 0.675rem 0.75rem !important;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}
.drp-buttons .btn-primary {
    --bs-btn-bg: var(--base-clr);
    --bs-btn-border-color: var(--base-clr);
    --bs-btn-hover-bg: var(--base-clr);
    --bs-btn-hover-border-color: var(--base-clr);
    --bs-btn-active-bg: var(--base-clr);
    --bs-btn-active-border-color: var(--base-clr);
    --bs-btn-disabled-bg: var(--base-clr);
    --bs-btn-disabled-border-color: var(--base-clr);
}

.upload-file__img img {
    border-radius: 3px;
    max-width: 100%;
}
.upload-file {
    position: relative;
    cursor: pointer;
}

@media (max-width: 379px) {
    .upload-file {
        inline-size: 100%;
    }
}
.cover_attachment,
.video_attachment,
.upload-file__input {
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
    inline-size: 100%;
    block-size: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-file__img {
    max-inline-size: 180px;
}

.upload-file__img img {
    aspect-ratio: 1/1;
    background-color: white;
    object-fit: cover;
}

.upload-file__img_banner {
    max-inline-size: 100%;
    max-block-size: 180px;
}

@media only screen and (max-width: 1399px) {
    .upload-file__img_banner {
        inline-size: 100%;
    }
}
@media only screen and (min-width: 576px) {
    .upload-file__img_banner img {
        aspect-ratio: 2/1;
    }
}

.upload-file__edit,
.upload-file__close {
    inline-size: 2rem;
    block-size: 2rem;
    background-color: #ffe1e2;
    display: grid;
    place-items: center;
    padding: 0.3125rem;
    color: var(--c2);
    position: absolute;
    inset-inline-end: -0.625rem;
    inset-block-end: -0.625rem;
}

.upload-file__edit {
    background-color: #c1cbff;
    color: var(--base-clr);
}

.upload-file__edit .material-icons,
.upload-file__close .material-icons {
    font-size: 1rem;
}

.upload-file__actions {
    position: absolute;
    inset-inline-end: 0.625rem;
    inset-block-start: 0.625rem;
}

.upload-file__actions .upload-file__delete_btn {
    background-color: white !important;
}

.upload-file__actions .upload-file__delete_btn:hover {
    background-color: var(--c2) !important;
}
.fs-12 {
    font-size: 12px;
}

.bg--secondary {
    background-color: #F7F8FA !important;
}

.h-200 {
    height: 200px !important;
}

.form--check-2 {
    display: flex;
    align-items: center;
    padding: 0;
    position: relative;
    margin: 0;
    gap: 10px;
}
.form--check-2 .form-check-input {
    margin: 0 !important;
    min-width: 16px;
    min-height: 16px;
    box-shadow: none !important;
    outline: none !important;
    position: relative;
}
.form--check-2 .form-check-input:checked {
    background-color: var(--primary-clr) !important;
    border-color: var(--primary-clr) !important;
}
.form--check-2 .form-check-input:not(:checked) ~ .form-check-label {
    opacity: 0.75;
}
.me-3 {
    margin-inline-end: 1rem;
}
.review-rating-demo {
    position: absolute;
    top: -35px;
    right: 15px;
    border: 2px solid #fff;
    border-radius: 55px;
    background: #039d55;
    color: white;
    z-index: 9;
    display: flex;
    align-items: center;
    padding: 2px 6px;
    font-size: 10px;
    gap: 4px;
}

.review-rating-demo * {
    font-size: 10px;
}
.arrow-icon {
    display: flex;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: var(--primary-clr);
    background: #006fbd0d;
    justify-content: center;
    align-items: center;
    font-size: 20px;
}

.flex-grow {
    flex-grow: 1;
}
.approve-buttons .btn {
    padding: 9px 15px 9px 13px;
    font-size: 14px;
    text-transform: capitalize;
    font-weight: 700;
    line-height: 1.3;
}
.approve-buttons .btn.btn-outline-success:not(:hover) {
    background: rgba(3, 157, 85, 0.1);
}
.approve-buttons .btn.btn-outline-danger:not(:hover) {
    background: rgba(255, 109, 109, 0.1) !important;
}
.badge {
    text-transform: capitalize;
}
.max-w-130px {
    max-width: 130px;
}
.max-w-260px {
    max-width: 260px;
}
.add-profile-image {
    max-width: 130px;
}
.add-profile-banner {
    max-width: 260px;
}
@media screen and (min-width: 576px) {
    .add-profile-image {
        max-width: 32%;
    }
    .add-profile-banner {
        max-width: 64%;
    }
}
.add-profile-banner h4,
.add-profile-image h4 {
    font-size: 14px;
}

@media screen and (max-width: 575px) {
    .approve-buttons .btn {
        padding: 5px 10px 5px 8px;
        font-size: 12px;
        font-weight: 600;
    }
}
.before-content::after {
    content: "";
    position: absolute;
    inset: 0;
    z-index: 1;
    cursor: pointer;
}

.max-w-400px {
    max-width: 400px;
}
.badge-soft-warning {
    background: #fff2e5;
    color: #d46819;
}
.max-w-700 {
    max-width: 700px;
}
.max-w-471 {
    max-width: 471px;
}
.max-w-520 {
    max-width: 520px;
}
.px-20 {
    padding-inline: 20px;
}
.promo-card h4 {
    color: #212b36;
    font-size: 14px;
    font-weight: 600;
}
.promo-card {
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: 5px;
    text-align: center;
    padding: 20px 15px;
    color: #4b5864;
    font-size: 12px;
    white-space: initial;
    letter-spacing: -0.025em;
}
.promo-card .position-relative {
    z-index: 1;
}
.promo-card::after,
.promo-card::before {
    content: "";
    position: absolute;
    width: 126px;
    height: 115px;
    background: #039d551a;
    border-radius: 50%;
}

.promo-card::before {
    top: -30px;
    right: -30px;
}

.promo-card::after {
    bottom: -30px;
    left: -30px;
}
.restaurant-dashboard-wrapper .promo-card::after,
.restaurant-dashboard-wrapper .promo-card::before {
    background: #0013560d;
}
.restaurant-dashboard-wrapper .promo-card {
    border: 1px solid #f4f4f4;
    box-shadow: 0px 5px 5px rgba(51, 66, 87, 0.05);
    border-radius: 5px;
    background: #4153b30d;
}
.restaurant-dashboard-wrapper .promo-card,
.restaurant-dashboard-wrapper-card {
    width: 100%;
}
@media screen and (min-width: 1200px) {
    .restaurant-dashboard-wrapper-card {
        width: 0;
        flex-grow: 1;
    }
    .restaurant-dashboard-wrapper .promo-card {
        width: 225px;
    }
}
.fs-15 {
    font-size: 15px;
}
.table-rest-info .w-80px {
    width: 80px;
    height: 80px;
}
.__alert-3 {
    width: calc(100% - 40px);
    top: 80px;
    right: 20px;
    position: fixed;
    max-width: 591px;
    background: #ffffff;
    border: 1px solid #e6e8ec;
    box-shadow: 0px 12px 32px rgba(6, 28, 61, 0.08);
    border-left: 3px solid var(--primary-clr);
    padding: 24px;
    border-radius: 8px;
    z-index: 999;
}
.__alert-3 .alert-inner {
    display: flex;
    align-items: flex-start;
    padding: 14px;
    gap: 10px;
}
.__alert-3 .alert-inner .cont {
    width: 0;
    flex-grow: 1;
    white-space: initial;
    word-break: break-all;
    font-size: 14px;
}
.__alert-4 {
    width: calc(100% - 40px);
    top: 80px;
    right: 20px;
    position: fixed;
    max-width: 591px;
    background: #ffffff;
    border: 1px solid #e6e8ec;
    box-shadow: 0px 12px 32px rgba(6, 28, 61, 0.08);
    border-left: 5px solid var(--base-clr);
    padding: 24px;
    border-radius: 8px;
    z-index: 999;
    padding-right: 60px;
}
.__alert-4 .alert-inner {
    display: flex;
    align-items: flex-start;
    padding: 14px;
    gap: 10px;
}
.__alert-4 .alert-inner .cont {
    width: 0;
    flex-grow: 1;
    white-space: initial;
    word-break: break-all;
    font-size: 14px;
}
/* body {
    word-break: break-word;
} */
.top-50 {
    top: 50%;
    transform: translateY(-50%);
}

.__alert-4 .right-0 {
    right: 10px !important;
}
.promo-card-2 {
    background: #0055a3;
    border-radius: 5px;
    padding: 11px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    text-align: left;
    gap: 12px;
    position: relative;
    overflow: hidden;
}
.promo-card-2 .shapes {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
}
.promo-card-2 a {
    position: relative;
}
.promo-card-2 .left {
    width: 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}
.promo-card-2 .left .inner {
    width: 0;
    flex-grow: 1;
}
@media screen and (max-width: 500px) {
    .promo-card-2 .left {
        width: 100%;
    }
}
.profile-preview-box,
.video-preview-box {
    display: none;
}
.p-20px {
    padding: 20px;
}

.sorting-card .form--check .form-check-input[type="radio"]:checked,
.sorting-card .toggle-switch-input:checked + .toggle-switch-label {
    background-color: var(--primary-clr) !important;
}
@media screen and (max-width: 1600px) {
    .promotion-typewise-upload-box .upload-file__img img {
        max-height: 110px !important;
        aspect-ratio: unset !important;
    }
}
.navbar-vertical-content .navbar-nav {
    padding-bottom: 80px;
}
.input-group .btn--primary {
    border-radius: 0 5px 5px 0 !important;
}
.input-token-group .form-control {
    padding-right: 40px;
    border-radius: 5px 0 0 5px !important;
}
.input-token-group .position-relative {
    flex-grow: 1;
    width: 0;
}
.input-token-group .generate-code {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 0;
    width: 40px;
}
.how-it-works-modal .modal-dialog {
    max-width: 1007px;
}
.how-it-count {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 16px;
    font-weight: 600;
}
.how-it-count::after {
    width: 0;
    flex-grow: 1;
    height: 1px;
    display: block;
    background: #9eadc14d;
    content: "";
}
@media screen and (max-width: 767px) {
    .how-it-count::after {
        display: none;
    }
}
.how-it-count span {
    width: 35px;
    height: 35px;
    border-radius: 6px;
    color: #107980;
    background: #009faa0d;
    display: flex;
    align-items: center;
    justify-content: center;
}
.__nav-module-item-drivemond * {
    color: #2b4d66;
}
.__nav-module-item-drivemond {
    color: #2b4d66;
    text-align: center;
    padding: 10px;
    background: #ffffff;
    border: 1px solid rgba(0, 159, 170, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    gap: 8px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 159, 170, 0.2);
}

.__nav-module-item-drivemond:hover {
    background: #009faa;
    color: #ffffff;
    border: 1px solid #009faa;
}
.__nav-module-item-drivemond:hover * {
    color: #ffffff;
}

/* Mutiple Select2 */

.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    .select2-selection__choice {
    display: inline-flex;
    align-items: center;
    padding: 0 5px;
    margin: 2px;
    background-color: #e4e4e4;
    border-radius: 4px;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    .select2-selection__choice__remove {
    cursor: pointer;
    margin-left: 5px;
    color: #333;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    .close-icon {
    cursor: pointer;
    color: #00000078;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    .select2-selection__rendered
    li {
    list-style: none;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    ul.select2-selection__rendered
    .select2-search
    input {
    width: 100% !important;
    margin: 0 !important;
    height: 30px;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    ul.select2-selection__rendered
    .select2-search {
    width: 30px;
    flex-grow: 1;
    margin-right: -15px;
    height: 30px;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    ul.select2-selection__rendered {
    display: flex;
    height: 38px;
    align-items: center;
    padding: 0;
    margin: 0;
    gap: 5px;
}
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    ul.select2-selection__rendered
    .name {
    padding: 5px;
    border-radius: 3px;
    background: #009faa26;
    color: #333;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.pickup-zone-tag
    .multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    ul.select2-selection__rendered
    .name {
    background: rgba(51, 66, 87, 0.06) !important;
    border-radius: 33px !important;
    color: rgba(51, 66, 87, 0.9) !important;
    font-weight: 500;
    padding: 5px 7px;
}

.pickup-zone-tag .select2-selection__rendered span {
    margin-left: 3px;
}

.select2-container .more {
    background: var(--primary-clr);
    border-radius: 30px;
    color: #ffffff;
    font-weight: 600;
    font-size: 13px;
    padding: 4px 12px;
}

/* Optional: Add a plus sign for remaining items */
.multiple-select2
    + .select2-container--default
    .select2-selection--multiple
    .select2-selection__rendered::after {
    content: attr(data-placeholder);
    color: #334257;
    font-weight: 600;
    display: inline-block;
    width: auto;
    text-align: center;
    background: transparent;
    margin-left: auto;
    display: none;
}
.table-responsive {
    position: static;
}
.table-responsive .dropdown {
    position: static;
}
.table-responsive .dropdown .dropdown-menu {
    top: unset;
    left: unset;
    right: unset;
    inset-inline-end: 0px;
}

.conversation-tooltip {
    font-size: 10px;
}
.conversation-tooltip .tooltip-arrow {
    left: -30% !important;
}
.form--check--inline {
    padding: 16px 20px !important;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.form--check--inline .form-check-label {
    display: flex;
    align-items: center;
    gap: 4px;
    width: 0;
}
.form--check--inline .form-check-input {
    position: relative;
    inset: unset;
    transform: translateY(-2px);
}
/* .coupon-slider .owl-item {
    width: 256px;
} */
.coupon-slider-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    border-radius: 8px;
    background: #f7f7f7;
    border: 1px solid #f7f7f7;
    width: 250px;
    border: none;
    outline: none;
    box-shadow: none;
    color: rgba(51, 66, 87, 0.6);
    position: relative;
}
.coupon-slider-button h6 {
    font-size: 14px;
    font-weight: 500;
    color: rgba(51, 66, 87, 1);
    margin: 0;
}
.coupon-slider-button small {
    font-size: 12px;
}
.coupon-slider-button .right {
    position: relative;
    width: 80px;
    color: var(--primary-clr);
}
.coupon-slider-button .left {
    text-align: left;
}
.coupon-slider-button .right h6 {
    font-weight: 900;
    color: var(--primary-clr);
}
.coupon-slider-button .right::before {
    content: "";
    position: absolute;
    left: 0;
    top: -4px;
    height: calc(100% + 8px);
    border-left: 2px dashed #fff;
}
.coupon-slider-button.active .right::before {
    border-color: var(--primary-clr);
    opacity: 0.5;
}
.coupon-slider-item {
    padding-top: 6px;
    padding-right: 6px;
}

.coupon-slider-button.active {
    border-color: var(--primary-clr);
    background: rgba(52, 195, 128, 0.1);
}
.coupon-slider-button .checkmark {
    color: var(--primary-clr);
    top: -5px;
    right: -5px;
    position: absolute;
    z-index: 1;
    animation: zoomIn 0.2s;
    -webkit-animation: zoomIn 0.2s;
    -moz-animation: zoomIn 0.2s;
    font-size: 20px;
}
.coupon-slider-button:not(.active) .checkmark {
    display: none;
}
.suggestion_dropdown .dropdown-menu {
    width: 100% !important;
    height: 150px;
    overflow-y: auto;
}

/* datepicker css */
.daterangepicker .ranges ul {
    min-width: 0;
}

.daterangepicker .ranges ul li {
    font-size: 0.65rem;
}
.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
    width: 1.5rem;
    min-width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
    line-height: 1.35rem;
    min-width: 0;
}

.daterangepicker .calendar-table th {
    font-size: 0.65rem;
}

.daterangepicker .calendar-table .month {
    font-size: 0.75rem;
}

ul.address-info .label {
    min-width: 150px;
    display: inline-block;
}

/* Multiple input group */
.multiple-input-group {
    border: 0.0625rem solid #e7eaf3;
    border-radius: 0.3125rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.multiple-input-group:focus {
    border-color: var(--primary-clr);
}
.multiple-input-group .form-control {
    border: none !important;
    margin-left: 40px;
}
.multiple-input-group .form-control:first-child {
    border-inline-end: 1px solid rgba(0, 0, 0, 0.1) !important;
}
.multiple-input-group label {
    position: absolute;
    top: 12px;
    left: 15px;
}
.multiple-input-group .custom-select {
    border: none !important;
    border-radius: 0;
    background-color: red;
    color: var(--title-clr);
    font-weight: 400;
}
.js-select2-custom + .select2 .select2-selection__rendered .select2-selection__choice{
    background-color: #F0F2F5 !important;
    margin: 0.39rem 0.25rem 0.25rem 0;
}
.js-select2-custom + .select2 .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove{
    background-color: #C8C8C8 !important;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    padding-bottom: 2.5px;
}

/* Taxi module provider-details */
.taxi-banner {
    padding-top: 180px;
    /*background: url("../img/1920x400/img1.jpg") no-repeat center center/cover;*/
    overflow: hidden;
}
.taxi-info-wrapper {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(30px);
    padding: 30px;
}
.taxi-info-wrapper .logo {
    margin-top: -70px;
}

/* pdf */
.pdf-single {
    position: relative;
    display: inline-block;
    width: 280px;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    cursor: pointer;
}

.pdf-frame {
    overflow: hidden;
}

.pdf-frame iframe {
    width: 100%;
    height: 120px;
    border: none;
    overflow: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.pdf-frame iframe::-webkit-scrollbar {
    display: none;
}

.pdf-single .overlay,
.download-image-single .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: center;
    border-radius: 5px;
    transition: opacity 0.3s ease;
}

.pdf-info {
    /* position: absolute;
    bottom: 0; */
    background-color: #fff;
    box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 14px 11px;
    width: 100%;
}

.pdf-single .download-btn,
.download-image-single .download-btn,
.identify-image-single .download-btn {
    position: absolute;
    inset-block-start: 10px;
    inset-inline-end: 10px;
    background: rgba(27, 90, 61, 0.7);
    color: #fff;
    block-size: 26px;
    inline-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3.79px;
}

.pdf-single .remove-btn,
.remove-btn_doc,
.image-single .remove-btn,
.image-general .remove-btn,
.download-image-single .remove-btn {
    position: absolute;
    inset-block-start: -10px;
    inset-inline-end: -10px;
    background: #ff4949;
    color: #fff;
    block-size: 26px;
    inline-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100px;
}

.image-general {
    position: relative;
}

.pdf-single .pdf-thumbnail-alt,
.pdf-single .pdf-thumbnail {
    height: 120px;
    object-fit: cover;
    object-position: top;
    width: 100%;
    border-radius: 5px;
}

.pdf-single .file-name {
    display: inline-block;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
}

.download-image-single {
    position: relative;
    display: inline-block;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
}
.download-image-single .image-frame img {
    max-width: 240px;
    height: 120px;
    object-fit: cover;
}

.zone-btn:focus {
    color: #fff !important;
    background-color: var(--primary-clr) !important;
    box-shadow: none;
}
.zone-btn {
    flex-grow: 1;
    min-width: max-content;
}

.custom-group-btn {
    /* border: 1px solid rgba(51, 66, 87, 0.2); */
    border: 1px solid #e7eaf3;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0;
    border-radius: 5px;
    cursor: pointer;
}

.custom-group-btn .item:first-child::after {
    content: "";
    width: 1px;
    height: 31px;
    background-color: rgba(0, 0, 0, 0.1);
    position: absolute;
    inset-inline-end: 0;
    inset-block-start: 50%;
    transform: translateY(-50%);
}

.custom-group-btn .item {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0;
}

.custom-group-btn .floating-label {
    position: absolute;
    inset-block-start: 12px;
    inset-inline-start: 16px;
    color: var(--title-clr);
}

.custom-group-btn .form-control {
    padding-inline-start: 55px;
    box-shadow: none;
    width: 100%;
    color: var(--title-clr);
}

.custom-group-btn:not(:has(.floating-label)) .form-control {
    padding-inline-start: 0.938rem;
}

.custom-group-btn .form-control:focus,
.custom-group-btn .custom-select:focus {
    box-shadow: none;
}

.custom-group-btn .custom-select {
    width: 100%;
    background-color: #f3f4f5;
    border-radius: 0;
    color: var(--title-clr);
}

/* custom timeline */
.custom-timeline {
}
.custom-timeline .single {
    display: flex;
    gap: 10px;
    align-items: center;
    color: var(--title-clr);
    font-weight: 500;
    /* opacity: 0.7; */
}
.custom-timeline .single.checked {
    opacity: 1;
    font-weight: 600;
}
.custom-timeline .single span {
}
.custom-timeline .single .count {
    font-weight: 700;
    background-color: #0a1123;
    color: #fff;
    border: 1px solid #0a1123;
    width: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: relative;
}
.custom-timeline .single .count-checked {
    font-weight: 700;
    background-color: transparent;
    color: var(--primary-clr);
    border: 1px solid var(--primary-clr);
    width: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: relative;
}
.custom-timeline .single .count2 {
    font-weight: 700;
    /*background-color: var(--primary-clr);*/
    /*color: #fff;*/
    border: 1px solid rgba(51, 66, 87, 0.7);
    width: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: relative;
}

.custom-timeline .single.checked .count {
    /*background-color: var(--primary-clr);*/
    border-color: var(--primary-clr);
    color: var(--primary-clr);
}

.custom-timeline .single.checked .count-checked::after {
    content: url("../img/icons/checked.png");
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
    block-size: 100%;
    inline-size: 100%;
    color: var(--primary-clr);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: transparent !important;
}

.business-plan-card-wrapper {
    display: block;
}
.business-plan-radio {
    display: none;
}

.business-plan-card {
    border: 0.62px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 20px;
}

.business-plan-radio:checked + .business-plan-card {
    border: 0.62px solid var(--primary-clr);
    background-color: color-mix(in srgb, var(--primary-clr), transparent 95%);
}

.business-plan-radio:checked + .business-plan-card .title {
    color: var(--primary-clr) !important;
    position: relative;
}

.business-plan-radio:checked + .business-plan-card .title::after {
    content: "";
    position: absolute;
    inset-inline-end: 0;
    inset-block-end: 0;
    block-size: 20px;
    inline-size: 20px;
    background: url("../img/icons/check.svg") no-repeat center center;
    background-size: 10px 10px;
    line-height: 1;
    background-color: var(--primary-clr);
    color: #ffffff;
    border-radius: 50%;
}

.business-plan-radio:checked + .business-plan-card p {
    opacity: 0.8;
}

.plan-slider.owl-carousel .owl-item {
    max-width: 250px !important;
    display: inline-block;
}

.plan-slider.owl-carousel .owl-stage {
    display: flex;
    justify-content: center;
}

.text-end {
    text-align: end;
}

.hs-unfold.m-0 {
    margin: 0 !important;
}

.w--150px {
    width: 150px;
}

.m-0 {
    margin: 0 !important;
}

.upload-file-wrapper {
    --border-clr: #e5e5e5;
    inline-size: 100%;
    max-inline-size: 180px;
    block-size: auto;
    min-block-size: 180px;
    border-radius: 10px;
    background: #fafafa;
    border: 1px dashed var(--border-clr);
    display: flex;
    justify-content: center;
    align-items: center;
}

.upload-file-img {
    inline-size: 100%;
    block-size: 100%;
    max-inline-size: 180px;
    max-block-size: 180px;
    aspect-ratio: 1 / 1;
    border-radius: 10px;
    object-fit: cover;
}

.upload-file-textbox h6 span {
    --info-clr: #0788ff;
    color: var(--info-clr);
}

.upload-file-wrapper.three-one {
    max-inline-size: 525px;
    min-block-size: 175px;
}

.upload-file-wrapper.three-one .upload-file-img {
    inline-size: 100%;
    block-size: auto;
    aspect-ratio: 3 / 1;
    max-inline-size: 525px;
    max-block-size: 175px;
    object-fit: cover;
}

.upload-file-wrapper.fullwidth {
    max-inline-size: 100%;
    min-block-size: 175px;
}

.upload-file-wrapper.fullwidth .upload-file-img {
    inline-size: 100%;
    block-size: auto;
    aspect-ratio: 3 / 1;
    max-inline-size: 100%;
    max-block-size: 175px;
    object-fit: cover;
}

.title.border-line .info {
    border-inline-start: 1px solid #d8d8d8;
    margin-inline-start: 10px;
    padding-inline-start: 10px;
    line-height: 1;
}

.table.similar th,
.table.similar td {
    width: 16.66%;
}

.min-w-110px {
    min-width: 110px;
}

.floating-review-wrapper .rating--review {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-color: color-mix(
        in srgb,
        var(--primary-clr) 30%,
        transparent
    ) !important;
    cursor: pointer;
    white-space: nowrap;
}

.floating-review-wrapper .rating--review-right {
    position: absolute;
    inset-inline-end: 0;
    inset-block-start: 30x;
    background: #fff;
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
    padding: 15px;
    border-radius: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 1;
}

.floating-review-wrapper:hover .rating--review-right {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.color-758590 {
    color: #758590;
}

.nav-tabs.flex-nowrap {
    overflow-x: auto;
    overflow-y: hidden;
}

/* details view slider */
.cz-product-gallery {
    display: block;
}
.cz-product-gallery .cz-preview {
    border: 1px solid transparent;
    border-radius: 5px;
    margin: 0;
    padding: 5px;
}
.cz-preview {
    width: 100%;
    margin-top: 0;
    margin-left: 0;
    max-height: 100% !important;
    background: white;
    border-radius: 10px;
}
.product-preview-item {
    z-index: 1;
    width: 100%;
    height: 100%;
    aspect-ratio: 290/145;
    overflow: hidden;
    border-radius: 5px;
    transition: opacity 0.3s ease-in-out;
}
.cz-product-gallery .cz {
    padding: 0 5px;
}
[data-simplebar] {
    overflow-y: auto;
    padding-right: 0 !important;
}
.product-preview-thumb {
    display: block;
    position: relative;
    aspect-ratio: 80/40;
    transition: border-color 0.2s ease-in-out;
    border-radius: 0.3125rem;
    text-decoration: none !important;
    overflow: hidden;
    margin: 10px 0 0;
}
[dir="rtl"] .cz-image-zoom {
    direction: rtl;
}

[dir="rtl"] .cz-image-zoom-pane {
    direction: ltr;
}

.input-label {
    color: var(--title-clr);
}
.opacity-80 .select2-selection__rendered span {
    opacity: 0.8;
}

.form-check-input.single-select {
    width: 22px;
    height: 22px;
    -webkit-accent-color: #14b19e;
    -moz-accent-color: #14b19e;
    accent-color: #14b19e;
    opacity: 0.5;
}
.form-check-input.single-select:checked {
    opacity: 1;
}

.form-check-input.single-select:checked::after {
    content: "✔";
    color: white;
    background-color: #14b19e;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.resturant-type-group .item {
    flex-basis: 50%;
    white-space: nowrap;
}

.pl-unset {
    padding-inline-start: 0.875rem !important;
}

.equal-width > * {
    flex: 1;
}

@media screen and (max-width: 991px) {
    .equal-width .plus-btn {
        max-width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        align-self: end;
    }
}

.text-wrapper .upload-file__img {
    --size: auto;
    --border-clr: #e5e5e5;
    border: 1px dashed var(--border-clr);
    background: transparent;
    padding: 1rem;
    border-radius: 10px;
}

.text-wrapper .upload-file__img__img {
    aspect-ratio: initial;
    object-fit: cover;
}

.text-wrapper .upload-file__img__img.border--dashed {
    --border-clr: #e5e5e5;
    border: 1px dashed var(--border-clr);
}

.text-wrapper .upload-file__img__img.ratio-2 {
    aspect-ratio: 2;
}

.text-info {
    color: var(--info-clr);
}

#coba.custom .file_upload {
    --bs-border-color: #e5e5e5;
    padding: 0 !important;
    border-radius: 10px !important;
    border: 1px dashed var(--bs-border-color) !important;
    background: #fafafa !important;
    display: inline-block;
    aspect-ratio: 2;
}

[theme="dark"] #coba.custom .file_upload {
    background: transparent !important;
}

#coba.custom .spartan_image_placeholder {
    position: relative;
    width: 34px;
    height: 34px;
    margin-top: 16px !important;
    border: none !important;
}

#coba.custom .img_ {
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
}

#coba.custom .spartan_remove_row {
    z-index: 9;
}

#coba.custom .upload-file__img_banner {
    inline-size: 100%;
    max-inline-size: 200px;
    block-size: auto;
}

#coba.custom .spartan_item_wrapper {
    max-inline-size: 200px !important;
}
#coba.custom .spartan_item_wrapper .file_upload img.img_ {
    aspect-ratio: 2;
    object-fit: cover;
}

.upload-file__textbox.pdf {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.document-wrapper {
    inline-size: 280px;
    block-size: 120px;
}
.pdf-frame iframe img,
.pdf-frame iframe svg {
    margin: auto;
    object-fit: cover;
}

.upload-file__textbox {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: #fafafc;
    height: 100%;
    width: 100%;
}
.upload-file__textbox .text-info {
    color: #0788ff !important;
}
.max-w-165 {
    max-width: 165px;
}
.max-w-180 {
    max-width: 180px;
}
.min-w-330 {
    min-width: 330px;
}
.w--200px {
    width: 200px;
}
.h-165 {
    height: 165px;
}
.h-180 {
    height: 180px;
}
.max-w-200px {
    max-width: 200px;
}

.img--vertical-2 {
    aspect-ratio: 2;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

#dropAreaLabel .text-info {
    color: #0788ff !important;
}

.image-single {
    position: relative;
    display: inline-block;
    width: 100%;
    height: 100%;
    border: 1px dashed var(--border-clr);
    border-radius: 10px;
}

.border-000-01 {
    border-color: rgba(0, 0, 0, 0.1) !important;
}

.form-group .input-icon {
    position: absolute;
    inset-inline-end: 0.5rem;
    inset-block-start: 50%;
    transform: translateY(-50%);
}

.rotate-45 {
    transform: rotate(45deg);
}

.form-group.text-title
    input[type="datetime-local" i]::-webkit-calendar-picker-indicator {
    opacity: 0.6 !important;
}

.custom-select-arrow {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: url("../img/arrow-down.svg") 96% / 15% no-repeat;
    background-position: right 0.5rem center;
    background-size: 18px 18px;
}

[dir="rtl"] .custom-select-arrow {
    background-position: left 0.5rem center;
}

.bg--F6F6F6 {
    background-color: #f6f6f6;
}
.bg--EDEDED {
    background-color: #ededed;
}

.trip-details-address li {
    padding-inline-start: 45px;
    padding-inline-end: unset;
    position: relative;
    display: flex;
}
.trip-details-address li:first-child::before {
    content: "";
    inset-inline-start: 16px;
    inset-block-start: 22px;
    height: calc(100% - 20px);
    width: 1px;
    border-left: 2px dashed rgba(0, 0, 0, 0.3);
    position: absolute;
    opacity: 0.5;
}
.trip-details-address li .svg {
    position: absolute;
    inset-inline-start: 0;
    inset-inline-end: unset;
    inset-block-start: 0;
}
.trip-details-address li:not(:last-child) {
    padding-bottom: 35px;
}

.max-h-160px {
    max-height: 160px !important;
}

.map-fullscreen-btn_wrapper {
    position: absolute;
    inset-block-start: 10px;
    inset-inline-end: 10px;
}
.map-fullscreen-btn_wrapper .shadow--card-2.btn:focus {
    box-shadow: 0px 5px 15px rgba(51, 66, 87, 0.08) !important;
}

/* custom route line draw design */
.custom_map_canvas .gm-ui-hover-effect > span {
    background-color: #fff;
}
.custom_map_canvas .gm-style .gm-style-iw-c {
    background: #000 !important;
    color: #fff;
    padding: 0.5rem !important;
}
/* close btn hide */
.custom_map_canvas .gm-style-iw-chr {
    /* display: none !important; */
    overflow: hidden !important;
    padding-bottom: 0.5rem !important;
}
.custom_map_canvas .gm-style-iw-chr .gm-style-iw-ch {
    display: none !important;
}
.custom_map_canvas .gm-style-iw-chr .gm-ui-hover-effect {
    position: absolute !important;
    inset-inline-end: 0;
    inset-block-start: 0;
}
.custom_map_canvas .gm-style-iw-chr .gm-ui-hover-effect,
.custom_map_canvas .gm-style-iw-chr .gm-ui-hover-effect span {
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
    padding: 0 !important;
}
.custom_map_canvas .gm-style-iw-chr .gm-ui-hover-effect span {
    background-color: var(--danger-clr);
}
.custom_map_canvas .gm-style-iw-d {
    overflow: hidden !important;
}
.custom_map_canvas .gm-style .gm-style-iw-tc::after {
    background: rgba(0, 0, 0, 0.9) !important;
}
.custom_map_canvas .poi-info-window div,
.poi-info-window a {
    background: rgba(0, 0, 0, 0.9) !important;
}

.custom_map_canvas .poi-info-window div {
    color: #fff !important;
}

.custom_map_canvas .poi-info-window a {
    color: var(--base-clr-2) !important;
}

/* custom route line draw design ends */

.w--100px {
    width: 100px !important;
}
.p-5px {
    padding: 5px !important;
}

.radius-15 {
    border-radius: 15px !important;
}

.select-item select:valid {
    opacity: 1;
}

.rounded-10 {
    border-radius: 10px !important;
}

.overflow-x-auto {
    overflow-x: auto !important;
}

@media (max-width: 575px) {
    #pdf-container .document-wrapper,
    #pdf-container .pdf-single {
        inline-size: 200px;
    }
}

.table-layout-fixed {
    table-layout: fixed;
}

.toggle-switch.dark .toggle-switch-input:checked + .toggle-switch-label {
    background-color: color-mix(
        in srgb,
        var(--title-clr),
        transparent 40%
    ) !important;
}

.height-custom .select2-container--default .select2-selection--multiple {
    height: 45px !important;
}
@media screen and (min-width: 1200px) {
    .update-quantity-modal .modal-dialog {
        max-width: 850px;
    }
    .w-200 {
        width: 200px;
    }
}

.__alert-4 {
    width: calc(100% - 40px);
    top: 80px;
    right: 20px;
    position: fixed;
    max-width: 591px;
    background: #ffffff;
    border: 1px solid #e6e8ec;
    box-shadow: 0px 12px 32px rgba(6, 28, 61, 0.08);
    border-left: 5px solid var(--base-clr);
    padding: 24px;
    border-radius: 8px;
    z-index: 999;
    padding-right: 60px;
}
.__alert-4 .alert-inner {
    display: flex;
    align-items: flex-start;
    padding: 14px;
    gap: 10px;
}
.__alert-4 .alert-inner .cont {
    width: 0;
    flex-grow: 1;
    white-space: initial;
    word-break: break-all;
    font-size: 14px;
}

.navbar-vertical-aside-mini-mode .promo-card {
    display: none;
}

.driverListCollapseBtn i {
    transition: transform 0.3s ease-in-out;
}

.driverListCollapseBtn[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.driverListCollapseBtn[aria-expanded="false"] i {
    transform: rotate(0deg);
}

.min-w-auto {
    min-width: auto !important;
}

@media (max-width: 575px) {
    .min-w-sm-auto {
        min-width: auto !important;
    }
}

.vertical-line-separator {
    width: 1px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.3);
}

.w--180px {
    width: 180px;
}

.upload-file-wrapper.aspect-3-2 {
    max-inline-size: 264px;
    min-block-size: 200px;
}

.upload-file-wrapper .upload-file-img {
    max-inline-size: 100% !important;
    max-block-size: 100% !important;
}

/* for language tabs */
.nav-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    flex-wrap: nowrap;
}

.nav-tabs .lang_link {
    white-space: nowrap !important;
}
/* language tabs ends */

.min-h-100 {
    min-height: 100% !important;
}

@media (max-width: 575px) {
    .min-w-100-mobile {
        min-width: 100% !important;
    }
}

.product-thumb-slider .owl-nav .owl-prev,
.product-thumb-slider .owl-nav .owl-next {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-top: 6px;
}
.product-thumb-slider .owl-nav .owl-prev::before,
.product-thumb-slider .owl-nav .owl-next::before {
    display: inline-block;
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    transform: rotate(320deg) translate(-1px);
}
.product-thumb-slider .owl-nav .owl-prev.disabled,
.product-thumb-slider .owl-nav .owl-next.disabled {
    display: none;
}
.product-thumb-slider .owl-nav .owl-prev {
    left: 0;
}
.product-thumb-slider .owl-nav .owl-prev::before {
    transform: rotate(135deg) translate(-1px, -1px);
}
@media screen and (max-width: 1199px) {
    .product-thumb-slider .owl-nav .owl-prev {
        left: 0;
    }
}
.product-thumb-slider .owl-nav .owl-next {
    right: 0;
}
@media screen and (max-width: 1199px) {
    .product-thumb-slider .owl-nav .owl-next {
        right: 0;
    }
}

.min-w150 {
    min-width: 150px;
}

.dropdown-icon + .select2 {
    position: relative;
}

.basic-multiple-select2 + .select2-container--default::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;

    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.text-limit-20 {
    width: 20ch;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select2-container--default .custom-select.select2-selection--multiple,
.select2-container--default .form-control.select2-selection--multiple {
    height: 45px;
}
.display-none{
    display: none;
}

.search-list-item {
    padding: 1rem;
    background-color: transparent;
    /* border-radius: 10px; */
    transition: all 0.3s ease;
}

.search-list-item:not(:last-child) {
    border-bottom: 1px solid #e9e9ea;
}

.search-list-item:hover {
    background-color: #f0f2f7;
}

.search-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    background-color: #f9fafc;
    border-radius: 0.3125rem;
    animation: slideDown 0.3s ease-out;
}

.min-h-350 {
    min-height: 350px;
}

.ctrlplusk {
    letter-spacing: -0.3px;
}

.modal.fade.removeSlideDown .modal-dialog {
    transition: none;
    transform: none;
}

.highlighted-keyword {
    background-color: #f5ab05a8;
    font-weight: bold;
}

.alert-primary-light {
    color: var(--theameColor);
    background-color: rgba(0, 154, 241, 0.1);
    border-color: rgba(0, 154, 241, 0);
}
.radio-disable-bg {
    background-color: #14b19f53 !important;
}
.bg-E7E6E8 {
    background-color: #E7E6E8 !important;
}
.h-300 {
    height: 300px !important;
}
  @media (min-width: 1399px) {
    .w-xxl-200px{
        width: 200px !important;
    }
}
.mark {
    background-color: #ffe58f;
    padding: 0 2px;
    border-radius: 2px;
}

.text-italic {
 font-style: italic;
}


/*New css Add 3.1*/
.h-100-vh{
    height: 100vh;
}
.h-100px{
    height: 100px;
}
.d-center{
    display: flex;
    align-items: center;
    justify-content: center;
}
.d-align-center{
    display: flex;
    align-items: center;
}
.d-content-between{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.info--outline{
    border: 1px solid var(--info-clr);
}
.info-hover:hover{
    background: var(--info-clr);
    color: #fff !important;
}
.fz-18{
    font-size: 18px;
}
.pe-30{
    padding-inline-end: 30px;
}
.p-30{
    padding: 30px;
}
.p-24 {
    padding: 24px;
}
.p-15{
    padding: 15px;
}
.p-12{
    padding: 12px;
}
.ps-20{
    padding-inline-start: 18px !important;
}
.mb-15{
    margin-bottom: 15px;
}
.h-20px{
    height: 20px;
}
.h-25px{
    height: 25px;
}
.max-w-220px {
    max-width: 220px;
}
.min-w-25px{
    min-width: 25px !important;
}
.min-w-50{
    min-width: 50px;
}
.mx-650{
    max-width: 650px;
}
.w-220{
    width: 220px;
}
.min-w-370{
    min-width: 330px;
}
.min-w-220{
    min-width: 220px;
}
.title-clr{
    color: var(--title-clr);
}
.list-none li{
    list-style: none;
}
.info-notes-bg{
    background: #E5EDFF;
}

img{
    max-width: 100%;
}

.bg-opacity-primary-10{
    background: #EFF4FF;
}
.bg-opacity-primary-5{
    background: #0661CB0D;
}
.bg-opacity-warning-10{
    background: #FFF9EC;
}
.bg-opacity-warning-5{
    background: #D89D4B0D;
}
.bg-opacity-info-10{
    background: #0093FA1A;
}
.bg-opacity-success-10{
    background: #00aa6d0D;
}
.danger-notes-bg{
    background: #FFE9E9;
}
.theme-clr{
    color: var(--theameColor);
}
.theme-border{
    border: 1px solid var(--theameColor);
}
.theme-hover:hover{
    background-color: var(--theameColor);
    color: #fff;
}
.success-border{
    border: 1px solid var(--success-clr);
}
.secondary-clr{
    color: var(--secondary-clr);
}
.hungar-export .js-hs-unfold-invoker.dropdown-toggle:not(:hover) {
    color: #fff !important;
}
.cus-warning-clr{
    color: var(--cus-warning-clr);
}
.cus-warning-light-clr{
    color: var(--cus-warning-light);
}
.bg-white-n{
    background-color: #fff;
}
/*View Details*/
.view-details {
    display: none;
}
.view-btn.active i{
    transform: rotate(180deg);
}
.shadow-md{
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.steps-integration-wrap .slide-cus__prev{
    inset-inline-start: -16px;
}
.steps-integration-wrap .slide-cus__next{
    inset-inline-end: -16px;
}
.steps-integration-wrap .slide-cus__prev button:hover,
.steps-integration-wrap .slide-cus__next button:hover{
    background: var(--primary) !important;
    color: #fff !important;
}
.step-integration-inner .min-w-220{
    width: 220px !important;
}

/*Owl Carousel Dots Style2*/
.dots-style2 .owl-dots{
    display: flex;
    justify-content: center;
    gap: 5px;
}
.dots-style2 .owl-dots .owl-dot{
    width: 6px;
    height: 6px;
    background: var(--secondary-clr);
    border-radius: 8.35714px;
    transition: all ease 0.3s;
}
.dots-style2 .owl-dots .owl-dot.active{
    background: var(--theameColor);
    width: 24px;
}

/*slide-counter style2*/
.slide-counter2{
    background: transparent;
    color: var(--title-clr);
    letter-spacing: 1px;
}

/*custom-accordion-style*/
.custom-accordion-style .accordion-button:not(.collapsed) i{
    transform: rotate(180deg);
}

/*Custom Offcanvas*/
.custom-offcanvas {
    position: fixed;
    top: 0;
    inset-inline-end: -480px; /* Hide offscreen initially */
    width: 480px;
    height: 100%;
    background-color: #fff;
    box-shadow: -4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    transition: all 0.3s ease-in-out;
    overflow-y: auto;
}
.custom-offcanvas.open {
    inset-inline-end: 0;
}
.offcanvas-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}
.offcanvas-overlay.show {
    display: block;
}
.btn-close {
    background: none;
    border: none;
    font-size: 1rem;
}

/*tax-report-vat*/
.tax-report-vat{
    height: 104px;
    overflow-y: scroll;
}

/*support-line*/
.support-line{
    border-left: 2px solid rgba(0, 0, 0, 0.10);
    height: 44px;
}
/*step-integration*/
.step-integration-inner{
    gap: 70px;
}
.step-integration-inner .integration-step{
    border: 0.75px dashed #A1A5AF;
    z-index: 1;
}
.step-integration-inner li:first-child .integration-step::before{
   display: none;
}
[dir="rtl"] .step-integration-inner li:first-child .integration-step::before{
    display: block;
}
[dir="rtl"] .step-integration-inner li:last-child .integration-step::before{
    display: none;
}
.step-integration-inner li .integration-step::before{
    position: absolute;
    width: 365px !important;
    right: 100%;
    top: 14px;
    content: "";
    border: 0.372px dashed rgba(0, 0, 0, 0.50);
    z-index: -1;
}
.scrollbar-w-0{
    scrollbar-width: none;
}
.white-nowrap{
    white-space: nowrap;
}

.end-10{
    inset-inline-end: 10px;
}

@media screen and (max-width: 1900px) {
    .step-integration-inner .min-w-370{
        min-width: initial;
    }
}
@media screen and (min-width: 1438px) and (max-width: 1900px) {
    .step-integration-inner .min-w-370{
        min-width: 210px;
    }
    .step-integration-inner li .integration-step::before{
        width: 245px !important;
        top: 14px;
    }
}
@media screen and (max-width: 1437px){
    .step-integration-inner li .integration-step::before{
        display: none;
    }
}

@media screen and (max-width: 1399px) {
    .p-24 {
        padding: 20px;
    }
    .p-30{
        padding: 20px;
    }
    .step-integration-inner{
        gap: 20px;
    }
    .step-integration-inner li .integration-step::before{
        /* width: 203px !important; */
        top: 14px;
    }
}
@media screen and (max-width: 1199px) {
    .tax-error__table.h-100-vh{
        height: initial;
    }
    .custom-offcanvas{
        width: 400px;
    }
}
@media screen and (max-width: 500px){
    .mobile-space-0{
        background: transparent !important;
        padding: 0 !important;
    }
    .custom-offcanvas{
        width: 320px;
    }
}
.disabled {
    opacity: .5;
    user-select: none;
    pointer-events: none;
    cursor: not-allowed;
}

/* Document upload */
.document-upload-wrapper {
    position: relative;
    height: 120px;
    max-width: 280px;
    background-color: #fff;
    color: #222324;
    border-radius: 5px;
    border: 1px dashed #E9E9EA;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
    cursor: pointer;
}
.document-upload-wrapper .document_input {
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
    inline-size: 100%;
    block-size: 100%;
    opacity: 0;
    cursor: pointer;
}
.document-upload-wrapper .textbox {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.single-document-uploaderwrap .pdf-single {
    position: relative;
    display: inline-block;
    max-width: 280px;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    cursor: pointer;
    flex-grow: 1;
}
.single-document-uploaderwrap .pdf-single .pdf-frame {
    overflow: hidden;
}
.single-document-uploaderwrap .pdf-single .pdf-thumbnail-alt,
.single-document-uploaderwrap .pdf-single .pdf-thumbnail {
    height: 120px;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: top;
    object-position: top;
    width: 100%;
    border-radius: 5px;
}
.single-document-uploaderwrap .pdf-single .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: center;
    border-radius: 5px;
    transition: opacity 0.3s ease;
}
.single-document-uploaderwrap .pdf-single .remove-btn {
    position: absolute;
    inset-block-start: -10px;
    inset-inline-end: -10px;
    background: #ff4949;
    color: #fff;
    block-size: 26px;
    inline-size: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100px;
    display: none !important;
}
.single-document-uploaderwrap .pdf-single .pdf-info {
    background-color: #fff;
    box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 14px 11px;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}
.single-document-uploaderwrap .pdf-single .file-name-wrapper {
    color: #222324;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    gap: 3px;
}
.single-document-uploaderwrap .pdf-single .file-name {
    display: inline-block;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
}

@media (max-width: 575px) {
    .document-upload-wrapper,
    .pdf-single {
        max-width: 220px;
    }
}

.select-class-closest {
    position: relative;
    /*z-index: 1;*/
}

.h225-vh{
    height: calc(100vh - 225px);
}
.cus-document-responsive{
    @media screen and (max-width: 1399px) {
        min-width: 235px !important;
        .address-info{
            font-size: 14px;
        }
    }
}
@media screen and (max-width: 1399px) {
    .address-info li {
        font-size: 13px !important;
    }
}

